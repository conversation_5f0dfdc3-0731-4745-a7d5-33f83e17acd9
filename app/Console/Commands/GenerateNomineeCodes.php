<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NomineeProfile;

class GenerateNomineeCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ussd:generate-nominee-codes 
                            {--force : Force regenerate codes for existing nominees}
                            {--dry-run : Show what would be changed without making changes}
                            {--backup : Create backup of current codes before updating}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate nominee codes with random digits for USSD voting system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Ensure storage directories exist
            $this->ensureStorageDirectories();
            
            $this->info('🚀 Generating nominee codes with random digits...');

            $force = $this->option('force');
            $dryRun = $this->option('dry-run');
            $backup = $this->option('backup');
            
            if ($force) {
                $nominees = NomineeProfile::all();
                $this->info('Processing all nominees (force mode)');
            } else {
                $nominees = NomineeProfile::whereNull('nominee_code')->get();
                $this->info('Processing nominees without codes');
            }

            if ($nominees->isEmpty()) {
                $this->info('✅ No nominees need code generation.');
                return 0;
            }

            // Show what will be processed
            $this->table(
                ['Count', 'Action'],
                [
                    [$nominees->count(), $force ? 'Regenerate all codes' : 'Generate missing codes'],
                    [$dryRun ? 'Yes' : 'No', 'Dry run mode'],
                    [$backup ? 'Yes' : 'No', 'Backup current codes'],
                ]
            );

            if (!$dryRun && !$this->confirm('Do you want to proceed with code generation?')) {
                $this->info('Operation cancelled.');
                return 0;
            }

            return $this->processNominees($nominees, $force, $dryRun, $backup);
            
        } catch (\Exception $e) {
            $this->error('❌ Command failed: ' . $e->getMessage());
            if ($this->getOutput()->isVerbose()) {
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * Process the nominees and generate codes.
     */
    private function processNominees($nominees, $force, $dryRun, $backup)
    {

        // Create backup if requested
        $backupData = [];
        if ($backup && !$dryRun) {
            $this->info('📋 Creating backup of current codes...');
            foreach ($nominees as $nominee) {
                if ($nominee->nominee_code) {
                    $backupData[] = [
                        'id' => $nominee->id,
                        'display_name' => $nominee->display_name,
                        'old_code' => $nominee->nominee_code,
                    ];
                }
            }
            
            try {
                // Ensure the backup directory exists
                $backupDir = storage_path('app/backups');
                if (!is_dir($backupDir)) {
                    mkdir($backupDir, 0755, true);
                }
                
                $backupFile = $backupDir . '/nominee_codes_backup_' . date('Y-m-d_H-i-s') . '.json';
                file_put_contents($backupFile, json_encode($backupData, JSON_PRETTY_PRINT));
                $this->info("📁 Backup saved to: {$backupFile}");
            } catch (\Exception $e) {
                $this->warn("⚠️  Could not create backup file: " . $e->getMessage());
                $this->warn("Continuing without backup...");
            }
        }

        $progressBar = $this->output->createProgressBar($nominees->count());
        $progressBar->start();

        $generated = 0;
        $errors = 0;
        $changes = [];

        foreach ($nominees as $nominee) {
            try {
                if ($force || empty($nominee->nominee_code)) {
                    $oldCode = $nominee->nominee_code;
                    $newCode = NomineeProfile::generateUniqueNomineeCode($nominee->display_name);
                    
                    $changes[] = [
                        'id' => $nominee->id,
                        'name' => $nominee->display_name,
                        'old_code' => $oldCode ?: 'NULL',
                        'new_code' => $newCode,
                    ];
                    
                    if (!$dryRun) {
                        $nominee->update(['nominee_code' => $newCode]);
                    }
                    $generated++;
                }
            } catch (\Exception $e) {
                $this->error("\nError generating code for nominee ID {$nominee->id}: " . $e->getMessage());
                $errors++;
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        
        $this->newLine(2);
        
        if ($dryRun) {
            $this->info("🔍 DRY RUN - No changes were made");
            $this->info("The following changes would be made:");
        } else {
            $this->info("✅ Code generation completed!");
            $this->info("The following changes were made:");
        }
        
        $this->info("Generated/Updated: {$generated} codes");
        
        if ($errors > 0) {
            $this->error("Errors: {$errors}");
        }

        // Show changes table
        if (!empty($changes)) {
            $this->newLine();
            $this->table(
                ['ID', 'Name', 'Old Code', 'New Code'],
                array_slice($changes, 0, 10) // Show first 10 changes
            );
            
            if (count($changes) > 10) {
                $this->info("... and " . (count($changes) - 10) . " more changes");
            }
        }

        // Show some examples from database
        if (!$dryRun) {
            $this->newLine();
            $this->info("📋 Sample nominee codes from database:");
            
            $sampleNominees = NomineeProfile::whereNotNull('nominee_code')
                ->limit(5)
                ->get(['display_name', 'nominee_code']);
                
            foreach ($sampleNominees as $nominee) {
                $this->line("  {$nominee->nominee_code} - {$nominee->display_name}");
            }
        }

        return 0;
    }

    /**
     * Ensure required storage directories exist.
     */
    private function ensureStorageDirectories()
    {
        $directories = [
            storage_path('logs'),
            storage_path('app'),
            storage_path('app/backups'),
            storage_path('framework'),
            storage_path('framework/cache'),
            storage_path('framework/sessions'),
            storage_path('framework/views'),
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                try {
                    mkdir($dir, 0755, true);
                } catch (\Exception $e) {
                    // Silently continue if we can't create directories
                    // The command will handle errors gracefully later
                }
            }
        }
    }
}
