<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class SetTelegramBotCommands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:set-commands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up Telegram bot commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $commands = [
                [
                    'command' => 'start',
                    'description' => 'Start voting and show main menu'
                ],
                [
                    'command' => 'categories',
                    'description' => 'Browse all award categories'
                ],
                [
                    'command' => 'search',
                    'description' => 'Search for nominees - Usage: /search [query]'
                ],
                [
                    'command' => 'vote',
                    'description' => 'Vote for a nominee - Usage: /vote [code] (e.g., /vote A1B2C)'
                ],
                [
                    'command' => 'status',
                    'description' => 'Check payment status - Usage: /status [payment_ref]'
                ],
                [
                    'command' => 'share',
                    'description' => 'Share nominee link - Usage: /share [code]'
                ],
                [
                    'command' => 'help',
                    'description' => 'Show help and available commands'
                ]
            ];

            $response = Telegram::setMyCommands([
                'commands' => $commands
            ]);

            if ($response) {
                $this->info('✅ Telegram bot commands set successfully!');
                
                foreach ($commands as $command) {
                    $this->line("  /{$command['command']} - {$command['description']}");
                }
                
                Log::info('Telegram bot commands configured', ['commands' => $commands]);
                return Command::SUCCESS;
            } else {
                $this->error('❌ Failed to set Telegram bot commands');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error setting Telegram bot commands: ' . $e->getMessage());
            Log::error('Failed to set Telegram bot commands', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }
}
