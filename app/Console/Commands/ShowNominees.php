<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NomineeProfile;

class ShowNominees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nominees:show';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show all nominees with their voting URLs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $nominees = NomineeProfile::with('awardCategories')
            ->approved()
            ->get();

        if ($nominees->isEmpty()) {
            $this->info('No approved nominees found.');
            return;
        }

        $this->info('Vote Your Fav Bot - Nominees:');
        $this->line('');

        foreach ($nominees as $nominee) {
            $category = $nominee->awardCategories->first();
            $categoryName = $category ? $category->name : 'No Category';
            
            $this->line("Name: {$nominee->display_name}");
            $this->line("Slug: {$nominee->slug} (Length: " . strlen($nominee->slug) . ")");
            $this->line("Category: {$categoryName}");
            $this->line("Votes: {$nominee->total_votes}");
            $this->line("Voting URL: " . route('vote.show', $nominee->slug));
            $this->line('---');
        }

        $this->info("Total nominees: {$nominees->count()}");
    }
}
