<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AwardCategory;
use App\Models\Organization;
use Illuminate\Support\Str;

class RegenerateSlugsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:regenerate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerates slugs for all award categories and organizations to ensure they are unique.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting slug regeneration...');

        $this->regenerateSlugsFor(AwardCategory::class);
        $this->regenerateSlugsFor(Organization::class);

        $this->info('All slugs have been regenerated successfully.');
        return 0;
    }

    private function regenerateSlugsFor($modelClass)
    {
        $this->line("Regenerating slugs for {$modelClass}...");
        $items = $modelClass::all();
        $bar = $this->output->createProgressBar(count($items));

        foreach ($items as $item) {
            $originalSlug = $item->slug;
            $baseSlug = Str::slug($item->name);
            $randomPart = strtolower(Str::random(5)) . '-' . strtolower(Str::random(5));
            $newSlug = $baseSlug . '-' . $randomPart;
            
            $item->slug = $newSlug;
            $item->save();

            $this->line("  Updated {$item->name}: {$originalSlug} -> {$newSlug}");
            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);
    }
}
