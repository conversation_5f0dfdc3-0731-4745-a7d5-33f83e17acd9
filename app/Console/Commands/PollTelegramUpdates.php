<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use App\Http\Controllers\TelegramBotController;
use App\Services\TelegramVotingService;
use App\Services\PaystackService;
use Illuminate\Http\Request;

class PollTelegramUpdates extends Command
{
    protected $signature = 'telegram:poll';
    protected $description = 'Poll for Telegram updates and process them (for local testing)';

    protected TelegramVotingService $telegramVotingService;
    protected PaystackService $paystackService;

    public function __construct(TelegramVotingService $telegramVotingService, PaystackService $paystackService)
    {
        parent::__construct();
        $this->telegramVotingService = $telegramVotingService;
        $this->paystackService = $paystackService;
    }    public function handle()
    {
        $this->info('📡 Polling Telegram for updates...');
        $this->newLine();

        try {
            // Get recent updates
            $updates = Telegram::getUpdates(['limit' => 10]);
            
            if (count($updates) > 0) {
                $this->info("📬 Found " . count($updates) . " updates:");
                $this->newLine();

                // Create controller with injected services
                $controller = new TelegramBotController(
                    $this->telegramVotingService,
                    $this->paystackService
                );

                foreach ($updates as $update) {
                    $this->processUpdate($update, $controller);
                }

                // Mark updates as processed
                $lastUpdateId = end($updates)->getUpdateId();
                Telegram::getUpdates(['offset' => $lastUpdateId + 1]);
                
                $this->newLine();
                $this->info('✅ All updates processed!');
            } else {
                $this->warn('📭 No new updates found.');
                $this->line('Send a message to @voteyourfav_bot and run this command again.');
            }

        } catch (\Exception $e) {
            $this->error('❌ Error polling updates: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }    private function processUpdate($update, $controller)
    {
        try {
            // Create a mock request with the update
            $request = new Request();
            $request->merge(['update' => $update->toArray()]);
            
            // Handle message updates
            if ($update->getMessage()) {
                $message = $update->getMessage();
                $user = $message->getFrom();
                $text = $message->getText();
                $chatId = $message->getChat()->getId();
                $date = date('Y-m-d H:i:s', $message->getDate());

                $this->line("📨 <fg=blue>Message from {$user->getFirstName()}</fg=blue>");
                $this->line("   Chat ID: {$chatId}");
                $this->line("   Text: <fg=green>{$text}</fg=green>");
                $this->line("   Date: {$date}");
                $this->line("   Processing...");
                
                $this->handleMessage($message, $controller);
                $this->line("   ✅ <fg=green>Processed successfully</fg=green>");
            }
            // Handle callback query updates
            elseif ($update->getCallbackQuery()) {
                $callbackQuery = $update->getCallbackQuery();
                $user = $callbackQuery->getFrom();
                $data = $callbackQuery->getData();
                $chatId = $callbackQuery->getMessage()->getChat()->getId();

                $this->line("🔘 <fg=yellow>Callback from {$user->getFirstName()}</fg=yellow>");
                $this->line("   Chat ID: {$chatId}");
                $this->line("   Data: <fg=cyan>{$data}</fg=cyan>");
                $this->line("   Processing...");
                
                $this->handleCallbackQuery($callbackQuery, $controller);
                $this->line("   ✅ <fg=green>Processed successfully</fg=green>");
            }
            
        } catch (\Exception $e) {
            $this->line("   ❌ <fg=red>Error: {$e->getMessage()}</fg=red>");
        }
        
        $this->newLine();
    }    private function handleMessage($message, $controller)
    {
        $chatId = $message->getChat()->getId();
        $text = $message->getText();
        $userId = $message->getFrom()->getId();

        // Use public testing methods instead of reflection
        $controller->processMessageForTesting($message);
    }

    private function handleCallbackQuery($callbackQuery, $controller)
    {
        // Use public testing method instead of reflection
        $controller->processCallbackQueryForTesting($callbackQuery);
    }
}
