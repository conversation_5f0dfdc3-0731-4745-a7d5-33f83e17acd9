<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class SetupTelegramBot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:setup {--force : Force setup even if webhook is already configured}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Complete Telegram bot setup - webhook, commands, and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🤖 Setting up Vote Your Fav Bot...');
        $this->newLine();

        // Step 1: Check bot connection
        if (!$this->checkBotConnection()) {
            return Command::FAILURE;
        }

        // Step 2: Set up webhook
        if (!$this->setupWebhook()) {
            return Command::FAILURE;
        }

        // Step 3: Set up bot commands
        if (!$this->setupCommands()) {
            return Command::FAILURE;
        }

        // Step 4: Test the setup
        $this->testSetup();

        $this->newLine();
        $this->info('✅ Telegram bot setup completed successfully!');
        $this->newLine();
        
        $this->displayBotInfo();
        
        return Command::SUCCESS;
    }

    private function checkBotConnection(): bool
    {
        $this->line('🔍 Checking bot connection...');
        
        try {
            $me = Telegram::getMe();
            $this->info("✅ Connected to bot: @{$me->getUsername()} ({$me->getFirstName()})");
            return true;
        } catch (\Exception $e) {
            $this->error('❌ Failed to connect to Telegram bot');
            $this->error('Error: ' . $e->getMessage());
            $this->newLine();
            $this->warn('Please check your TELEGRAM_BOT_ACCESS_TOKEN in .env file');
            return false;
        }
    }

    private function setupWebhook(): bool
    {
        $this->line('🔗 Setting up webhook...');
        
        try {
            // Check current webhook
            $webhookInfo = Telegram::getWebhookInfo();
            $webhookUrl = route('telegram.webhook');
            
            if ($webhookInfo->getUrl() === $webhookUrl && !$this->option('force')) {
                $this->info("✅ Webhook already configured: {$webhookUrl}");
                return true;
            }

            // Set new webhook
            $response = Telegram::setWebhook([
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query'],
                'drop_pending_updates' => true
            ]);

            if ($response) {
                $this->info("✅ Webhook set successfully: {$webhookUrl}");
                Log::info('Telegram webhook configured', ['url' => $webhookUrl]);
                return true;
            } else {
                $this->error('❌ Failed to set webhook');
                return false;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error setting webhook: ' . $e->getMessage());
            return false;
        }
    }

    private function setupCommands(): bool
    {
        $this->line('📝 Setting up bot commands...');
        
        try {
            $commands = [
                [
                    'command' => 'start',
                    'description' => 'Start voting and show main menu'
                ],
                [
                    'command' => 'categories',
                    'description' => 'Browse all award categories'
                ],
                [
                    'command' => 'search',
                    'description' => 'Search for nominees'
                ],
                [
                    'command' => 'vote',
                    'description' => 'Vote for a nominee'
                ],
                [
                    'command' => 'status',
                    'description' => 'Check payment status'
                ],
                [
                    'command' => 'share',
                    'description' => 'Share nominee link'
                ],
                [
                    'command' => 'help',
                    'description' => 'Show help and available commands'
                ]
            ];

            $response = Telegram::setMyCommands(['commands' => $commands]);

            if ($response) {
                $this->info('✅ Bot commands configured successfully');
                return true;
            } else {
                $this->error('❌ Failed to set bot commands');
                return false;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error setting commands: ' . $e->getMessage());
            return false;
        }
    }

    private function testSetup(): void
    {
        $this->line('🧪 Testing setup...');
        
        try {
            $webhookInfo = Telegram::getWebhookInfo();
            
            $this->table(
                ['Property', 'Value'],
                [
                    ['Webhook URL', $webhookInfo->getUrl() ?: 'Not set'],
                    ['Pending Updates', $webhookInfo->getPendingUpdateCount()],
                    ['Last Error Date', $webhookInfo->getLastErrorDate() ? date('Y-m-d H:i:s', $webhookInfo->getLastErrorDate()) : 'None'],
                    ['Max Connections', $webhookInfo->getMaxConnections()],
                ]
            );

            if ($webhookInfo->getLastErrorMessage()) {
                $this->warn('Last webhook error: ' . $webhookInfo->getLastErrorMessage());
            }

        } catch (\Exception $e) {
            $this->warn('Could not retrieve webhook info: ' . $e->getMessage());
        }
    }

    private function displayBotInfo(): void
    {
        try {
            $me = Telegram::getMe();
            $webhookInfo = Telegram::getWebhookInfo();

            $this->line('🤖 <options=bold>Bot Information:</>');
            $this->line("   Name: {$me->getFirstName()}");
            $this->line("   Username: @{$me->getUsername()}");
            $this->line("   ID: {$me->getId()}");
            $this->newLine();
            
            $this->line('🔗 <options=bold>Webhook Information:</>');
            $this->line("   URL: {$webhookInfo->getUrl()}");
            $this->line("   Status: " . ($webhookInfo->getUrl() ? '✅ Active' : '❌ Not configured'));
            $this->newLine();
            
            $this->line('📱 <options=bold>Next Steps:</>');
            $this->line('   1. Test your bot by sending /start to @' . $me->getUsername());
            $this->line('   2. Check bot status: php artisan telegram:info');
            $this->line('   3. Monitor logs for any issues');
            
        } catch (\Exception $e) {
            $this->warn('Could not display bot info: ' . $e->getMessage());
        }
    }
}
