<?php

namespace App\Console\Commands;

use App\Models\NomineeProfile;
use App\Models\AwardCategory;
use App\Services\SmsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotifyHighPerformingNominees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nominees:notify-high-performers 
                            {--min-votes=100 : Minimum votes required to receive notification}
                            {--dry-run : Preview messages without sending}
                            {--category= : Specific category slug to filter by}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send SMS notifications to nominees with high vote counts about their current standings';

    private ?SmsService $smsService = null;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Initialize SMS service
        try {
            $this->smsService = new SmsService();
        } catch (\Exception $e) {
            $this->error("❌ SMS service not configured properly: " . $e->getMessage());
            $this->warn("💡 Please set MNOTIFY_API_KEY in your .env file");
            return Command::FAILURE;
        }

        $minVotes = (int) $this->option('min-votes');
        $isDryRun = $this->option('dry-run');
        $categorySlug = $this->option('category');

        $this->info("🚀 Starting high performer notifications...");
        $this->info("📊 Minimum votes: {$minVotes}");

        if ($isDryRun) {
            $this->warn("🔍 DRY RUN MODE - No messages will be sent");
        }

        if ($categorySlug) {
            $this->info("🎯 Filtering by category: {$categorySlug}");
        }

        // Get high-performing nominees
        $nominees = $this->getHighPerformingNominees($minVotes, $categorySlug);

        if ($nominees->isEmpty()) {
            $this->warn("❌ No nominees found with {$minVotes}+ votes");
            return Command::SUCCESS;
        }

        $this->info("📱 Found {$nominees->count()} nominees to notify");

        $successCount = 0;
        $failureCount = 0;
        $skippedCount = 0;

        foreach ($nominees as $nominee) {
            $result = $this->processNominee($nominee, $isDryRun);
            
            switch ($result['status']) {
                case 'success':
                    $successCount++;
                    break;
                case 'failed':
                    $failureCount++;
                    break;
                case 'skipped':
                    $skippedCount++;
                    break;
            }

            // Add small delay to avoid rate limiting
            if (!$isDryRun) {
                usleep(500000); // 0.5 seconds
            }
        }

        $this->displaySummary($successCount, $failureCount, $skippedCount, $isDryRun);

        return Command::SUCCESS;
    }

    /**
     * Get nominees with high vote counts
     */
    private function getHighPerformingNominees(int $minVotes, ?string $categorySlug)
    {
        $query = NomineeProfile::with(['user', 'awardCategories.organization'])
            ->where('total_votes', '>=', $minVotes)
            ->where('is_approved', true)
            ->where('is_suspended', false);

        // Filter by specific category if provided
        if ($categorySlug) {
            $query->whereHas('awardCategories', function ($q) use ($categorySlug) {
                $q->where('slug', $categorySlug)
                  ->where('is_active', true)
                  ->where('is_suspended', false);
            });
        }

        return $query->orderBy('total_votes', 'desc')->get();
    }

    /**
     * Process individual nominee notification
     */
    private function processNominee(NomineeProfile $nominee, bool $isDryRun): array
    {
        // Get phone number
        $phoneNumber = $this->getPhoneNumber($nominee);
        
        if (!$phoneNumber) {
            $this->warn("⚠️  Skipping {$nominee->display_name} - No phone number");
            return ['status' => 'skipped', 'reason' => 'No phone number'];
        }

        // Calculate rankings for each category
        $rankings = $this->calculateRankings($nominee);
        
        if (empty($rankings)) {
            $this->warn("⚠️  Skipping {$nominee->display_name} - No active categories");
            return ['status' => 'skipped', 'reason' => 'No active categories'];
        }

        // Generate message
        $message = $this->generateMessage($nominee, $rankings);

        if ($isDryRun) {
            $this->displayPreview($nominee, $phoneNumber, $message);
            return ['status' => 'success', 'reason' => 'Dry run preview'];
        }

        // Send SMS
        $sent = $this->smsService->sendSms($phoneNumber, $message);

        if ($sent) {
            $this->info("✅ Sent to {$nominee->display_name} ({$phoneNumber})");
            Log::info('High performer SMS sent', [
                'nominee_id' => $nominee->id,
                'nominee_name' => $nominee->display_name,
                'phone' => $phoneNumber,
                'total_votes' => $nominee->total_votes
            ]);
            return ['status' => 'success'];
        } else {
            $this->error("❌ Failed to send to {$nominee->display_name} ({$phoneNumber})");
            return ['status' => 'failed', 'reason' => 'SMS sending failed'];
        }
    }

    /**
     * Get phone number from nominee's user account
     */
    private function getPhoneNumber(NomineeProfile $nominee): ?string
    {
        if (!$nominee->user) {
            return null;
        }

        return $nominee->user->phone_number ?? $nominee->user->phone;
    }

    /**
     * Calculate rankings for nominee in each of their categories
     */
    private function calculateRankings(NomineeProfile $nominee): array
    {
        $rankings = [];

        foreach ($nominee->awardCategories as $category) {
            // Skip inactive or suspended categories
            if (!$category->is_active || $category->is_suspended) {
                continue;
            }

            // Get all nominees in this category ordered by votes
            $categoryNominees = $category->nomineeProfiles()
                ->approved()
                ->orderBy('total_votes', 'desc')
                ->get();

            // Find current nominee's position
            $position = 1;
            foreach ($categoryNominees as $index => $categoryNominee) {
                if ($categoryNominee->id === $nominee->id) {
                    $position = $index + 1;
                    break;
                }
            }

            $rankings[] = [
                'category_name' => $category->name,
                'position' => $position,
                'total_nominees' => $categoryNominees->count(),
                'is_first' => $position === 1,
                'organization_name' => $category->organization->name ?? 'N/A'
            ];
        }

        return $rankings;
    }

    /**
     * Generate SMS message for nominee
     */
    private function generateMessage(NomineeProfile $nominee, array $rankings): string
    {
        $message = "Hi {$nominee->display_name}! ";
        $message .= "You have {$nominee->total_votes} votes. ";

        // Check if they're first in any category
        $firstPlaceCategories = array_filter($rankings, fn($r) => $r['is_first']);

        if (!empty($firstPlaceCategories)) {
            $message .= "You're leading in: ";
            $categoryNames = array_map(fn($r) => $r['category_name'], $firstPlaceCategories);
            $message .= implode(', ', array_slice($categoryNames, 0, 2)); // Limit to 2 categories for SMS length

            if (count($categoryNames) > 2) {
                $message .= " and " . (count($categoryNames) - 2) . " more";
            }
            $message .= "! ";
        } else {
            // Show their best position
            $bestPosition = min(array_column($rankings, 'position'));
            $bestCategory = collect($rankings)->firstWhere('position', $bestPosition);

            $message .= "You're #{$bestPosition} in {$bestCategory['category_name']}. ";
        }

        $message .= "Keep encouraging supporters to vote! ";
        $message .= "Vote at: " . route('vote.show', $nominee->slug);

        return $message;
    }

    /**
     * Display preview for dry run
     */
    private function displayPreview(NomineeProfile $nominee, string $phoneNumber, string $message): void
    {
        $this->line("📱 Preview for {$nominee->display_name}:");
        $this->line("   Phone: {$phoneNumber}");
        $this->line("   Votes: {$nominee->total_votes}");
        $this->line("   Message: {$message}");
        $this->line("   Length: " . strlen($message) . " characters");
        $this->line("");
    }

    /**
     * Display final summary
     */
    private function displaySummary(int $success, int $failed, int $skipped, bool $isDryRun): void
    {
        $this->line("");
        $this->info("📊 Summary:");

        if ($isDryRun) {
            $this->info("   Previewed: {$success}");
        } else {
            $this->info("   Sent: {$success}");
        }

        $this->info("   Failed: {$failed}");
        $this->info("   Skipped: {$skipped}");
        $this->info("   Total: " . ($success + $failed + $skipped));

        if (!$isDryRun && $success > 0) {
            $this->info("✅ Notifications completed successfully!");
        }
    }
}
