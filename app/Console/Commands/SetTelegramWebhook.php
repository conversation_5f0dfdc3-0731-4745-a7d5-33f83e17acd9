<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;

class SetTelegramWebhook extends Command
{
    protected $signature = 'telegram:webhook {url? : The webhook URL (optional)}';
    protected $description = 'Set up Telegram webhook with custom URL';

    public function handle()
    {
        $url = $this->argument('url');
        
        if (!$url) {
            $url = $this->ask('Enter your ngrok HTTPS URL (e.g., https://abc123.ngrok.io)');
        }
        
        if (!$url) {
            $this->error('❌ URL is required');
            return 1;
        }
        
        // Ensure URL has https and ends with webhook path
        if (!str_starts_with($url, 'https://')) {
            $this->error('❌ URL must start with https://');
            return 1;
        }
        
        $webhookUrl = rtrim($url, '/') . '/telegram/webhook';
        
        $this->info("🔗 Setting webhook to: {$webhookUrl}");
        
        try {
            $response = Telegram::setWebhook([
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query']
            ]);
            
            if ($response) {
                $this->info('✅ Webhook set successfully!');
                
                // Check webhook info
                $webhookInfo = Telegram::getWebhookInfo();
                $this->newLine();
                $this->info('📋 Webhook Information:');
                $this->line("URL: {$webhookInfo->getUrl()}");
                $this->line("Pending Updates: {$webhookInfo->getPendingUpdateCount()}");
                
                $this->newLine();
                $this->info('🎉 Your bot should now respond to messages!');
                $this->line('Try sending a message to @voteyourfav_bot');
                
            } else {
                $this->error('❌ Failed to set webhook');
                return 1;
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
