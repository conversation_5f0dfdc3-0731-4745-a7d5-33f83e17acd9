<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\NomineeProfile;
use App\Models\AwardCategory;
use Illuminate\Support\Facades\Storage;

class ExportOrganizationNominees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:organization-nominees 
                            {--org-id= : Organization ID to export}
                            {--output-dir=exports : Directory to save the export file}
                            {--format=csv : Export format (csv or json)}
                            {--base-url= : Base URL for profile links (e.g., https://voteyourfav.com)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export nominees, their codes and categories for a specific organization to CSV or JSON file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('🚀 Starting organization nominees export...');

            // Get organization ID
            $orgId = $this->option('org-id');
            
            if (!$orgId) {
                $orgId = $this->askForOrganizationId();
            }

            if (!$orgId) {
                $this->error('❌ Organization ID is required.');
                return 1;
            }

            // Find the organization
            $organization = Organization::find($orgId);
            
            if (!$organization) {
                $this->error("❌ Organization with ID {$orgId} not found.");
                return 1;
            }

            $this->info("📋 Found organization: {$organization->name}");

            // Get nominees for this organization
            $nominees = $this->getOrganizationNominees($organization);

            if ($nominees->isEmpty()) {
                $this->warn("⚠️  No nominees found for organization: {$organization->name}");
                
                // Check if there are nominees with missing users
                $orphanedNominees = \App\Models\NomineeProfile::whereHas('awardCategories', function ($query) use ($organization) {
                    $query->where('organization_id', $organization->id);
                })->whereDoesntHave('user')->count();
                
                if ($orphanedNominees > 0) {
                    $this->warn("⚠️  Found {$orphanedNominees} nominee(s) with missing user records - data integrity issue detected");
                }
                
                return 0;
            }

            // Get format and base URL
            $format = $this->option('format') ?: 'csv';
            $baseUrl = $this->option('base-url') ?: $this->ask('Enter the base URL (e.g., https://voteyourfav.com)');

            if (!$baseUrl) {
                $this->error('❌ Base URL is required for generating profile links.');
                return 1;
            }

            // Generate export data
            if ($format === 'csv') {
                $filename = $this->saveAsCsv($organization, $nominees, $baseUrl);
            } else {
                $exportData = $this->generateExportData($organization, $nominees, $baseUrl);
                $filename = $this->saveAsJson($exportData, $organization);
            }

            $this->info("✅ Export completed successfully!");
            $this->info("📁 File saved: {$filename}");
            
            if ($format === 'csv') {
                // For CSV, calculate actual exported count (excluding header)
                $exportedCount = count(explode("\n", trim(file_get_contents($filename)))) - 1;
                $this->info("📊 Total nominees exported: {$exportedCount}");
            } else {
                $this->info("📊 Total nominees exported: {$nominees->count()}");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Export failed: ' . $e->getMessage());
            if ($this->getOutput()->isVerbose()) {
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * Ask for organization ID interactively.
     */
    private function askForOrganizationId(): ?int
    {
        // Show available organizations
        $organizations = Organization::select('id', 'name', 'is_active')
            ->orderBy('name')
            ->get();

        if ($organizations->isEmpty()) {
            $this->error('❌ No organizations found in the database.');
            return null;
        }

        $this->info('📋 Available organizations:');
        $this->table(
            ['ID', 'Name', 'Status'],
            $organizations->map(function ($org) {
                return [
                    $org->id,
                    $org->name,
                    $org->is_active ? 'Active' : 'Inactive'
                ];
            })->toArray()
        );

        $orgId = $this->ask('Please enter the Organization ID');

        return is_numeric($orgId) ? (int) $orgId : null;
    }

    /**
     * Get nominees for the organization with their categories.
     */
    private function getOrganizationNominees(Organization $organization)
    {
        // Get all nominees that belong to categories of this organization
        return NomineeProfile::whereHas('awardCategories', function ($query) use ($organization) {
            $query->where('organization_id', $organization->id);
        })
        ->whereHas('user') // Ensure user exists
        ->with([
            'user:id,name,email,phone',
            'awardCategories' => function ($query) use ($organization) {
                $query->where('organization_id', $organization->id)
                      ->select('award_categories.id', 'award_categories.name', 'award_categories.slug', 'award_categories.organization_id');
            }
        ])
        ->get();
    }

    /**
     * Generate the export data structure.
     */
    private function generateExportData(Organization $organization, $nominees, string $baseUrl)
    {
        $exportData = [
            'export_info' => [
                'generated_at' => now()->toISOString(),
                'generated_by' => 'artisan export:organization-nominees',
                'total_nominees' => $nominees->count(),
            ],
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'slug' => $organization->slug,
                'description' => $organization->description,
                'is_active' => $organization->is_active,
            ],
            'nominees' => [],
        ];

        foreach ($nominees as $nominee) {
            // Skip nominees without users (data integrity issue)
            if (!$nominee->user) {
                continue;
            }

            $profileImageUrl = '';
            if ($nominee->profile_image) {
                $profileImageUrl = rtrim($baseUrl, '/') . '/storage/' . $nominee->profile_image;
            }
            
            $nomineeData = [
                'id' => $nominee->id,
                'nominee_code' => $nominee->nominee_code ?: '',
                'slug' => $nominee->slug ?: '',
                'display_name' => $nominee->display_name ?: 'Unknown',
                'bio' => $nominee->bio ?: '',
                'profile_image_url' => $profileImageUrl,
                'total_votes' => $nominee->total_votes ?: 0,
                'is_approved' => $nominee->is_approved,
                'is_suspended' => $nominee->is_suspended,
                'profile_url' => rtrim($baseUrl, '/') . '/vote/' . ($nominee->slug ?: 'unknown'),
                'user' => [
                    'id' => $nominee->user->id ?: '',
                    'name' => $nominee->user->name ?: '',
                    'email' => $nominee->user->email ?: '',
                    'phone' => $nominee->user->phone ?: '',
                ],
                'categories' => [],
            ];

            // Add categories for this nominee
            foreach ($nominee->awardCategories as $category) {
                $nomineeData['categories'][] = [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                ];
            }

            $exportData['nominees'][] = $nomineeData;
        }

        return $exportData;
    }

    /**
     * Save the export data to a CSV file.
     */
    private function saveAsCsv(Organization $organization, $nominees, string $baseUrl): string
    {
        $outputDir = $this->option('output-dir');
        
        // Ensure the directory exists in the public disk
        $disk = Storage::disk('public');
        if (!$disk->exists($outputDir)) {
            $disk->makeDirectory($outputDir);
        }

        // Generate filename
        $timestamp = now()->format('Y-m-d_H-i-s');
        $orgSlug = \Illuminate\Support\Str::slug($organization->name);
        $filename = "{$outputDir}/nominees_export_{$orgSlug}_{$timestamp}.csv";

        // Prepare CSV data
        $csvData = [];
        
                 // CSV Headers
         $csvData[] = [
             'ID',
             'Nominee Code',
             'Slug',
             'Display Name',
             'Bio',
             'Profile Picture URL',
             'Total Votes',
             'Is Approved',
             'Is Suspended',
             'Profile URL',
             'User ID',
             'User Name',
             'User Email',
             'User Phone',
             'Categories (Names)',
             'Categories (IDs)',
             'Categories (Slugs)'
         ];

                // Add nominee data
        $skippedCount = 0;
        foreach ($nominees as $nominee) {
            // Skip nominees without users (data integrity issue)
            if (!$nominee->user) {
                $skippedCount++;
                continue;
            }

            $categoryNames = $nominee->awardCategories->pluck('name')->join('; ');
            $categoryIds = $nominee->awardCategories->pluck('id')->join('; ');
            $categorySlugs = $nominee->awardCategories->pluck('slug')->join('; ');
            
            $profileImageUrl = '';
            if ($nominee->profile_image) {
                $profileImageUrl = rtrim($baseUrl, '/') . '/storage/' . $nominee->profile_image;
            }
            
            $csvData[] = [
                $nominee->id,
                $nominee->nominee_code ?: '',
                $nominee->slug ?: '',
                $nominee->display_name ?: 'Unknown',
                $nominee->bio ?: '',
                $profileImageUrl,
                $nominee->total_votes ?: 0,
                $nominee->is_approved ? 'Yes' : 'No',
                $nominee->is_suspended ? 'Yes' : 'No',
                rtrim($baseUrl, '/') . '/vote/' . ($nominee->slug ?: 'unknown'),
                $nominee->user->id ?: '',
                $nominee->user->name ?: '',
                $nominee->user->email ?: '',
                $nominee->user->phone ?: '',
                $categoryNames,
                $categoryIds,
                $categorySlugs
            ];
        }

        // Convert to CSV string
        $csvContent = '';
        foreach ($csvData as $row) {
            $csvContent .= '"' . implode('","', array_map(function($field) {
                return str_replace('"', '""', $field);
            }, $row)) . '"' . "\n";
        }

        // Save the file using the public disk
        $disk->put($filename, $csvContent);

        // Report skipped nominees if any
        if ($skippedCount > 0) {
            $this->warn("⚠️  Skipped {$skippedCount} nominee(s) due to missing user records");
        }

        return storage_path("app/public/{$filename}");
    }

    /**
     * Save the export data to a JSON file.
     */
    private function saveAsJson(array $exportData, Organization $organization): string
    {
        $outputDir = $this->option('output-dir');
        
        // Ensure the directory exists in the public disk
        $disk = Storage::disk('public');
        if (!$disk->exists($outputDir)) {
            $disk->makeDirectory($outputDir);
        }

        // Generate filename
        $timestamp = now()->format('Y-m-d_H-i-s');
        $orgSlug = \Illuminate\Support\Str::slug($organization->name);
        $filename = "{$outputDir}/nominees_export_{$orgSlug}_{$timestamp}.json";

        // Save the file using the public disk
        $disk->put($filename, json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return storage_path("app/public/{$filename}");
    }
} 