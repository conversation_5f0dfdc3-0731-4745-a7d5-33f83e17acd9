<?php

namespace App\Console\Commands;

use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use League\Csv\Reader;

class SeedNomineesFromCsv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-nominees-from-csv {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seeds users and nominee profiles from the UENR CSS AWARDS NOMINATION FORM.csv file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to seed nominees from CSV...');
        $dryRun = $this->option('dry-run');

        $csvPath = base_path('UENR CSS AWARDS NOMINATION FORM.csv');
        if (!file_exists($csvPath)) {
            $this->error('CSV file not found at ' . $csvPath);
            return 1;
        }

        $csv = Reader::createFromPath($csvPath, 'r');
        $csv->setHeaderOffset(0);
        $records = $csv->getRecords();

        $logChannel = Log::build([
            'driver' => 'single',
            'path' => storage_path('logs/nominee_credentials.log'),
            'level' => 'info',
        ]);

        foreach ($records as $record) {
            $email = trim($record['Email']);
            $fullName = trim($record['Full Name']);
            $categoryName = trim($record['Award Category ']);
            $phoneNumber = trim($record['Phone Number']);
            $profileImage = trim($record['Profile Image']);

            if (empty($email) || empty($fullName) || empty($categoryName)) {
                $this->warn("Skipping record due to missing data: " . json_encode($record));
                continue;
            }

            $this->info("Processing: {$fullName} <{$email}> for category '{$categoryName}'");

            if ($dryRun) {
                $this->line("  [DRY RUN] Would process user {$email}.");
                $user = User::firstOrNew(['email' => $email]);
                if (!$user->exists) {
                    $this->line("  [DRY RUN]   User does not exist. Would create with default password.");
                }
            } else {
                $user = User::firstOrNew(['email' => $email]);
                if (!$user->exists) {
                    $password = Str::password(8);
                    $user->name = $fullName;
                    $user->password = Hash::make($password);
                    $user->role = 'nominee';
                    $user->save();
                    $this->info("  Created new user: {$email}");
                    $logChannel->info("User created: email={$email}, password={$password}");
                } else {
                    $this->info("  User found: {$email}");
                }
            }

            $category = AwardCategory::firstOrCreate(
                ['name' => $categoryName],
                ['organization_id' => 1] // Defaulting to organization 1 as per requirement
            );

            if ($dryRun) {
                $this->line("  [DRY RUN] Would create nominee profile for '{$fullName}' in category '{$categoryName}'.");
            } else {
                NomineeProfile::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'award_category_id' => $category->id,
                    ],
                    [
                        'bio' => $fullName,
                        'contact' => $phoneNumber,
                        'profile_image_path' => $profileImage,
                        'status' => 'pending', // Nominees should be approved by an admin
                    ]
                );
                $this->info("  Created/updated nominee profile for '{$fullName}' in category '{$categoryName}'.");
            }
        }

        $this->info('Finished seeding nominees from CSV.');
        if (!$dryRun) {
            $this->info('Nominee credentials for new users have been logged to storage/logs/nominee_credentials.log');
        }
        return 0;
    }
}
