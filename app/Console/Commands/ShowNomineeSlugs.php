<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NomineeProfile;

class ShowNomineeSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'show:nominee-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show sample nominee slugs for Telegram bot testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📋 Sample Nominee Slugs for Telegram Bot:');
        $this->newLine();
        
        $nominees = NomineeProfile::with('awardCategories')->take(10)->get();
        
        if ($nominees->count() > 0) {
            $this->line('Use these commands in Telegram (both formats work):');
            $this->newLine();
            
            foreach ($nominees as $nominee) {
                $category = $nominee->awardCategories->first();
                $categoryName = $category ? $category->name : 'No Category';
                
                $this->line("• <fg=green>/vote {$nominee->slug}</fg=green> OR <fg=yellow>/vote {$nominee->nominee_code}</fg=yellow>");
                $this->line("  └─ {$nominee->display_name} ({$categoryName})");
                $this->newLine();
            }
            
            $this->info('💡 How to use:');
            $this->line('1. Copy any of the green OR yellow commands above');
            $this->line('2. Send it to @voteyourfav_bot in Telegram');
            $this->line('3. The bot will show voting options for that nominee');
            $this->line('4. Both 5-character slugs and nominee codes work!');
        } else {
            $this->warn('No nominees found in the database.');
        }

        $this->newLine();
        $this->info('🔧 Testing New Slug Generation Format:');
        
        // Generate 3 test slugs to show the format
        for ($i = 1; $i <= 3; $i++) {
            $slug = NomineeProfile::generateUniqueSlug();
            $this->line("  Example #{$i}: <fg=green>/vote {$slug}</fg=green> (Length: " . strlen($slug) . ")");
        }
        
        $this->newLine();
        $this->info('✨ Benefits of dual code support:');
        $this->line('• Short 5-character slugs (perfect for quick typing)');
        $this->line('• Descriptive nominee codes (easy to remember)');
        $this->line('• Works across all platforms (Web, Telegram, USSD)');
        $this->line('• 60+ million possible slug combinations');
        $this->line('• User-friendly nominee codes based on display names');
    }
}
