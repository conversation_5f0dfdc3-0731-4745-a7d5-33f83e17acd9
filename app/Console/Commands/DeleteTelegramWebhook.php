<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class DeleteTelegramWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:delete-webhook {--drop-pending : Drop pending updates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete the Telegram webhook';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🗑️  Deleting Telegram webhook...');
        $this->newLine();

        try {
            // Check current webhook status
            $webhookInfo = Telegram::getWebhookInfo();
            
            if (!$webhookInfo->getUrl()) {
                $this->warn('⚠️  No webhook is currently configured');
                return Command::SUCCESS;
            }

            $this->line("Current webhook: {$webhookInfo->getUrl()}");
            $this->line("Pending updates: {$webhookInfo->getPendingUpdateCount()}");
            $this->newLine();

            // Confirm deletion
            if (!$this->confirm('Are you sure you want to delete the webhook?', true)) {
                $this->info('Operation cancelled');
                return Command::SUCCESS;
            }

            // Delete webhook
            $result = Telegram::deleteWebhook([
                'drop_pending_updates' => $this->option('drop-pending')
            ]);

            if ($result) {
                $this->info('✅ Webhook deleted successfully');
                
                if ($this->option('drop-pending')) {
                    $this->line('📭 Pending updates have been dropped');
                }

                Log::info('Telegram webhook deleted via command');
                
                $this->newLine();
                $this->line('📋 Next steps:');
                $this->line('• You can now use polling methods like "php artisan telegram:test"');
                $this->line('• To restore webhook: php artisan telegram:setup');
                $this->line('• To set custom webhook: php artisan telegram:webhook <url>');
                
            } else {
                $this->error('❌ Failed to delete webhook');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            Log::error('Failed to delete Telegram webhook: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
} 