<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use App\Models\Payment;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;

class TelegramBotInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display Telegram bot information and statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🤖 Telegram Bot Information');
        $this->newLine();

        try {
            // Bot basic info
            $me = Telegram::getMe();
            $this->displayBotInfo($me);

            // Webhook info
            $this->displayWebhookInfo();

            // Commands info
            $this->displayCommandsInfo();

            // Usage statistics
            $this->displayUsageStats();

        } catch (\Exception $e) {
            $this->error('❌ Error retrieving bot information: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function displayBotInfo($me): void
    {
        $this->line('<options=bold>🤖 Bot Details:</>');
        $this->table(
            ['Property', 'Value'],
            [
                ['Name', $me->getFirstName()],
                ['Username', '@' . $me->getUsername()],
                ['ID', $me->getId()],
                ['Can Join Groups', $me->getCanJoinGroups() ? '✅ Yes' : '❌ No'],
                ['Can Read All Group Messages', $me->getCanReadAllGroupMessages() ? '✅ Yes' : '❌ No'],
                ['Supports Inline Queries', $me->getSupportsInlineQueries() ? '✅ Yes' : '❌ No'],
            ]
        );
        $this->newLine();
    }

    private function displayWebhookInfo(): void
    {
        try {
            $webhookInfo = Telegram::getWebhookInfo();
            
            $this->line('<options=bold>🔗 Webhook Status:</>');
            $this->table(
                ['Property', 'Value'],
                [
                    ['URL', $webhookInfo->getUrl() ?: '❌ Not configured'],
                    ['Status', $webhookInfo->getUrl() ? '✅ Active' : '❌ Inactive'],
                    ['Pending Updates', $webhookInfo->getPendingUpdateCount()],
                    ['Max Connections', $webhookInfo->getMaxConnections()],
                    ['Allowed Updates', is_array($webhookInfo->getAllowedUpdates()) ? 
                        implode(', ', $webhookInfo->getAllowedUpdates()) : 'All'],
                    ['Last Error Date', $webhookInfo->getLastErrorDate() ? 
                        date('Y-m-d H:i:s', $webhookInfo->getLastErrorDate()) : 'None'],
                    ['Last Error Message', $webhookInfo->getLastErrorMessage() ?: 'None'],
                ]
            );
            $this->newLine();

        } catch (\Exception $e) {
            $this->warn('Could not retrieve webhook information: ' . $e->getMessage());
        }
    }

    private function displayCommandsInfo(): void
    {
        try {
            $commands = Telegram::getMyCommands();
            
            $this->line('<options=bold>📝 Configured Commands:</>');
            
            if (empty($commands)) {
                $this->warn('No commands configured. Run: php artisan telegram:set-commands');
            } else {
                $commandsTable = [];
                foreach ($commands as $command) {
                    $commandsTable[] = [
                        '/' . $command->getCommand(),
                        $command->getDescription()
                    ];
                }
                
                $this->table(['Command', 'Description'], $commandsTable);
            }
            $this->newLine();

        } catch (\Exception $e) {
            $this->warn('Could not retrieve commands information: ' . $e->getMessage());
        }
    }

    private function displayUsageStats(): void
    {
        $this->line('<options=bold>📊 Usage Statistics:</>');
        
        // Telegram payments stats
        $telegramPayments = Payment::where('customer_email', 'like', '<EMAIL>')->count();
        $telegramPaymentsSuccess = Payment::where('customer_email', 'like', '<EMAIL>')
            ->where('status', 'success')
            ->count();
        $telegramPaymentsTotal = Payment::where('customer_email', 'like', '<EMAIL>')
            ->where('status', 'success')
            ->sum('amount');

        // General stats
        $totalCategories = AwardCategory::where('is_active', true)->count();
        $activeVotingCategories = AwardCategory::where('is_active', true)
            ->where('voting_start_date', '<=', now())
            ->where('voting_end_date', '>=', now())
            ->count();
        $totalNominees = NomineeProfile::approved()->count();

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Active Categories', $totalCategories],
                ['Categories with Active Voting', $activeVotingCategories],
                ['Total Approved Nominees', $totalNominees],
                ['Telegram Payments (All)', $telegramPayments],
                ['Telegram Payments (Successful)', $telegramPaymentsSuccess],
                ['Telegram Revenue (GHS)', number_format($telegramPaymentsTotal, 2)],
                ['Telegram Success Rate', $telegramPayments > 0 ? 
                    round(($telegramPaymentsSuccess / $telegramPayments) * 100, 1) . '%' : 'N/A'],
            ]
        );
        $this->newLine();

        // Quick actions
        $this->line('<options=bold>🛠️ Quick Actions:</>');
        $this->line('   • Setup bot: <comment>php artisan telegram:setup</comment>');
        $this->line('   • Set commands: <comment>php artisan telegram:set-commands</comment>');
        $this->line('   • Test webhook: <comment>curl -X POST ' . route('telegram.webhook') . '</comment>');
        $this->line('   • View webhook in browser: <comment>' . route('telegram.set-webhook') . '</comment>');
    }
}
