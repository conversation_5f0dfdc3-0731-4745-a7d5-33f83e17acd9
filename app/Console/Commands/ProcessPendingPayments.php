<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Payment;
use App\Models\Vote;
use App\Models\NomineeProfile;
use App\Services\PaystackService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

class ProcessPendingPayments extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'payments:process-pending {--hours=2 : Hours to look back for pending payments}';

    /**
     * The console command description.
     */
    protected $description = 'Process pending USSD payments that may have been missed by webhooks';

    protected PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        parent::__construct();
        $this->paystackService = $paystackService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to process pending payments...');

        // Get pending payments from the last 6 hours
        $pendingPayments = Payment::where('status', 'pending')
            ->where('created_at', '>=', now()->subHours(6))
            ->whereNotNull('paystack_reference')
            ->get();

        if ($pendingPayments->isEmpty()) {
            $this->info('No pending payments found to process.');
            return;
        }

        $this->info("Found {$pendingPayments->count()} pending payments to check.");

        $processed = 0;
        $failed = 0;

        foreach ($pendingPayments as $payment) {
            try {
                $this->info("Checking payment ID: {$payment->id}, Reference: {$payment->paystack_reference}");
                
                // Query Paystack for current payment status
                $response = $this->queryPaystackPayment($payment->paystack_reference);
                
                if ($response && isset($response['data'])) {
                    $paymentData = $response['data'];
                    $status = $paymentData['status'] ?? null;
                    
                    $this->info("Paystack status for {$payment->paystack_reference}: {$status}");
                    
                    if ($status === 'success') {
                        $this->processSuccessfulPayment($payment, $paymentData);
                        $processed++;
                        $this->info("✓ Successfully processed payment {$payment->id}");
                    } elseif (in_array($status, ['failed', 'abandoned'])) {
                        $payment->update([
                            'status' => 'failed',
                            'gateway_response' => json_encode($paymentData)
                        ]);
                        $failed++;
                        $this->warn("✗ Payment {$payment->id} marked as failed");
                    } else {
                        $this->info("- Payment {$payment->id} still pending on Paystack");
                    }
                } else {
                    $this->error("Failed to get payment status from Paystack for {$payment->paystack_reference}");
                }
                
                // Brief pause to avoid rate limiting
                usleep(500000); // 0.5 seconds
                
            } catch (\Exception $e) {
                $this->error("Error processing payment {$payment->id}: " . $e->getMessage());
                Log::error('ProcessPendingPayments Error', [
                    'payment_id' => $payment->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        $this->info("Processing complete. Processed: {$processed}, Failed: {$failed}");
        
        Log::info('ProcessPendingPayments Command Completed', [
            'total_checked' => $pendingPayments->count(),
            'processed' => $processed,
            'failed' => $failed
        ]);
    }

    private function queryPaystackPayment($reference)
    {
        $secretKey = config('services.paystack.secret');
        
        if (!$secretKey) {
            $this->error('Paystack secret key not configured');
            return null;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
                'Content-Type' => 'application/json',
            ])->get("https://api.paystack.co/transaction/verify/{$reference}");

            if ($response->successful()) {
                return $response->json();
            } else {
                $this->error("Paystack API error for {$reference}: " . $response->body());
                return null;
            }
        } catch (\Exception $e) {
            $this->error("HTTP error querying Paystack for {$reference}: " . $e->getMessage());
            return null;
        }
    }

    private function processSuccessfulPayment($payment, $paymentData)
    {
        // Update payment status
        $payment->update([
            'status' => 'success',
            'paid_at' => now(),
            'gateway_response' => json_encode($paymentData)
        ]);

        // Get metadata
        $metadata = $paymentData['metadata'] ?? [];
        
        // Find nominee
        $nomineeId = $metadata['nominee_id'] ?? $payment->metadata['nominee_profile_id'] ?? null;
        $nominee = NomineeProfile::find($nomineeId);
        
        if (!$nominee) {
            $this->error("Nominee not found for payment {$payment->id}");
            Log::error('Nominee not found in ProcessPendingPayments', [
                'payment_id' => $payment->id,
                'nominee_id' => $nomineeId,
                'metadata' => $metadata
            ]);
            return;
        }

        // Check if vote already exists
        $existingVote = Vote::where('payment_id', $payment->id)->first();
        if ($existingVote) {
            $this->info("Vote already exists for payment {$payment->id}");
            return;
        }

        // Calculate vote count
        $voteCount = $metadata['vote_count'] ?? $payment->metadata['votes_requested'] ?? 1;
        
        // Detect payment method
        $channel = $paymentData['channel'] ?? '';
        $authorization = $paymentData['authorization'] ?? [];
        $authChannel = $authorization['channel'] ?? '';
        
        $voteMethod = 'ussd'; // Default for mobile money
        if (in_array($channel, ['card', 'bank']) || in_array($authChannel, ['card', 'bank'])) {
            $voteMethod = 'web';
        } elseif (str_starts_with($payment->reference, 'TG_')) {
            $voteMethod = 'telegram';
        }

        // Get customer info
        $customer = $paymentData['customer'] ?? [];
        
        // Create vote record
        $vote = Vote::create([
            'nominee_profile_id' => $nominee->id,
            'payment_id' => $payment->id,
            'voter_name' => $payment->customer_name ?? $customer['first_name'] ?? 'Mobile Money Voter',
            'voter_email' => $payment->customer_email ?? $customer['email'] ?? '<EMAIL>',
            'voter_phone' => $payment->customer_phone ?? $customer['phone'] ?? $payment->metadata['msisdn'] ?? null,
            'vote_count' => $voteCount,
            'amount_paid' => $payment->amount,
            'discount_applied' => $metadata['discount_applied'] ?? $payment->metadata['discount_applied'] ?? 0,
            'ip_address' => $metadata['ip_address'] ?? null,
            'user_agent' => $metadata['user_agent'] ?? 'Mobile Money App'
        ]);

        // Update nominee vote count
        $nominee->increment('total_votes', $voteCount);

        $this->info("Created vote {$vote->id} for nominee {$nominee->name} ({$voteCount} votes)");
        
        Log::info('Vote created from pending payment processing', [
            'payment_id' => $payment->id,
            'vote_id' => $vote->id,
            'nominee_id' => $nominee->id,
            'vote_count' => $voteCount,
            'amount' => $payment->amount,
            'method' => $voteMethod
        ]);
    }
} 