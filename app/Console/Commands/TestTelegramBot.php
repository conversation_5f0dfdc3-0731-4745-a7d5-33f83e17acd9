<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;

class TestTelegramBot extends Command
{
    protected $signature = 'telegram:test {--keep-webhook : Keep webhook active instead of temporarily removing it}';
    protected $description = 'Test Telegram bot functionality';

    private $originalWebhookUrl = null;

    public function handle()
    {
        $this->info('🤖 Testing Telegram Bot...');
        $this->newLine();

        try {
            // Test bot connection
            $this->line('1. Testing bot connection...');
            $me = Telegram::getMe();
            $this->info("✅ Connected to: {$me->getFirstName()} (@{$me->getUsername()})");
            $this->newLine();

            // Handle webhook conflict
            if (!$this->option('keep-webhook')) {
                $this->handleWebhookForTesting();
            }

            // Get recent updates
            $this->line('2. Checking for recent messages...');
            
            if ($this->option('keep-webhook')) {
                $this->warn('⚠️  Webhook is active - cannot use getUpdates method');
                $this->line('   Use --keep-webhook=false or run: php artisan telegram:test');
                $this->newLine();
            } else {
                $updates = Telegram::getUpdates(['limit' => 5]);
                
                if (count($updates) > 0) {
                    $this->info("📬 Found " . count($updates) . " recent messages:");
                    foreach ($updates as $update) {
                        if ($update->getMessage()) {
                            $message = $update->getMessage();
                            $user = $message->getFrom();
                            $text = $message->getText();
                            $chatId = $message->getChat()->getId();
                            
                            $this->line("  • From: {$user->getFirstName()} (ID: {$chatId})");
                            $this->line("  • Message: {$text}");
                            $this->line("  • Date: " . date('Y-m-d H:i:s', $message->getDate()));
                            $this->newLine();
                        }
                    }
                } else {
                    $this->warn('📭 No recent messages. Send a message to @voteyourfav_bot to test!');
                    $this->newLine();
                }

                // Restore webhook if it was removed
                $this->restoreWebhookAfterTesting();
            }

            // Test commands
            $this->line('3. Checking configured commands...');
            $commands = Telegram::getMyCommands();
            
            if (count($commands) > 0) {
                $this->info('✅ Commands configured:');
                foreach ($commands as $command) {
                    $this->line("  /{$command->getCommand()} - {$command->getDescription()}");
                }
            } else {
                $this->warn('❌ No commands configured');
            }
            $this->newLine();

            $this->info('🎯 How to test the bot:');
            $this->line('1. Open Telegram and search for @voteyourfav_bot');
            $this->line('2. Send /start to begin');
            $this->line('3. Try commands like /categories, /help, etc.');
            
            if (!$this->option('keep-webhook')) {
                $this->line('4. Run "php artisan telegram:test" again to see new messages');
            } else {
                $this->line('4. Check your webhook endpoint for live updates');
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            
            // Try to restore webhook even if there was an error
            if (!$this->option('keep-webhook') && $this->originalWebhookUrl) {
                $this->restoreWebhookAfterTesting();
            }
            
            return 1;
        }

        return 0;
    }

    private function handleWebhookForTesting()
    {
        try {
            // Get current webhook info
            $webhookInfo = Telegram::getWebhookInfo();
            $this->originalWebhookUrl = $webhookInfo->getUrl();

            if ($this->originalWebhookUrl) {
                $this->line("⚠️  Webhook is active: {$this->originalWebhookUrl}");
                $this->line("   Temporarily removing webhook for testing...");
                
                // Delete webhook
                $result = Telegram::deleteWebhook(['drop_pending_updates' => true]);
                
                if ($result) {
                    $this->info("✅ Webhook temporarily removed");
                } else {
                    $this->warn("⚠️  Could not remove webhook");
                }
                
                $this->newLine();
            }
        } catch (\Exception $e) {
            $this->warn("Could not handle webhook: " . $e->getMessage());
        }
    }

    private function restoreWebhookAfterTesting()
    {
        if ($this->originalWebhookUrl) {
            try {
                $this->line("🔄 Restoring webhook...");
                
                $result = Telegram::setWebhook([
                    'url' => $this->originalWebhookUrl,
                    'allowed_updates' => ['message', 'callback_query'],
                    'drop_pending_updates' => false
                ]);
                
                if ($result) {
                    $this->info("✅ Webhook restored: {$this->originalWebhookUrl}");
                } else {
                    $this->warn("⚠️  Could not restore webhook");
                    $this->line("   Run: php artisan telegram:setup to reconfigure");
                }
                
            } catch (\Exception $e) {
                $this->warn("Could not restore webhook: " . $e->getMessage());
                $this->line("   Run: php artisan telegram:setup to reconfigure");
            }
        }
    }
}
