<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NomineeProfile;

class TestSlugGeneration extends Command
{
    protected $signature = 'test:slug-generation';
    protected $description = 'Test the new 5-character slug generation';

    public function handle()
    {
        $this->info('🔧 Testing New Slug Generation...');
        $this->newLine();

        // Generate 5 test slugs
        for ($i = 1; $i <= 5; $i++) {
            $slug = NomineeProfile::generateUniqueSlug();
            $length = strlen($slug);
            $isValid = preg_match('/^[A-Z0-9]{5}$/', $slug);
            
            $this->line("Slug #{$i}: <fg=green>{$slug}</fg=green>");
            $this->line("  └─ Length: {$length} characters");
            $this->line("  └─ Format valid: " . ($isValid ? '✅ Yes' : '❌ No'));
            $this->newLine();
        }

        $this->info('✨ Benefits of new slug format:');
        $this->line('• Short and memorable (5 characters)');
        $this->line('• Perfect for Telegram: /vote ' . NomineeProfile::generateUniqueSlug());
        $this->line('• USSD friendly: *123*' . NomineeProfile::generateUniqueSlug() . '#');
        $this->line('• Easy to type and share');
        $this->line('• 36^5 = 60,466,176 possible combinations');
    }
}
