<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        // Check if admin user is approved (super admins bypass this check)
        if ($user->isAdmin() && !$user->isSuperAdmin() && !$user->isApproved()) {
            // Allow access only to dashboard for pending admins
            if (!$request->routeIs('admin.dashboard*')) {
                return redirect()->route('admin.dashboard')
                    ->with('warning', 'Your account is pending approval. Access to admin features is restricted until approved.');
            }
        }

        return $next($request);
    }
} 