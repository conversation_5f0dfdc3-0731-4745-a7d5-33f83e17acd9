<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ViewerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Allow super admins to access viewer routes for testing/debugging
        if ($user->isSuperAdmin() || $user->isViewer()) {
            return $next($request);
        }

        // Redirect non-viewers to their appropriate dashboard
        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        }

        if ($user->isNominee()) {
            return redirect()->route('dashboard');
        }

        // Fallback redirect
        return redirect()->route('dashboard');
    }
}
