<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApprovedUserMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Super admins bypass approval checks
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Check if user is approved
        if (!$user->isApproved()) {
            return redirect()->route('admin.dashboard')
                ->with('error', 'Your account is pending approval. Access to this feature is restricted.');
        }

        return $next($request);
    }
}
