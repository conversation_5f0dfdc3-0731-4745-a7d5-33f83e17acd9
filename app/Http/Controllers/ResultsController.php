<?php

namespace App\Http\Controllers;

use App\Models\AwardCategory;
use App\Models\Organization;
use App\Models\Vote;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class ResultsController extends Controller
{
    /**
     * Display the public results page with beautiful visualizations
     */
    public function index()
    {
        // Get all organizations with their categories and voting data
        $organizations = Organization::with(['awardCategories' => function($query) {
            $query->where('is_active', true)
                  ->orderBy('name');
        }])
        ->where('is_active', true)
        ->orderBy('name')
        ->get();

        $resultsData = [];
                 $overallStats = [
             'total_votes' => 0,
             'total_nominees' => 0,
             'total_categories' => 0,
             'total_organizations' => $organizations->count(),
         ];

        foreach ($organizations as $organization) {
                         $orgData = [
                 'id' => $organization->id,
                 'name' => $organization->name,
                 'description' => $organization->description,
                 'slug' => $organization->slug,
                 'categories' => [],
                 'total_votes' => 0,
             ];

            foreach ($organization->awardCategories as $category) {
                // Get nominees for this category with their vote counts
                $nominees = $category->nomineeProfiles()
                    ->approved()
                    ->with(['votes'])
                    ->get()
                    ->map(function ($nominee) {
                                                 $totalVotes = $nominee->votes->sum('vote_count');
                         
                         return [
                             'id' => $nominee->id,
                             'display_name' => $nominee->display_name,
                             'slug' => $nominee->slug,
                             'bio' => $nominee->bio,
                             'total_votes' => $totalVotes,
                             'vote_percentage' => 0, // Will be calculated below
                         ];
                    })
                    ->sortByDesc('total_votes')
                    ->values();

                // Calculate percentages
                $categoryTotalVotes = $nominees->sum('total_votes');
                if ($categoryTotalVotes > 0) {
                    $nominees = $nominees->map(function ($nominee) use ($categoryTotalVotes) {
                        $nominee['vote_percentage'] = round(($nominee['total_votes'] / $categoryTotalVotes) * 100, 1);
                        return $nominee;
                    });
                }

                // Get voting trends (votes per day for the last 30 days)
                $votingTrends = $this->getVotingTrends($category->id);

                                 $categoryData = [
                     'id' => $category->id,
                     'name' => $category->name,
                     'description' => $category->description,
                     'slug' => $category->slug,
                     'price_per_vote' => (float) $category->price_per_vote,
                     'voting_start_date' => $category->voting_start_date,
                     'voting_end_date' => $category->voting_end_date,
                     'is_voting_active' => $category->isVotingActive(),
                     'nominees' => $nominees,
                     'total_votes' => $categoryTotalVotes,
                     'voting_trends' => $votingTrends,
                 ];

                                 $orgData['categories'][] = $categoryData;
                 $orgData['total_votes'] += $categoryTotalVotes;

                                 // Update overall stats
                 $overallStats['total_votes'] += $categoryTotalVotes;
                 $overallStats['total_nominees'] += $nominees->count();
                 $overallStats['total_categories']++;
            }

            $resultsData[] = $orgData;
        }

        // Get top performing categories across all organizations
        $topCategories = collect($resultsData)
            ->pluck('categories')
            ->flatten(1)
            ->sortByDesc('total_votes')
            ->take(10)
            ->values();

        // Get recent voting activity
        $recentActivity = Vote::with(['nomineeProfile:id,display_name,slug', 'nomineeProfile.awardCategories:id,name'])
            ->latest()
            ->take(20)
            ->get()
            ->map(function ($vote) {
                $category = $vote->nomineeProfile->awardCategories->first();
                return [
                    'id' => $vote->id,
                    'nominee_name' => $vote->nomineeProfile->display_name,
                    'nominee_slug' => $vote->nomineeProfile->slug,
                    'category_name' => $category ? $category->name : 'Unknown',
                                         'vote_count' => $vote->vote_count,
                     'voter_name' => $vote->voter_name ?: 'Anonymous',
                    'created_at' => $vote->created_at,
                ];
            });

        return Inertia::render('results', [
            'organizations' => $resultsData,
            'overall_stats' => $overallStats,
            'top_categories' => $topCategories,
            'recent_activity' => $recentActivity,
            'page_title' => 'Voting Results - Live Dashboard',
        ]);
    }

    /**
     * Get voting trends for a category over the last 30 days
     */
    private function getVotingTrends($categoryId)
    {
        // Get nominee profile IDs for this category
        $nomineeProfileIds = DB::table('award_category_nominee_profile')
            ->where('award_category_id', $categoryId)
            ->pluck('nominee_profile_id');

        if ($nomineeProfileIds->isEmpty()) {
            return [];
        }

        // Get votes grouped by date for the last 30 days
        $trends = Vote::whereIn('nominee_profile_id', $nomineeProfileIds)
            ->where('created_at', '>=', now()->subDays(30))
                         ->select(
                 DB::raw('DATE(created_at) as date'),
                 DB::raw('SUM(vote_count) as votes')
             )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
                         ->map(function ($item) {
                 return [
                     'date' => $item->date,
                     'votes' => (int) $item->votes,
                 ];
             });

        // Fill in missing dates with zero values
        $startDate = now()->subDays(29);
        $endDate = now();
        $dateRange = [];
        
        for ($date = $startDate; $date <= $endDate; $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $existingData = $trends->firstWhere('date', $dateStr);
            
                         $dateRange[] = [
                 'date' => $dateStr,
                 'votes' => $existingData ? $existingData['votes'] : 0,
             ];
        }

        return $dateRange;
    }
} 