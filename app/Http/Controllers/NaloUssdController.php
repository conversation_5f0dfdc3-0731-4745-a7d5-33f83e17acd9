<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\UssdSession;
use App\Models\NomineeProfile;
use App\Models\AwardCategory;
use App\Models\Payment;
use App\Services\PaystackService;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

class NaloUssdController extends Controller
{
    protected PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Handle NALO USSD webhook requests
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            // Validate incoming request according to NALO spec
            $validator = Validator::make($request->all(), [
                'USERID' => 'required|string|max:8',
                'MSISDN' => 'required|string|size:12',
                'USERDATA' => 'nullable|string|max:120',
                'MSGTYPE' => 'required|boolean',
                'NETWORK' => 'nullable|string|max:10',
                'SESSIONID' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                Log::error('NALO USSD validation failed', [
                    'errors' => $validator->errors(),
                    'request' => $request->all()
                ]);
                
                return $this->errorResponse($request, 'Invalid request format');
            }
            $userId = $request->input('USERID');
            $msisdn = $request->input('MSISDN');
            $userInput = (string) $request->input('USERDATA', '');
            $isFirstRequest = $request->input('MSGTYPE');
            $network = $request->input('NETWORK', 'UNKNOWN');
            $sessionId = $request->input('SESSIONID', $msisdn . '_' . time());

            Log::info('NALO USSD Request', [
                'user_id' => $userId,
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'user_input' => $userInput,
                'is_first' => $isFirstRequest,
                'network' => $network
            ]);

            // Get or create session
            $session = $this->getOrCreateSession($sessionId, $msisdn, $userId, $network);

            // Process the request based on current state
            $response = $this->processUssdRequest($session, $userInput, $isFirstRequest);

            // Update session activity
            $session->updateActivity();

            Log::info('NALO USSD Response', [
                'session_id' => $sessionId,
                'response' => $response
            ]);

            return response()->json([
                'USERID' => $userId,
                'MSISDN' => $msisdn,
                'USERDATA' => $userInput,
                'MSG' => $response['message'],
                'MSGTYPE' => $response['continue_session']
            ]);

        } catch (\Exception $e) {
            Log::error('NALO USSD webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return $this->errorResponse($request, 'System error. Please try again.');
        }
    }
    /**
     * Process USSD request based on session state
     */
    private function processUssdRequest(UssdSession $session, ?string $userInput, bool $isFirstRequest): array
    {
        // Clean user input - handle null by converting to empty string
        $userInput = trim($userInput ?? '');

        // If this is the first request, show main menu
        if ($isFirstRequest) {
            return $this->showMainMenu($session);
        }

        // Handle session timeout
        if ($session->isExpired()) {
            $session->update(['is_active' => false]);
            return $this->timeoutResponse();
        }

        // Process based on current screen
        switch ($session->current_screen) {
            case 'main_menu':
                return $this->handleMainMenuInput($session, $userInput);
                
            case 'enter_nominee_code':
                return $this->handleNomineeCodeInput($session, $userInput);
                
            case 'check_votes':
                return $this->handleCheckVotes($session, $userInput);
                
            case 'enter_vote_count':
                return $this->handleVoteCountInput($session, $userInput);
                
            case 'confirm_payment':
                return $this->handlePaymentConfirmation($session, $userInput);
                  case 'payment_processing':
                return $this->handlePaymentProcessing($session, $userInput);
                
            case 'enter_otp':
                return $this->handleOtpInput($session, $userInput);
                
            default:
                return $this->showMainMenu($session);
        }
    }

    /**
     * Show main menu
     */
    private function showMainMenu(UssdSession $session): array
    {
        $session->update([
            'current_screen' => 'main_menu',
            'user_data' => json_encode([])
        ]);

        $message = "Welcome to VoteYourFav!\n";
        $message .= "Cast your vote easily.\n\n";
        $message .= "1. Vote with Nominee Code\n";
        $message .= "2. Check Nominee Votes\n";
        $message .= "0. Exit";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }
    /**
     * Handle main menu input
     */
    private function handleMainMenuInput(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->showNomineeCodeEntry($session);
                
            case '2':
                $session->update(['current_screen' => 'check_votes']);
                return [
                    'message' => "Enter nominee code to check votes:\n(e.g., A1B2C or JOHN001)\n\n0. Back to menu",
                    'continue_session' => true
                ];
                
            case '0':
                $session->update(['is_active' => false]);
                return [
                    'message' => "Thank you for using VoteYourFav!",
                    'continue_session' => false
                ];
                
            default:
                return [
                    'message' => "Invalid option. Please select:\n1. Vote with Code\n2. Check Nominee Votes\n0. Exit",
                    'continue_session' => true
                ];
        }
    }

    /**
     * Show nominee code entry screen
     */
    private function showNomineeCodeEntry(UssdSession $session): array
    {
        $session->update(['current_screen' => 'enter_nominee_code']);
        return [
            'message' => "Enter nominee code:\n(e.g., A1B2C, X9Y7Z)\nor nominee code (JOHN001)\n\n0. Back to menu",
            'continue_session' => true
        ];
    }
    /**
     * Handle nominee code input
     */
    private function handleNomineeCodeInput(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            return $this->showMainMenu($session);
        }

        $nomineeCode = strtoupper(trim($input));
        
        // Find nominee by code (searches both nominee_code and slug fields) using VoteYourFav's findByCode method
        $nominee = NomineeProfile::findByCode($nomineeCode);

        if (!$nominee) {
            return [
                'message' => "Invalid nominee code: {$nomineeCode}\nPlease try again or press 0 to go back.",
                'continue_session' => true
            ];
        }

        // Check if nominee can receive votes
        if (!$nominee->canReceiveVotes()) {
            return [
                'message' => "Voting not active for this nominee.\nTry another code or press 0 to go back.",
                'continue_session' => true
            ];
        }

        $category = $nominee->awardCategories->first();
        
        if (!$category) {
            return [
                'message' => "Nominee not assigned to category.\nTry another code or press 0 to go back.",
                'continue_session' => true
            ];
        }
        
        // Store nominee in session
        $userData = [
            'nominee' => [
                'id' => $nominee->id,
                'name' => $nominee->display_name,
                'code' => $nomineeCode,
                'category_id' => $category->id,
                'category_name' => $category->name,
                'price_per_vote' => $category->price_per_vote,
                'discount_percentage' => $category->discount_percentage,
                'discount_min_votes' => $category->discount_min_votes
            ]
        ];
        
        $session->update([
            'current_screen' => 'enter_vote_count',
            'user_data' => json_encode($userData)
        ]);

        $message = "Nominee: {$nominee->display_name}\n";
        $message .= "Category: {$category->name}\n";
        $message .= "Price: GHS {$category->price_per_vote}/vote\n";
        
        if ($category->discount_percentage > 0) {
            $message .= "Discount: {$category->discount_percentage}% for {$category->discount_min_votes}+ votes\n";
        }
        
        $message .= "\nEnter votes (1-100000):\n0. Back to menu";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }
    /**
     * Show categories with pagination
     */
    private function showCategories(UssdSession $session, int $page = 1): array
    {
        $perPage = 4; // Show 4 categories per page to leave room for navigation
        $offset = ($page - 1) * $perPage;
        
        // Get total count for pagination
        $totalCategories = AwardCategory::where('is_active', true)
            ->where('is_suspended', false)
            ->count();
            
        if ($totalCategories === 0) {
            return [
                'message' => "No categories available.\nPress 0 to go back.",
                'continue_session' => true
            ];
        }
        
        // Get categories for current page
        $categories = AwardCategory::where('is_active', true)
            ->where('is_suspended', false)
            ->with('organization')
            ->skip($offset)
            ->take($perPage)
            ->get();

        $totalPages = ceil($totalCategories / $perPage);
        
        $userData = [
            'categories' => $categories->toArray(),
            'page' => $page,
            'total_pages' => $totalPages,
            'per_page' => $perPage
        ];
        
        $session->update([
            'current_screen' => 'browse_categories',
            'user_data' => json_encode($userData)
        ]);

        $message = "Categories (Page {$page}/{$totalPages}):\n";
        foreach ($categories as $index => $category) {
            $number = $index + 1;
            $message .= "{$number}. {$category->name}\n";
        }
        
        // Add navigation options
        if ($totalPages > 1) {
            if ($page < $totalPages) {
                $message .= "5. Next Page\n";
            }
            if ($page > 1) {
                $message .= "8. Previous Page\n";
            }
        }
        
        $message .= "0. Back to menu";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }
    /**
     * Handle category selection with pagination
     */
    private function handleCategorySelection(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            return $this->showMainMenu($session);
        }

        $userData = json_decode($session->user_data, true);
        $categories = collect($userData['categories']);
        $currentPage = $userData['page'] ?? 1;
        $totalPages = $userData['total_pages'] ?? 1;
        
        // Handle pagination options
        if ($input === '5' && $currentPage < $totalPages) {
            // Next page
            return $this->showCategories($session, $currentPage + 1);
        }
        
        if ($input === '8' && $currentPage > 1) {
            // Previous page
            return $this->showCategories($session, $currentPage - 1);
        }

        // Handle category selection
        $categoryIndex = (int)$input - 1;

        if ($categoryIndex < 0 || $categoryIndex >= $categories->count()) {
            $maxOption = $categories->count();
            $navOptions = [];
            
            if ($totalPages > 1) {
                if ($currentPage < $totalPages) $navOptions[] = "5. Next Page";
                if ($currentPage > 1) $navOptions[] = "8. Previous Page";
            }
            
            $message = "Invalid selection. Choose 1-{$maxOption}";
            if (!empty($navOptions)) {
                $message .= ", " . implode(", ", $navOptions);
            }
            $message .= " or 0 to go back.";
            
            return [
                'message' => $message,
                'continue_session' => true
            ];
        }

        $selectedCategory = $categories[$categoryIndex];
          // Get nominees for this category
        $nominees = NomineeProfile::whereHas('awardCategories', function ($query) use ($selectedCategory) {
            $query->where('award_categories.id', $selectedCategory['id']);
        })
        ->approved() // Use VoteYourFav's approved scope
        ->with('awardCategories')
        ->take(9)
        ->get();

        if ($nominees->isEmpty()) {
            return [
                'message' => "No nominees in this category.\nPress 0 to go back.",
                'continue_session' => true
            ];
        }

        $userData['selected_category'] = $selectedCategory;
        $userData['nominees'] = $nominees->toArray();
        
        $session->update([
            'current_screen' => 'browse_nominees',
            'user_data' => json_encode($userData)
        ]);

        $message = "Nominees in {$selectedCategory['name']}:\n";
        foreach ($nominees as $index => $nominee) {
            $number = $index + 1;
            $message .= "{$number}. {$nominee->display_name}\n";
        }
        $message .= "0. Back to categories";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }

    /**
     * Handle nominee selection
     */
    private function handleNomineeSelection(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            $userData = json_decode($session->user_data, true);
            $currentPage = $userData['page'] ?? 1;
            return $this->showCategories($session, $currentPage);
        }

        $userData = json_decode($session->user_data, true);
        $nominees = collect($userData['nominees']);
        $nomineeIndex = (int)$input - 1;

        if ($nomineeIndex < 0 || $nomineeIndex >= $nominees->count()) {
            return [
                'message' => "Invalid selection. Choose 1-{$nominees->count()} or 0 to go back.",
                'continue_session' => true
            ];
        }

        $selectedNominee = $nominees[$nomineeIndex];
        $category = $userData['selected_category'];
        
        $userData['nominee'] = [
            'id' => $selectedNominee['id'],
            'name' => $selectedNominee['display_name'],
            'code' => $selectedNominee['slug'],
            'category_id' => $category['id'],
            'category_name' => $category['name'],
            'price_per_vote' => $category['price_per_vote']
        ];
        
        $session->update([
            'current_screen' => 'enter_vote_count',
            'user_data' => json_encode($userData)
        ]);

        $message = "Nominee: {$selectedNominee['display_name']}\n";
        $message .= "Category: {$category['name']}\n";
        $message .= "Price: GHS {$category['price_per_vote']} per vote\n\n";
        $message .= "Enter number of votes (1-100000):\n";
        $message .= "0. Back to nominees";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }
    /**
     * Handle vote count input
     */
    private function handleVoteCountInput(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            $userData = json_decode($session->user_data, true);
            // Check if we came from browsing nominees or entering a code
            if (isset($userData['selected_category'])) {
                // Return to nominee list for that category
                $session->update(['current_screen' => 'browse_nominees']);
                return $this->handleCategorySelection($session, (string) ($userData['selected_category_index'] ?? '1'));
            } else {
                // Return to main menu if code was entered directly
                return $this->showMainMenu($session);
            }
        }

        $voteCount = (int)$input;
        
        if ($voteCount < 1 || $voteCount > 100000) {
            return [
                'message' => "Invalid vote count. Enter 1-100000 votes or 0 to go back.",
                'continue_session' => true
            ];
        }

        $userData = json_decode($session->user_data, true);
        $nominee = $userData['nominee'];
        
        // Get the category to calculate pricing using VoteYourFav logic
        $category = AwardCategory::find($nominee['category_id']);
        if (!$category) {
            return [
                'message' => "Error loading category. Please try again.\n0. Back to menu",
                'continue_session' => true
            ];
        }
        
        // Use VoteYourFav's pricing calculation
        $pricing = $category->calculatePrice($voteCount);
        
        $userData['vote_count'] = $voteCount;
        $userData['pricing'] = $pricing;
        
        $session->update([
            'current_screen' => 'confirm_payment',
            'user_data' => json_encode($userData)
        ]);

        $message = "Confirm your vote:\n";
        $message .= "Nominee: {$nominee['name']}\n";
        $message .= "Votes: {$voteCount}\n";
        $message .= "Base cost: GHS {$pricing['base_price']}\n";
        
        if ($pricing['discount'] > 0) {
            $message .= "Discount: -GHS {$pricing['discount']}\n";
        }
        
        $message .= "Final total: GHS {$pricing['final_price']}\n\n";
        $message .= "1. Confirm & Pay\n";
        $message .= "2. Change vote count\n";
        $message .= "0. Cancel";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }

    /**
     * Handle payment confirmation
     */
    private function handlePaymentConfirmation(UssdSession $session, string $input): array
    {
        $userData = json_decode($session->user_data, true);
        
        switch ($input) {
            case '1':
                return $this->initiatePayment($session, $userData);
                
            case '2':
                $session->update(['current_screen' => 'enter_vote_count']);
                $nominee = $userData['nominee'];
                $message = "Nominee: {$nominee['name']}\n";
                $message .= "Enter number of votes (1-100000):\n0. Back";
                return [
                    'message' => $message,
                    'continue_session' => true
                ];
                
            case '0':
                return $this->showMainMenu($session);
                
            default:
                return [
                    'message' => "Invalid option:\n1. Confirm & Pay\n2. Change votes\n0. Cancel",
                    'continue_session' => true
                ];
        }
    }    /**
     * Initiate payment process
     */
    private function initiatePayment(UssdSession $session, array $userData): array
    {
        // Use database transaction to ensure data consistency
        return \DB::transaction(function () use ($session, $userData) {
        try {
            $nominee = $userData['nominee'];
            $voteCount = $userData['vote_count'];
            $totalAmount = $userData['pricing']['final_price'];

            // Find nominee profile
            $nomineeProfile = NomineeProfile::find($nominee['id']);
            if (!$nomineeProfile) {
                return [
                    'message' => "Nominee not found.\n0. Back to menu",
                    'continue_session' => true
                ];
            }

            // Determine provider from phone number
            $provider = $this->detectMobileMoneyProvider($session->msisdn);
            
            if (!$provider) {
                return [
                    'message' => "Mobile money not supported for this number.\n0. Back to menu",
                    'continue_session' => true
                ];
            }

            // Create payment record first
            $paymentReference = 'NALO_' . strtoupper(Str::random(12));
            
            Log::info('Creating USSD payment record', [
                'reference' => $paymentReference,
                'amount' => $totalAmount,
                'nominee_id' => $nomineeProfile->id,
                'session_id' => $session->session_id,
                'msisdn' => $session->msisdn
            ]);
            
            try {
                $payment = \App\Models\Payment::create([
                    'reference' => $paymentReference,
                    'amount' => $totalAmount, // Store in GHS
                    'currency' => 'GHS',
                    'status' => 'pending',
                    'payment_method' => 'mobile_money',
                    'customer_name' => 'NALO USSD Voter',
                    'customer_email' => $this->generateUniqueEmailFromPhone($session->msisdn),
                    'customer_phone' => $session->msisdn,
                    'metadata' => [
                        'session_id' => $session->session_id,
                        'nominee_profile_id' => $nomineeProfile->id,
                        'msisdn' => $session->msisdn,
                        'votes_requested' => $voteCount,
                        'provider' => $provider,
                        'source' => 'nalo_ussd',
                        'platform' => 'voteyourfav',
                        'total_amount_ghs' => $totalAmount,
                        'discount_applied' => $userData['pricing']['discount_percentage'] ?? 0
                    ]
                ]);
                
                Log::info('USSD payment record created successfully', [
                    'payment_id' => $payment->id,
                    'reference' => $payment->reference,
                    'amount' => $payment->amount,
                    'status' => $payment->status
                ]);
                
                // Verify payment was actually saved
                $verifyPayment = \App\Models\Payment::find($payment->id);
                if (!$verifyPayment) {
                    throw new \Exception('Payment created but not found in database after creation');
                }
                
            } catch (\Exception $e) {
                Log::error('Failed to create USSD payment record', [
                    'reference' => $paymentReference,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'session_id' => $session->session_id
                ]);
                
                return [
                    'message' => "Payment setup failed. Please try again.\n0. Back to menu",
                    'continue_session' => true
                ];
            }

            // Map providers to Paystack mobile money format
            $providerMap = [
                'mtn' => 'mtn',
                'vodafone' => 'vod',
                'airtel' => 'atl',
                'tigo' => 'atl' // Airtel-Tigo merger
            ];

            $paystackProvider = $providerMap[$provider] ?? 'mtn';

            // Create mobile money charge using the charge API (not deprecated method)
            $paystackResponse = $this->paystackService->charge([
                'email' => $payment->customer_email,
                'amount' => $totalAmount,
                'currency' => 'GHS',
                'reference' => $payment->reference,
                'mobile_money' => [
                    'phone' => $this->formatPhoneForProvider($session->msisdn),
                    'provider' => $paystackProvider
                ],
                'metadata' => [
                    'payment_id' => $payment->id,
                    'vote_count' => $voteCount,
                    'nominee_id' => $nomineeProfile->id,
                    'session_id' => $session->session_id,
                    'msisdn' => $session->msisdn,
                    'source' => 'nalo_ussd',
                    'platform' => 'voteyourfav',
                    'total_amount_ghs' => $totalAmount,
                    'provider' => $provider,
                    'discount_applied' => $userData['pricing']['discount_percentage'] ?? 0
                ],
                'callback_url' => env('APP_URL') . '/api/payments/webhook'
            ]);

            if (!$paystackResponse['status']) {
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => json_encode($paystackResponse)
                ]);

                Log::error('NALO USSD Mobile money charge failed', [
                    'payment_id' => $payment->id,
                    'payment_reference' => $payment->reference,
                    'error' => $paystackResponse['message'] ?? 'Unknown error',
                    'response' => $paystackResponse
                ]);
                
                return [
                    'message' => "Payment setup failed. Please try again.\n0. Back to menu",
                    'continue_session' => true
                ];
            }

            // Update payment with gateway reference
            $payment->update([
                'paystack_reference' => $paystackResponse['data']['reference'] ?? null,
                'gateway_response' => json_encode($paystackResponse['data'])
            ]);

            // Update session with payment reference
            $userData['payment_reference'] = $payment->reference;
            $userData['payment_id'] = $payment->id;
            
            $session->update([
                'current_screen' => 'payment_processing',
                'user_data' => json_encode($userData)
            ]);

            Log::info('NALO USSD Mobile Money Charge Initiated', [
                'payment_id' => $payment->id,
                'payment_reference' => $payment->reference,
                'paystack_reference' => $paystackResponse['data']['reference'] ?? null,
                'amount' => $totalAmount,
                'nominee' => $nomineeProfile->display_name,
                'session_id' => $session->session_id,
                'provider' => $provider,
                'paystack_status' => $paystackResponse['data']['status'] ?? 'unknown'
            ]);

            $chargeStatus = $paystackResponse['data']['status'] ?? '';
            
            if ($chargeStatus === 'pay_offline' || $chargeStatus === 'send_otp') {
                return [
                    'message' => "Close and check your approvals",
                    'continue_session' => false
                ];
            } else {
                $message = "A payment request has been initiated. Please check your approval\n\n";
                $message .= "Amount: GHS {$totalAmount}\n";
                $message .= "Reference: {$payment->reference}\n\n";
                $message .= "1. I have paid\n";
                $message .= "0. Cancel";
                
                return [
                    'message' => $message,
                    'continue_session' => true
                ];
            }

        } catch (\Exception $e) {
            Log::error('NALO USSD Payment initiation error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_data' => $userData,
                'session_id' => $session->session_id
            ]);

            return [
                'message' => "Payment setup failed. Please try again.\n0. Back to menu",
                'continue_session' => true
            ];
        }
        }); // Close DB transaction
    }

    /**
     * Handle payment processing status
     */
    private function handlePaymentProcessing(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            return $this->showMainMenu($session);
        }

        $userData = json_decode($session->user_data, true);
        $paymentReference = $userData['payment_reference'] ?? null;
        
        if (!$paymentReference) {
             return $this->showMainMenu($session);
        }

        // Always check status when user inputs '1'
        if ($input === '1') {
            return $this->checkPaymentStatus($session, $paymentReference);
        }

        // Default message if input is not 1 or 0
        return [
            'message' => "Invalid option.\n\n1. I have paid (Check status)\n0. Cancel",
            'continue_session' => true
        ];
    }
      /**
     * Check payment status
     */
    private function checkPaymentStatus(UssdSession $session, string $paymentReference): array
    {
        Log::info('NALO USSD Payment Status Check', [
            'payment_reference' => $paymentReference,
            'session_id' => $session->session_id
        ]);
        
        // Check payment status using the Payment model
        $payment = \App\Models\Payment::where('reference', $paymentReference)->first();
            
        if (!$payment) {
            Log::error('NALO USSD Payment Not Found', [
                'payment_reference' => $paymentReference,
                'session_id' => $session->session_id
            ]);
            return [
                'message' => "Payment not found.\n0. Back to menu",
                'continue_session' => true
            ];
        }

        Log::info('NALO USSD Payment Found', [
            'payment_reference' => $paymentReference,
            'current_status' => $payment->status,
            'session_id' => $session->session_id
        ]);

        // Check if payment was completed via webhook
        if ($payment->status === 'success') {
            // Check if vote was already created
            $existingVote = \App\Models\Vote::where('payment_id', $payment->id)->first();
            
            if ($existingVote) {
                $metadata = $payment->metadata ?? [];
                $nomineeProfileId = $metadata['nominee_profile_id'] ?? null;
                $nominee = \App\Models\NomineeProfile::find($nomineeProfileId);
                $session->update(['is_active' => false]);
                
                return [
                    'message' => "✓ Payment successful!\n" .
                               "Vote(s) cast for: " . ($nominee ? $nominee->display_name : 'Unknown') . "\n" .
                               "Votes: {$existingVote->vote_count}\n" .
                               "Thank you for voting!",
                    'continue_session' => false
                ];
            }
        }

        // If payment is still pending, check with Paystack for real-time status
        if ($payment->status === 'pending' && $payment->paystack_reference) {
            try {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . config('services.paystack.secret'),
                    'Content-Type' => 'application/json',
                ])->get("https://api.paystack.co/transaction/verify/{$payment->paystack_reference}");

                if ($response->successful()) {
                    $verificationData = $response->json();
                    $paystackStatus = $verificationData['data']['status'] ?? null;
                    
                    Log::info('NALO USSD Paystack Verification Result', [
                        'payment_reference' => $paymentReference,
                        'gateway_reference' => $payment->paystack_reference,
                        'paystack_status' => $paystackStatus,
                        'session_id' => $session->session_id
                    ]);
                    
                    if ($paystackStatus === 'success') {
                        // Payment succeeded but webhook might have missed it
                        // Update payment status
                        $payment->update([
                            'status' => 'success',
                            'gateway_response' => json_encode($verificationData['data']),
                            'processed_at' => now()
                        ]);

                        // Create vote if it doesn't exist
                        $existingVote = \App\Models\Vote::where('payment_id', $payment->id)->first();
                        if (!$existingVote) {
                            $metadata = $payment->metadata ?? [];
                            $nomineeProfileId = $metadata['nominee_profile_id'] ?? null;
                            $votesRequested = $metadata['votes_requested'] ?? 1;
                            $sessionId = $metadata['session_id'] ?? null;
                            
                            $nominee = \App\Models\NomineeProfile::find($nomineeProfileId);
                            if ($nominee) {
                                $vote = \App\Models\Vote::create([
                                    'nominee_profile_id' => $nominee->id,
                                    'payment_id' => $payment->id,
                                    'voter_name' => $payment->customer_name,
                                    'voter_email' => $payment->customer_email,
                                    'voter_phone' => $payment->customer_phone,
                                    'vote_count' => $votesRequested,
                                    'amount_paid' => $payment->amount,
                                    'discount_applied' => 0,
                                    'vote_method' => 'ussd',
                                    'session_id' => $sessionId,
                                    'ip_address' => null,
                                    'user_agent' => 'NALO USSD'
                                ]);

                                // Update nominee vote count
                                $nominee->increment('vote_count', $votesRequested);

                                Log::info('NALO USSD Vote Created from Status Check', [
                                    'payment_id' => $payment->id,
                                    'vote_id' => $vote->id,
                                    'nominee_id' => $nominee->id,
                                    'vote_count' => $votesRequested
                                ]);
                            }
                        }

                        $session->update(['is_active' => false]);
                        
                        return [
                            'message' => "✓ Payment successful!\n" .
                                       "Vote(s) cast for: {$nominee->display_name}\n" .
                                       "Votes: {$votesRequested}\n" .
                                       "Thank you for voting!",
                            'continue_session' => false
                        ];
                    } elseif (in_array($paystackStatus, ['failed', 'abandoned'])) {
                        $payment->update([
                            'status' => 'failed',
                            'gateway_response' => json_encode($verificationData['data']),
                            'processed_at' => now()
                        ]);
                        
                        return [
                            'message' => "Payment failed. Please try again.\n0. Back to menu",
                            'continue_session' => true
                        ];
                    }
                } else {
                    Log::error('NALO USSD Paystack Verification Failed', [
                        'payment_reference' => $paymentReference,
                        'gateway_reference' => $payment->paystack_reference,
                        'http_status' => $response->status(),
                        'response_body' => $response->body()
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('NALO USSD Paystack Verification Exception', [
                    'payment_reference' => $paymentReference,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Payment still pending
        return [
            'message' => "Payment is being processed...\n\n" .
                       "1. Check status again\n" .
                       "0. Cancel",
            'continue_session' => true
        ];
    }

    /**
     * Show help screen
     */
    private function showHelp(UssdSession $session): array
    {
        $session->update(['current_screen' => 'main_menu']); // Go back to main menu after help
        $message = "VoteYourFav Help:\n\n";
        $message .= "• Vote with a nominee's code or browse categories.\n";
        $message .= "• Payments are made via Mobile Money.\n";
        $message .= "• For issues, contact <NAME_EMAIL>\n\n";
        $message .= "0. Back to menu";

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }

    /**
     * Get or create USSD session
     */
    private function getOrCreateSession(string $sessionId, string $msisdn, string $userId, string $network): UssdSession
    {
        $session = UssdSession::where('session_id', $sessionId)
            ->where('msisdn', $msisdn)
            ->first();

        if (!$session || $session->isExpired()) {
            if ($session) {
                $session->update(['is_active' => false]);
            }
            $session = UssdSession::create([
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'user_id' => $userId,
                'network' => $network,
                'current_screen' => 'main_menu',
                'last_activity' => Carbon::now(),
                'is_active' => true,
            ]);
        } else {
             $session->updateActivity();
        }

        return $session;
    }

    /**
     * Return timeout response
     */
    private function timeoutResponse(): array
    {
        return [
            'message' => "Session expired. Please dial again to continue.",
            'continue_session' => false
        ];
    }

    /**
     * Return error response
     */
    private function errorResponse(Request $request, string $message): JsonResponse
    {
        return response()->json([
            'USERID' => $request->input('USERID', ''),
            'MSISDN' => $request->input('MSISDN', ''),
            'USERDATA' => $request->input('USERDATA', ''),
            'MSG' => $message . "\n\nDial again to retry.",
            'MSGTYPE' => false
        ]);
    }

    /**
     * Health check endpoint for NALO
     */
    public function healthCheck(): JsonResponse
    {
        return response()->json([
            'status' => 'healthy',
            'service' => 'VoteYourFav NALO USSD Integration',
            'timestamp' => Carbon::now()->toISOString(),
            'version' => '1.0.1',
            'endpoint' => 'https://voteyourfav.com/api/nalo/ussd/webhook'
        ]);
    }

    /**
     * Handle OTP input for mobile money payments
     */
    private function handleOtpInput(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            return $this->showMainMenu($session);
        }

        // Validate OTP format (typically 4-6 digits)
        if (!preg_match('/^\d{4,6}$/', $input)) {
            return [
                'message' => "Invalid code format. Please enter the 4-6 digit SMS code:\n0. Cancel",
                'continue_session' => true
            ];
        }

        $userData = json_decode($session->user_data, true);
        $paymentReference = $userData['payment_reference'] ?? null;

        if (!$paymentReference) {
            return $this->showMainMenu($session);
        }
        
        Log::info('NALO USSD OTP Submission', [
            'payment_reference' => $paymentReference,
            'otp_length' => strlen($input),
            'session_id' => $session->session_id
        ]);

        // Submit OTP to Paystack
        $otpResult = $this->paystackService->submitOtp($paymentReference, $input);

        if (!$otpResult['status']) {
            Log::error('NALO USSD OTP Submission Failed', [
                'payment_reference' => $paymentReference,
                'error' => $otpResult['message'],
                'session_id' => $session->session_id
            ]);

            return [
                'message' => "Invalid verification code. Please try again:\n\nEnter SMS code:\n0. Cancel",
                'continue_session' => true
            ];
        }

        Log::info('NALO USSD OTP Submitted Successfully', [
            'payment_reference' => $paymentReference,
            'paystack_status' => $otpResult['data']['status'] ?? 'unknown',
            'session_id' => $session->session_id
        ]);        // Payment is now processing, check its status immediately
        $session->update(['current_screen' => 'payment_processing']);
        return $this->checkPaymentStatus($session, $paymentReference);
    }    /**
     * Detect mobile money provider from phone number
     */
    private function detectMobileMoneyProvider(string $msisdn): ?string
    {
        // Remove country code if present
        $phone = preg_replace('/^233/', '', $msisdn);
        $phone = preg_replace('/^\+233/', '', $phone);
        
        // Add leading 0 if not present
        if (!str_starts_with($phone, '0')) {
            $phone = '0' . $phone;
        }
        
        // Ghana mobile money prefixes (complete mapping - check 4-digit first, then 3-digit)
        $providers4digit = [
            // MTN 4-digit prefixes
            '0256' => 'mtn', '0257' => 'mtn',
            '0597' => 'mtn', '0598' => 'mtn', '0599' => 'mtn',
        ];
        
        $providers3digit = [
            // MTN 3-digit prefixes
            '024' => 'mtn', '025' => 'mtn', '053' => 'mtn', 
            '054' => 'mtn', '055' => 'mtn', '059' => 'mtn',
            
            // Telecel (formerly Vodafone) prefixes  
            '020' => 'telecel', '050' => 'telecel',
            
            // AirtelTigo prefixes
            '026' => 'airteltigo', '027' => 'airteltigo',
            '056' => 'airteltigo', '057' => 'airteltigo'
        ];
        
        // Check 4-digit prefixes first
        $prefix4 = substr($phone, 0, 4);
        if (isset($providers4digit[$prefix4])) {
            return $providers4digit[$prefix4];
        }
        
        // Fall back to 3-digit prefixes
        $prefix3 = substr($phone, 0, 3);
        return $providers3digit[$prefix3] ?? null;
    }    /**
     * Format phone number for Ghana mobile money providers
     */
    private function formatPhoneForProvider(string $msisdn): string
    {
        // Remove country code and ensure proper format
        $phone = preg_replace('/^233/', '', $msisdn);
        $phone = preg_replace('/^\+233/', '', $phone);
        
        // Add leading 0 if not present
        if (!str_starts_with($phone, '0')) {
            $phone = '0' . $phone;
        }
        
        return $phone;
    }

    private function handleCheckVotes(UssdSession $session, string $input): array
    {
        if ($input === '0') {
            return $this->showMainMenu($session);
        }

        $nomineeCode = strtoupper(trim($input));
        
        // Find nominee by code (searches both nominee_code and slug fields) with votes relationship
        $nominee = NomineeProfile::with(['votes', 'awardCategories'])
            ->where(function($query) use ($nomineeCode) {
                $query->where('nominee_code', $nomineeCode)
                      ->orWhere('slug', $nomineeCode);
            })
            ->first();

        if (!$nominee) {
            return [
                'message' => "Invalid nominee code: {$nomineeCode}\nPlease try again or press 0 to go back.",
                'continue_session' => true
            ];
        }

        $category = $nominee->awardCategories->first();
        $totalVotes = $nominee->votes()->sum('vote_count');
        
        $message = "Nominee: {$nominee->display_name}\n";
        if ($category) {
            $message .= "Category: {$category->name}\n";
        }
        $message .= "Total Votes: " . number_format($totalVotes) . "\n\n";
        $message .= "1. Vote for this nominee\n";
        $message .= "2. Check another nominee\n";
        $message .= "0. Back to menu";

        // Store nominee info in session for voting option
        if ($nominee->canReceiveVotes() && $category) {
            $userData = [
                'nominee' => [
                    'id' => $nominee->id,
                    'name' => $nominee->display_name,
                    'code' => $nomineeCode,
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'price_per_vote' => $category->price_per_vote,
                    'discount_percentage' => $category->discount_percentage,
                    'discount_min_votes' => $category->discount_min_votes
                ]
            ];
            
            $session->update([
                'current_screen' => 'check_votes_menu',
                'user_data' => json_encode($userData)
            ]);
        } else {
            $message = "Nominee: {$nominee->display_name}\n";
            if ($category) {
                $message .= "Category: {$category->name}\n";
            }
            $message .= "Total Votes: " . number_format($totalVotes) . "\n\n";
            $message .= "2. Check another nominee\n";
            $message .= "0. Back to menu";
            
            $session->update(['current_screen' => 'check_votes_menu']);
        }

        return [
            'message' => $message,
            'continue_session' => true
        ];
    }

    private function handleCheckVotesMenu(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                $userData = json_decode($session->user_data, true);
                if (!empty($userData['nominee'])) {
                    $session->update(['current_screen' => 'enter_vote_count']);
                    return [
                        'message' => "Enter number of votes (1-100000):\n0. Back",
                        'continue_session' => true
                    ];
                }
                return $this->showMainMenu($session);
                
            case '2':
                $session->update(['current_screen' => 'check_votes']);
                return [
                    'message' => "Enter nominee code to check votes:\n(e.g., A1B2C or JOHN001)\n\n0. Back to menu",
                    'continue_session' => true
                ];
                
            case '0':
                return $this->showMainMenu($session);
                
            default:
                return [
                    'message' => "Invalid option.\n1. Vote for this nominee\n2. Check another nominee\n0. Back to menu",
                    'continue_session' => true
                ];
        }
    }

    /**
     * Generate a unique email address from phone number to avoid Paystack conflicts
     */
    private function generateUniqueEmailFromPhone($msisdn)
    {
        return "nalo_ussd_{$msisdn}@voteyourfav.com";
    }
}