<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\NomineeProfile;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Database\Eloquent\Collection;
use App\Models\User;

class PaymentController extends Controller
{
    protected PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Handle Paystack webhook
     */
    public function webhook(Request $request): JsonResponse
    {
        // Get the payload and signature
        $payload = $request->getContent();
        $signature = $request->header('x-paystack-signature');

        // Verify the webhook signature
        if (!$this->paystackService->verifyWebhookSignature($payload, $signature)) {
            Log::warning('Invalid webhook signature received');
            return response()->json(['message' => 'Invalid signature'], 400);
        }

        // Decode the payload
        $data = json_decode($payload, true);

        if (!$data) {
            Log::error('Invalid webhook payload received');
            return response()->json(['message' => 'Invalid payload'], 400);
        }

        // Process the webhook
        $processed = $this->paystackService->handleWebhook($data);

        if ($processed) {
            return response()->json(['message' => 'Webhook processed successfully']);
        }

        return response()->json(['message' => 'Webhook processing failed'], 500);
    }

    /**
     * Verify a payment transaction
     */
    public function verify(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string',
        ]);

        $reference = $request->input('reference');

        // Find the payment record
        $payment = Payment::where('reference', $reference)->first();

        if (!$payment) {
            return response()->json([
                'status' => false,
                'message' => 'Payment not found'
            ], 404);
        }

        // If already successful, return success
        if ($payment->isSuccessful()) {
            return response()->json([
                'status' => true,
                'message' => 'Payment already verified',
                'data' => [
                    'reference' => $payment->reference,
                    'amount' => $payment->amount,
                    'status' => $payment->status,
                ]
            ]);
        }

        // Verify with Paystack
        $verification = $this->paystackService->verifyPayment($reference);

        if (!$verification['status']) {
            return response()->json([
                'status' => false,
                'message' => $verification['message']
            ], 400);
        }

        $paystackData = $verification['data'];

        // Check if payment was successful on Paystack
        if ($paystackData['status'] === 'success') {
            // Process the successful payment
            $processed = $this->paystackService->processSuccessfulPayment($payment, $paystackData);

            if ($processed) {
                return response()->json([
                    'status' => true,
                    'message' => 'Payment verified and processed successfully',
                    'data' => [
                        'reference' => $payment->reference,
                        'amount' => $payment->amount,
                        'status' => $payment->fresh()->status,
                    ]
                ]);
            }

            return response()->json([
                'status' => false,
                'message' => 'Payment verification successful but processing failed'
            ], 500);
        }

        // Payment failed on Paystack
        $payment->markAsFailed($paystackData['gateway_response'] ?? 'Payment failed');

        return response()->json([
            'status' => false,
            'message' => 'Payment failed',
            'data' => [
                'reference' => $payment->reference,
                'status' => $payment->fresh()->status,
            ]
        ], 400);
    }

    /**
     * Get payment status
     */
    public function status(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string',
        ]);

        $payment = Payment::where('reference', $request->input('reference'))->first();

        if (!$payment) {
            return response()->json([
                'status' => false,
                'message' => 'Payment not found'
            ], 404);
        }

        return response()->json([
            'status' => true,
            'data' => [
                'reference' => $payment->reference,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'status' => $payment->status,
                'customer_email' => $payment->customer_email,
                'created_at' => $payment->created_at,
                'paid_at' => $payment->paid_at,
            ]
        ]);
    }

    /**
     * Handle payment callback from Paystack
     */
    public function callback(Request $request): Response
    {
        $reference = $request->query('reference');
        $trxref = $request->query('trxref');
        
        // Use reference from query parameter
        $paymentReference = $reference ?: $trxref;
        
        if (!$paymentReference) {
            return Inertia::render('payment/result', [
                'status' => 'error',
                'message' => 'No payment reference provided',
                'reference' => null,
                'nominee' => null,
            ]);
        }

        // Find the payment record
        $payment = Payment::where('reference', $paymentReference)->first();

        if (!$payment) {
            return Inertia::render('payment/result', [
                'status' => 'error',
                'message' => 'Payment record not found',
                'reference' => $paymentReference,
                'nominee' => null,
            ]);
        }

        // If already processed, show success
        if ($payment->isSuccessful()) {
            $nominee = $this->getNomineeFromPayment($payment);
            
            return Inertia::render('payment/result', [
                'status' => 'success',
                'message' => 'Payment successful! Your votes have been counted.',
                'reference' => $payment->reference,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'vote_count' => $payment->metadata['vote_count'] ?? 0,
                'nominee' => $nominee,
            ]);
        }

        // Verify payment with Paystack
        $verification = $this->paystackService->verifyPayment($paymentReference);

        if (!$verification['status']) {
            return Inertia::render('payment/result', [
                'status' => 'error',
                'message' => 'Payment verification failed: ' . $verification['message'],
                'reference' => $paymentReference,
                'nominee' => null,
            ]);
        }

        $paystackData = $verification['data'];

        if ($paystackData['status'] === 'success') {
            // Process the successful payment
            $processed = $this->paystackService->processSuccessfulPayment($payment, $paystackData);

            if ($processed) {
                $nominee = $this->getNomineeFromPayment($payment);
                
                return Inertia::render('payment/result', [
                    'status' => 'success',
                    'message' => 'Payment successful! Your votes have been counted.',
                    'reference' => $payment->reference,
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'vote_count' => $payment->metadata['vote_count'] ?? 0,
                    'nominee' => $nominee,
                ]);
            }

            return Inertia::render('payment/result', [
                'status' => 'error',
                'message' => 'Payment was successful but vote processing failed. Please contact support.',
                'reference' => $payment->reference,
                'nominee' => null,
            ]);
        }

        // Payment failed
        $payment->markAsFailed($paystackData['gateway_response'] ?? 'Payment failed');

        return Inertia::render('payment/result', [
            'status' => 'failed',
            'message' => 'Payment failed: ' . ($paystackData['gateway_response'] ?? 'Unknown error'),
            'reference' => $payment->reference,
            'nominee' => null,
        ]);
    }

    /**
     * Get nominee information from payment metadata
     */
    private function getNomineeFromPayment(Payment $payment): ?array
    {
        if (!isset($payment->metadata['nominee_profile_id'])) {
            return null;
        }

        $nominee = NomineeProfile::with(['awardCategories'])
            ->find($payment->metadata['nominee_profile_id']);

        if (!$nominee) {
            return null;
        }

        // Get the first category (since the payment is associated with one category)
        $firstCategory = $nominee->awardCategories->first();

        return [
            'id' => $nominee->id,
            'slug' => $nominee->slug,
            'display_name' => $nominee->display_name,
            'profile_image' => $nominee->profile_image,
            'total_votes' => $nominee->total_votes,
            'category' => $firstCategory ? [
                'name' => $firstCategory->name,
            ] : null,
        ];
    }

    /**
     * Get Paystack public key for frontend
     */
    public function publicKey(): JsonResponse
    {
        return response()->json([
            'public_key' => $this->paystackService->getPublicKey()
        ]);
    }

    /**
     * Get recent payments with filters
     */
    private function getRecentPayments(User $user, $status = null, $orgId = null): Collection
    {
        $query = Payment::with(['votes.nomineeProfile.awardCategories'])
            ->when($status, fn($q) => $q->where('status', $status));

        if ($user->isSuperAdmin()) {
            $query->when($orgId, function($q) use ($orgId) {
                return $q->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $orgId));
            });
        } else {
            $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
        }

        return $query->latest()->paginate(20);
    }

    /**
     * Export payments for admin users
     */
    public function export(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403);
        }

        $payments = $this->getPaymentsForExport($user);

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // Headers
            fputcsv($file, [
                'Reference',
                'Amount (GHS)',
                'Status', 
                'Nominee',
                'Category',
                'Created At'
            ]);

            foreach ($payments as $payment) {
                $vote = $payment->votes->first();
                $firstCategory = $vote && $vote->nomineeProfile ? $vote->nomineeProfile->awardCategories->first() : null;
                fputcsv($file, [
                    $payment->reference,
                    number_format($payment->amount, 2),
                    ucfirst($payment->status),
                    $vote ? $vote->nomineeProfile->display_name : 'N/A',
                    $firstCategory ? $firstCategory->name : 'N/A',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }
            
            fclose($file);
        };

        $filename = 'payments_export_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Get payments for export
     */
    private function getPaymentsForExport(User $user): Collection
    {
        return $user->isSuperAdmin() ? 
            $this->getAllPaymentsForExport() : 
            $this->getOrganizationPaymentsForExport($user->organization_id);
    }

    private function getAllPaymentsForExport(): Collection
    {
        return Payment::with(['votes.nomineeProfile.awardCategories'])->get();
    }

    private function getOrganizationPaymentsForExport($organizationId): Collection
    {
        return Payment::with(['votes.nomineeProfile.awardCategories'])
            ->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId))
            ->get();
    }

    private function getFilteredPaymentsForAdmin(User $user): Collection
    {
        return $user->isSuperAdmin() ? 
            Payment::with(['votes.nomineeProfile.awardCategories'])->get() :
            Payment::with(['votes.nomineeProfile.awardCategories'])
                ->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id))
                ->get();
    }

    private function getFilteredPaymentsForOrganization($user): Collection 
    {
        return $user->isSuperAdmin() ? 
            Payment::with(['votes.nomineeProfile.awardCategories'])->get() :
            Payment::with(['votes.nomineeProfile.awardCategories'])
                ->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id))
                ->get();
    }
}
