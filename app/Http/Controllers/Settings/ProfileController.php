<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Show the user's profile settings page.
     */
    public function edit(Request $request): Response
    {
        $user = $request->user();
        
        // Load nominee profile if user is a nominee
        if ($user->isNominee()) {
            $user->load('nomineeProfile');
        }
        
        return Inertia::render('settings/profile', [
            'mustVerifyEmail' => $user instanceof MustVerifyEmail,
            'status' => $request->session()->get('status'),
            'user' => $user,
            'nomineeProfile' => $user->isNominee() ? $user->nomineeProfile : null,
        ]);
    }

    /**
     * Update the user's profile settings.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $user = $request->user();
        $validated = $request->validated();
        
        // Update basic user information
        $user->fill([
            'name' => $validated['name'],
            'email' => $validated['email'],
        ]);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();
        
        // Update nominee profile if user is a nominee
        if ($user->isNominee() && $user->nomineeProfile) {
            $profileData = [];
            
            // Handle profile picture upload
            if ($request->hasFile('profile_image')) {
                // Delete old profile image if it exists
                if ($user->nomineeProfile->profile_image) {
                    Storage::disk('public')->delete($user->nomineeProfile->profile_image);
                }
                
                // Store new profile image
                $profileImagePath = $request->file('profile_image')->store('profile-images', 'public');
                $profileData['profile_image'] = $profileImagePath;
            }
            
            // Update other nominee profile fields if provided
            if (isset($validated['display_name'])) {
                $profileData['display_name'] = $validated['display_name'];
            }
            
            if (isset($validated['bio'])) {
                $profileData['bio'] = $validated['bio'];
            }
            
            if (isset($validated['achievements'])) {
                $profileData['achievements'] = $validated['achievements'];
            }
            
            if (isset($validated['why_vote_for_me'])) {
                $profileData['why_vote_for_me'] = $validated['why_vote_for_me'];
            }
            
            if (isset($validated['social_links'])) {
                $profileData['social_links'] = $validated['social_links'];
            }
            
            // Update nominee profile if there's data to update
            if (!empty($profileData)) {
                $user->nomineeProfile->update($profileData);
            }
        }

        return to_route('profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        // Delete profile image if it exists
        if ($user->isNominee() && $user->nomineeProfile && $user->nomineeProfile->profile_image) {
            Storage::disk('public')->delete($user->nomineeProfile->profile_image);
        }

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
