<?php

namespace App\Http\Controllers;

use App\Models\NomineeProfile;
use App\Models\Payment;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class VotingController extends Controller
{
    protected PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Show all available categories for voting.
     */
    public function categories(): Response
    {
        $categories = \App\Models\AwardCategory::with(['organization'])
            ->withCount([
                'nomineeProfiles as nominees_count' => function($query) {
                    $query->approved();
                }
            ])
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->latest()
            ->get()
            ->map(function ($category) {
                // Calculate votes count for approved nominees only (consistent with admin)
                $approvedNomineeProfileIds = $category->nomineeProfiles()
                    ->approved()
                    ->pluck('nominee_profiles.id');
                    
                $votesCount = \App\Models\Vote::whereIn('nominee_profile_id', $approvedNomineeProfileIds)->count();
                
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'slug' => $category->slug,
                    'organization_name' => $category->organization ? $category->organization->name : 'N/A',
                    'price_per_vote' => $category->price_per_vote,
                    'voting_start_date' => $category->voting_start_date,
                    'voting_end_date' => $category->voting_end_date,
                    'is_voting_active' => $category->isVotingActive(),
                    'nominees_count' => $category->nominees_count,
                    'votes_count' => $votesCount,
                ];
            });

        return Inertia::render('voting/categories', [
            'categories' => $categories,
            'stats' => [
                'total_categories' => $categories->count(),
                'active_categories' => $categories->filter(fn($c) => $c['is_voting_active'])->count(),
                'total_votes' => $categories->sum('votes_count'),
            ]
        ]);
    }

    /**
     * Show nominees in a specific category for voting.
     */
    public function showCategory(string $slug): Response
    {
        $category = \App\Models\AwardCategory::with(['organization'])
            ->where('slug', $slug)
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->firstOrFail();

        // Check if voting is active for this category
        if (!$category->isVotingActive()) {
            return Inertia::render('voting/category-not-available', [
                'category' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'organization_name' => $category->organization->name,
                    'voting_start_date' => $category->voting_start_date,
                    'voting_end_date' => $category->voting_end_date,
                ],
                'message' => 'Voting is not currently active for this category.',
                'reason' => 'voting_not_active'
            ]);
        }

        // Get all approved nominees in this category, sorted by votes
        // Eager load awardCategories to prevent N+1 queries when calling canReceiveVotes()
        $nominees = $category->nomineeProfiles()
            ->approved()
            ->byVotes()
            ->with('awardCategories')
            ->get()
            ->map(function ($nominee, $index) {
                return [
                    'id' => $nominee->id,
                    'slug' => $nominee->slug,
                    'display_name' => $nominee->display_name,
                    'bio' => $nominee->bio,
                    'profile_image' => $nominee->getRawOriginal('profile_image') ? Storage::url($nominee->getRawOriginal('profile_image')) : null,
                    'total_votes' => $nominee->total_votes,
                    'ranking' => $index + 1,
                    'can_receive_votes' => $nominee->canReceiveVotes(),
                ];
            });

        return Inertia::render('voting/category', [
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'description' => $category->description,
                'slug' => $category->slug,
                'price_per_vote' => $category->price_per_vote,
                'discount_percentage' => $category->discount_percentage,
                'discount_min_votes' => $category->discount_min_votes,
                'voting_start_date' => $category->voting_start_date,
                'voting_end_date' => $category->voting_end_date,
                'organization_name' => $category->organization->name,
                'is_voting_active' => $category->isVotingActive(),
            ],
            'nominees' => $nominees,
            'stats' => [
                'total_nominees' => $nominees->count(),
                'total_votes' => $nominees->sum('total_votes'),
                'top_nominee' => $nominees->first(),
            ]
        ]);
    }

    /**
     * Show the voting page for a nominee.
     */
    public function show(string $slug): Response
    {
        $nominee = NomineeProfile::with(['awardCategories', 'user'])
            ->where('slug', $slug)
            ->approved()
            ->firstOrFail();

        Log::info('Loading nominee for voting page: ' . $slug);
        Log::info('Nominee ID: ' . $nominee->id);
        Log::info('Nominee Award Categories: ' . $nominee->awardCategories->pluck('name')->implode(', '));

        // Get the first category for voting purposes
        $category = $nominee->awardCategories->first();
        
        if (!$category) {
            return Inertia::render('voting/not-available', [
                'nominee' => [
                    'id' => $nominee->id,
                    'slug' => $nominee->slug,
                    'display_name' => $nominee->display_name,
                    'bio' => $nominee->bio,
                    'profile_image' => $nominee->profile_image,
                ],
                'message' => 'This nominee is not currently assigned to any voting category.',
                'reason' => 'category_not_assigned'
            ]);
        }

        // Calculate ranking within the category
        $ranking = $category->nomineeProfiles()
            ->approved()
            ->where('total_votes', '>', $nominee->total_votes)
            ->count() + 1;

        // Increment link clicks
        $nominee->incrementLinkClicks();

        // Get other nominees in the same category
        // Eager load awardCategories to prevent N+1 queries when mapping response data
        $otherNominees = $category
            ->nomineeProfiles()
            ->approved()
            ->byVotes()
            ->with('awardCategories')
            ->where('nominee_profiles.id', '!=', $nominee->id)
            ->limit(5)
            ->get();

        // Generate SEO metadata
        $profileImageUrl = $nominee->getRawOriginal('profile_image') ? Storage::url($nominee->getRawOriginal('profile_image')) : null;
        $absoluteImageUrl = $profileImageUrl ? url($profileImageUrl) : url('/voteyourfav_logo.png');
        
        $seoData = [
            'title' => "{$nominee->display_name} - Vote on VoteYourFav",
            'description' => "Vote for {$nominee->display_name} in the {$category->name} category. Currently ranked #{$ranking} with " . number_format($nominee->total_votes) . " votes. Support your favorite nominee on VoteYourFav's secure voting platform.",
            'keywords' => "vote, voting, award, {$nominee->display_name}, {$category->name}, nomination, Ghana, competition, contest, VoteYourFav",
            'og_title' => "Vote for {$nominee->display_name} - {$category->name}",
            'og_description' => "Support {$nominee->display_name} in the {$category->name} category. Ranked #{$ranking} with " . number_format($nominee->total_votes) . " votes. Vote securely on VoteYourFav.",
            'og_image' => $absoluteImageUrl,
            'og_url' => route('vote.show', $nominee->slug),
            'twitter_title' => "Vote for {$nominee->display_name} - {$category->name}",
            'twitter_description' => "Support {$nominee->display_name} in the {$category->name} category. Currently ranked #{$ranking} with " . number_format($nominee->total_votes) . " votes.",
            'twitter_image' => $absoluteImageUrl,
            'canonical_url' => route('vote.show', $nominee->slug),
        ];

        return Inertia::render('voting/show', [
            'nominee' => [
                'id' => $nominee->id,
                'slug' => $nominee->slug,
                'display_name' => $nominee->display_name,
                'bio' => $nominee->bio,
                'profile_image' => $nominee->getRawOriginal('profile_image') ? Storage::url($nominee->getRawOriginal('profile_image')) : null,
                'additional_images' => $nominee->additional_images,
                'social_links' => $nominee->social_links,
                'achievements' => $nominee->achievements,
                'why_vote_for_me' => $nominee->why_vote_for_me,
                'total_votes' => $nominee->total_votes,
                'ranking' => $ranking,
                'can_receive_votes' => $nominee->canReceiveVotes(),
            ],
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'description' => $category->description,
                'price_per_vote' => $category->price_per_vote,
                'discount_percentage' => $category->discount_percentage,
                'discount_min_votes' => $category->discount_min_votes,
                'voting_start_date' => $category->voting_start_date,
                'voting_end_date' => $category->voting_end_date,
                'is_voting_active' => $category->isVotingActive(),
            ],
            'other_nominees' => $otherNominees->map(function ($other) {
                return [
                    'slug' => $other->slug,
                    'display_name' => $other->display_name,
                    'profile_image' => $other->profile_image,
                    'total_votes' => $other->total_votes,
                ];
            }),
            'paystack_public_key' => $this->paystackService->getPublicKey(),
            'seo' => $seoData,
        ]);
    }

    /**
     * Calculate the price for voting.
     */
    public function calculatePrice(Request $request, string $slug)
    {
        $nominee = NomineeProfile::with('awardCategories')->where('slug', $slug)->approved()->firstOrFail();
        
        $category = $nominee->awardCategories->first();
        if (!$category) {
            return response()->json(['error' => 'Nominee is not assigned to any category.'], 400);
        }
        
        $request->validate([
            'vote_count' => 'required|integer|min:1|max:100000',
        ]);

        $voteCount = $request->input('vote_count');
        $pricing = $category->calculatePrice($voteCount);

        return response()->json([
            'vote_count' => $voteCount,
            'base_price' => $pricing['base_price'],
            'discount' => $pricing['discount'],
            'paystack_surcharge' => $pricing['paystack_surcharge'],
            'final_price' => $pricing['final_price'],
            'discount_percentage' => $pricing['discount_percentage'],
            'currency' => 'GHS',
        ]);
    }

    /**
     * Initiate payment for votes.
     */
    public function initiatePayment(Request $request, string $slug)
    {
        $nominee = NomineeProfile::with('awardCategories')->where('slug', $slug)->approved()->firstOrFail();

        $category = $nominee->awardCategories->first();
        if (!$category) {
            return response()->json(['error' => 'Nominee is not assigned to any category.'], 400);
        }

        if (!$nominee->canReceiveVotes()) {
            return response()->json([
                'error' => 'Voting is not currently active for this nominee.'
            ], 400);
        }

        $request->validate([
            'vote_count' => 'required|integer|min:1|max:100000',
            'voter_name' => 'nullable|string|max:255',
            'voter_email' => 'required|email|max:255',
            'voter_phone' => 'nullable|string|max:20',
        ]);

        $voteCount = $request->input('vote_count');
        $pricing = $category->calculatePrice($voteCount);

        // Create payment record
        $payment = Payment::create([
            'reference' => 'VTC_' . Str::upper(Str::random(10)),
            'amount' => $pricing['final_price'],
            'currency' => 'GHS',
            'status' => Payment::STATUS_PENDING,
            'customer_email' => $request->input('voter_email'),
            'customer_name' => $request->input('voter_name'),
            'customer_phone' => $request->input('voter_phone'),
            'metadata' => [
                'nominee_profile_id' => $nominee->id,
                'vote_count' => $voteCount,
                'discount_applied' => $pricing['discount_percentage'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ],
        ]);

        // Initialize payment with Paystack
        $paystackResponse = $this->paystackService->initializePayment([
            'email' => $payment->customer_email,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
            'reference' => $payment->reference,
            'callback_url' => route('payment.callback'),
            'metadata' => [
                'nominee_name' => $nominee->display_name,
                'category_name' => $category->name,
                'vote_count' => $voteCount,
                'custom_fields' => [
                    [
                        'display_name' => 'Nominee',
                        'variable_name' => 'nominee',
                        'value' => $nominee->display_name
                    ],
                    [
                        'display_name' => 'Category',
                        'variable_name' => 'category',
                        'value' => $category->name
                    ],
                    [
                        'display_name' => 'Votes',
                        'variable_name' => 'votes',
                        'value' => $voteCount
                    ]
                ]
            ]
        ]);

        if (!$paystackResponse['status']) {
            return response()->json([
                'error' => $paystackResponse['message']
            ], 500);
        }

        return response()->json([
            'status' => true,
            'message' => 'Payment initialized successfully',
            'data' => [
                'payment_reference' => $payment->reference,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'authorization_url' => $paystackResponse['data']['authorization_url'],
                'access_code' => $paystackResponse['data']['access_code'],
                'paystack_public_key' => $this->paystackService->getPublicKey(),
            ]
        ]);
    }

    /**
     * Show the general voting form where users can enter nominee codes.
     */
    public function voteByCode(): Response
    {
        return Inertia::render('voting/nominee-form', [
            'seo' => [
                'title' => 'Vote for Your Favorite Nominee - VoteYourFav',
                'description' => 'Enter a nominee code to vote for your favorite nominee. Support your favorite contestants in various award categories.',
                'keywords' => 'vote, voting, nominee code, award, nomination, Ghana, competition, contest, VoteYourFav',
                'og_title' => 'Vote for Your Favorite Nominee - VoteYourFav',
                'og_description' => 'Enter a nominee code to vote for your favorite nominee. Support your favorite contestants in various award categories.',
                'canonical_url' => route('vote.nominee.form'),
            ]
        ]);
    }

    /**
     * Search for a nominee by their code (slug or nominee_code) and redirect to their voting page.
     */
    public function searchNominee(Request $request)
    {
        $request->validate([
            'nominee_code' => ['required', 'string', 'max:50', 'min:3'],
        ], [
            'nominee_code.required' => 'Please enter a nominee code.',
            'nominee_code.min' => 'Nominee code must be at least 3 characters long.',
            'nominee_code.max' => 'Nominee code must not exceed 50 characters.',
        ]);

        $code = trim($request->nominee_code);

        // Log the search attempt for debugging
        Log::info('Nominee search attempt', ['code' => $code, 'ip' => $request->ip()]);

        // Use the findByCode method from NomineeProfile model
        $nominee = NomineeProfile::findByCode($code);

        // Check if nominee exists and is approved
        if (!$nominee || !$nominee->is_approved) {
            Log::info('Nominee not found or not approved', ['code' => $code, 'found' => $nominee ? true : false]);
            
            return back()->withErrors([
                'nominee_code' => 'No active nominee found with the code "' . $code . '". Please check the code and try again.'
            ])->withInput();
        }

        // Check if the nominee can receive votes (has active categories)
        $activeCategory = $nominee->awardCategories()
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->first();

        if (!$activeCategory) {
            return back()->withErrors([
                'nominee_code' => 'This nominee is not currently available for voting. Please try again later.'
            ])->withInput();
        }

        Log::info('Nominee found and redirecting', ['nominee_id' => $nominee->id, 'slug' => $nominee->slug]);

        // Redirect to the nominee's voting page
        return redirect()->route('vote.show', $nominee->slug);
    }
}
