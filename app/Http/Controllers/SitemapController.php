<?php

namespace App\Http\Controllers;

use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;

class SitemapController extends Controller
{
    public function index(): Response
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Homepage
        $sitemap .= $this->addUrl(
            route('welcome'),
            now()->toISOString(),
            'daily',
            '1.0'
        );

        // Voting categories page
        $sitemap .= $this->addUrl(
            route('vote.categories'),
            now()->toISOString(),
            'daily',
            '0.9'
        );

        // Individual category pages
        $categories = AwardCategory::where('is_active', true)
            ->where('is_suspended', false)
            ->get();

        foreach ($categories as $category) {
            $sitemap .= $this->addUrl(
                route('vote.showCategory', $category->slug),
                $category->updated_at->toISOString(),
                'daily',
                '0.8'
            );
        }

        // Individual nominee voting pages
        $nominees = NomineeProfile::with('awardCategories')
            ->approved()
            ->whereHas('awardCategories', function($query) {
                $query->where('is_active', true)
                      ->where('is_suspended', false);
            })
            ->get();

        foreach ($nominees as $nominee) {
            $sitemap .= $this->addUrl(
                route('vote.show', $nominee->slug),
                $nominee->updated_at->toISOString(),
                'weekly',
                '0.7'
            );
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    private function addUrl(string $url, string $lastmod, string $changefreq, string $priority): string
    {
        return "  <url>\n" .
               "    <loc>" . htmlspecialchars($url) . "</loc>\n" .
               "    <lastmod>{$lastmod}</lastmod>\n" .
               "    <changefreq>{$changefreq}</changefreq>\n" .
               "    <priority>{$priority}</priority>\n" .
               "  </url>\n";
    }
}
