<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;

class RobotsController extends Controller
{
    public function index(): Response
    {
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Allow: /vote/*\n";
        $robots .= "Allow: /category/*\n\n";
        
        $robots .= "# Block sensitive/private areas\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /api/\n";
        $robots .= "Disallow: /profile/\n";
        $robots .= "Disallow: /settings/\n";
        $robots .= "Disallow: /dashboard/\n";
        $robots .= "Disallow: /payment/\n";
        $robots .= "Disallow: /checkout/\n";
        $robots .= "Disallow: /login\n";
        $robots .= "Disallow: /register\n";
        $robots .= "Disallow: /forgot-password\n";
        $robots .= "Disallow: /reset-password\n\n";
        
        $robots .= "# Block Laravel specific paths\n";
        $robots .= "Disallow: /storage/\n";
        $robots .= "Disallow: /vendor/\n";
        $robots .= "Disallow: /_debugbar/\n\n";
        
        $robots .= "# Crawl delay to prevent server overload during peak voting\n";
        $robots .= "Crawl-delay: 1\n\n";
        
        $robots .= "# Dynamic sitemap - automatically updated with new content\n";
        $robots .= "Sitemap: " . config('app.url') . "/sitemap.xml\n";

        return response($robots, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400', // Cache for 24 hours
        ]);
    }
}
