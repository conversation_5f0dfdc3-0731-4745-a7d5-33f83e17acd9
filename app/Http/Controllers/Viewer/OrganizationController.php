<?php

namespace App\Http\Controllers\Viewer;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class OrganizationController extends Controller
{
    /**
     * Display a listing of organizations (read-only).
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search');
        $status = $request->get('status');

        $query = Organization::with(['awardCategories', 'users'])
            ->withCount(['awardCategories', 'users']);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($status === 'active') {
            $query->where('is_active', true);
        } elseif ($status === 'inactive') {
            $query->where('is_active', false);
        }

        $organizations = $query->latest()
            ->paginate(15)
            ->through(function ($org) {
                return [
                    'id' => $org->id,
                    'name' => $org->name,
                    'slug' => $org->slug,
                    'description' => $org->description,
                    'is_active' => $org->is_active,
                    'award_categories_count' => $org->award_categories_count,
                    'users_count' => $org->users_count,
                    'created_at' => $org->created_at->format('M d, Y'),
                ];
            });

        $stats = [
            'total' => Organization::count(),
            'active' => Organization::where('is_active', true)->count(),
            'inactive' => Organization::where('is_active', false)->count(),
        ];

        return Inertia::render('viewer/organizations/index', [
            'organizations' => $organizations,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'status' => $status,
            ],
        ]);
    }

    /**
     * Display the specified organization (read-only).
     */
    public function show(Organization $organization): Response
    {
        $organization->load([
            'awardCategories' => function($query) {
                $query->withCount(['nomineeProfiles']);
            },
            'users' => function($query) {
                $query->withCount(['nomineeProfile']);
            }
        ]);

        $organizationData = [
            'id' => $organization->id,
            'name' => $organization->name,
            'slug' => $organization->slug,
            'description' => $organization->description,
            'website' => $organization->website,
            'contact_email' => $organization->contact_email,
            'phone' => $organization->phone,
            'is_active' => $organization->is_active,
            'created_at' => $organization->created_at->format('M d, Y'),
            'updated_at' => $organization->updated_at->format('M d, Y'),
        ];

        $categories = $organization->awardCategories->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'is_active' => $category->is_active,
                'is_suspended' => $category->is_suspended,
                'voting_status' => $category->isVotingActive() ? 'Active' : 'Inactive',
                'nominee_profiles_count' => $category->nominee_profiles_count,
                'price_per_vote' => $category->price_per_vote,
            ];
        });

        $users = $organization->users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'slug' => $user->slug,
                'email' => $user->email,
                'role' => $user->role,
                'is_suspended' => $user->is_suspended,
                'approval_status' => $user->approval_status,
                'nominee_profile_count' => $user->nominee_profile_count ?? 0,
                'created_at' => $user->created_at->format('M d, Y'),
            ];
        });

        return Inertia::render('viewer/organizations/show', [
            'organization' => $organizationData,
            'categories' => $categories,
            'users' => $users,
        ]);
    }
}
