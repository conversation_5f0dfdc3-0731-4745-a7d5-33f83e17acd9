<?php

namespace App\Http\Controllers\Viewer;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\User;
use App\Models\NomineeProfile;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the viewer dashboard with read-only statistics.
     */
    public function index(): Response
    {
        // Get basic statistics
        $stats = [
            'total_organizations' => Organization::count(),
            'active_organizations' => Organization::where('is_active', true)->count(),
            'total_categories' => AwardCategory::count(),
            'active_categories' => AwardCategory::where('is_active', true)->where('is_suspended', false)->count(),
            'total_users' => User::count(),
            'total_admins' => User::where('role', User::ROLE_ADMIN)->count(),
            'total_nominees' => User::where('role', User::ROLE_NOMINEE)->count(),
            'total_viewers' => User::where('role', User::ROLE_VIEWER)->count(),
            'approved_nominees' => NomineeProfile::where('is_approved', true)->count(),
            'pending_nominees' => NomineeProfile::where('is_approved', false)->count(),
        ];

        // Get recent organizations
        $recentOrganizations = Organization::with(['awardCategories', 'users'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($org) {
                return [
                    'id' => $org->id,
                    'name' => $org->name,
                    'slug' => $org->slug,
                    'is_active' => $org->is_active,
                    'categories_count' => $org->awardCategories->count(),
                    'users_count' => $org->users->count(),
                    'created_at' => $org->created_at->format('M d, Y'),
                ];
            });

        // Get recent categories
        $recentCategories = AwardCategory::with(['organization'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'organization_name' => $category->organization->name ?? 'N/A',
                    'is_active' => $category->is_active,
                    'is_suspended' => $category->is_suspended,
                    'voting_status' => $category->isVotingActive() ? 'Active' : 'Inactive',
                    'created_at' => $category->created_at->format('M d, Y'),
                ];
            });

        return Inertia::render('viewer/dashboard', [
            'stats' => $stats,
            'recent_organizations' => $recentOrganizations,
            'recent_categories' => $recentCategories,
        ]);
    }
}
