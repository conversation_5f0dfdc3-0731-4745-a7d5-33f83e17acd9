<?php

namespace App\Http\Controllers\Viewer;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    /**
     * Display a listing of users (read-only).
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search');
        $role = $request->get('role');
        $status = $request->get('status');
        $organizationId = $request->get('organization_id');

        $query = User::with(['organization', 'nomineeProfile'])
            ->withCount(['nomineeProfile']);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($role && $role !== 'all') {
            $query->where('role', $role);
        }

        if ($status === 'active') {
            $query->where('is_suspended', false);
        } elseif ($status === 'suspended') {
            $query->where('is_suspended', true);
        } elseif ($status === 'approved') {
            $query->where('approval_status', 'approved');
        } elseif ($status === 'pending') {
            $query->where('approval_status', 'pending');
        }

        if ($organizationId && $organizationId !== 'all') {
            $query->where('organization_id', $organizationId);
        }

        $users = $query->latest()
            ->paginate(15)
            ->through(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'slug' => $user->slug,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'role' => $user->role,
                    'is_suspended' => $user->is_suspended,
                    'approval_status' => $user->approval_status,
                    'organization_name' => $user->organization->name ?? 'N/A',
                    'nominee_profile_count' => $user->nominee_profile_count ?? 0,
                    'created_at' => $user->created_at->format('M d, Y'),
                ];
            });

        // Get organizations for filter dropdown
        $organizations = \App\Models\Organization::select('id', 'name')
            ->orderBy('name')
            ->get();

        $roles = [
            ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
            ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ['value' => User::ROLE_VIEWER, 'label' => 'Viewer'],
        ];

        $stats = [
            'total' => User::count(),
            'admins' => User::where('role', User::ROLE_ADMIN)->count(),
            'nominees' => User::where('role', User::ROLE_NOMINEE)->count(),
            'viewers' => User::where('role', User::ROLE_VIEWER)->count(),
            'suspended' => User::where('is_suspended', true)->count(),
            'pending_approval' => User::where('approval_status', 'pending')->count(),
        ];

        return Inertia::render('viewer/users/index', [
            'users' => $users,
            'organizations' => $organizations,
            'roles' => $roles,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'role' => $role,
                'status' => $status,
                'organization_id' => $organizationId,
            ],
        ]);
    }

    /**
     * Display the specified user (read-only).
     */
    public function show(User $user): Response
    {
        $user->load(['organization', 'nomineeProfile.awardCategories']);

        $userData = [
            'id' => $user->id,
            'name' => $user->name,
            'slug' => $user->slug,
            'email' => $user->email,
            'phone' => $user->phone,
            'role' => $user->role,
            'is_suspended' => $user->is_suspended,
            'suspension_reason' => $user->suspension_reason,
            'approval_status' => $user->approval_status,
            'approved_at' => $user->approved_at?->format('M d, Y g:i A'),
            'rejection_reason' => $user->rejection_reason,
            'organization_name' => $user->organization->name ?? 'N/A',
            'created_at' => $user->created_at->format('M d, Y g:i A'),
            'updated_at' => $user->updated_at->format('M d, Y g:i A'),
        ];

        $nomineeProfile = null;
        if ($user->nomineeProfile) {
            $profile = $user->nomineeProfile;
            $nomineeProfile = [
                'id' => $profile->id,
                'display_name' => $profile->display_name,
                'slug' => $profile->slug,
                'nominee_code' => $profile->nominee_code,
                'bio' => $profile->bio,
                'profile_image' => $profile->profile_image,
                'is_approved' => $profile->is_approved,
                'total_votes' => $profile->total_votes,
                'achievements' => $profile->achievements,
                'why_vote_for_me' => $profile->why_vote_for_me,
                'social_links' => $profile->social_links,
                'categories' => $profile->awardCategories->map(function ($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'slug' => $category->slug,
                        'is_active' => $category->is_active,
                        'voting_status' => $category->isVotingActive() ? 'Active' : 'Inactive',
                    ];
                }),
                'created_at' => $profile->created_at->format('M d, Y'),
            ];
        }

        return Inertia::render('viewer/users/show', [
            'user' => $userData,
            'nominee_profile' => $nomineeProfile,
        ]);
    }
}
