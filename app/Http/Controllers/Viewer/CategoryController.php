<?php

namespace App\Http\Controllers\Viewer;

use App\Http\Controllers\Controller;
use App\Models\AwardCategory;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CategoryController extends Controller
{
    /**
     * Display a listing of award categories (read-only).
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $organizationId = $request->get('organization_id');

        $query = AwardCategory::with(['organization'])
            ->withCount(['nomineeProfiles']);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($status === 'active') {
            $query->where('is_active', true)->where('is_suspended', false);
        } elseif ($status === 'inactive') {
            $query->where(function($q) {
                $q->where('is_active', false)->orWhere('is_suspended', true);
            });
        }

        if ($organizationId && $organizationId !== 'all') {
            $query->where('organization_id', $organizationId);
        }

        $categories = $query->latest()
            ->paginate(15)
            ->through(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'organization_name' => $category->organization->name ?? 'N/A',
                    'is_active' => $category->is_active,
                    'is_suspended' => $category->is_suspended,
                    'voting_status' => $category->isVotingActive() ? 'Active' : 'Inactive',
                    'nominee_profiles_count' => $category->nominee_profiles_count,
                    'price_per_vote' => $category->price_per_vote,
                    'voting_start_date' => $category->voting_start_date?->format('M d, Y'),
                    'voting_end_date' => $category->voting_end_date?->format('M d, Y'),
                    'created_at' => $category->created_at->format('M d, Y'),
                ];
            });

        // Get organizations for filter dropdown
        $organizations = \App\Models\Organization::select('id', 'name')
            ->orderBy('name')
            ->get();

        $stats = [
            'total' => AwardCategory::count(),
            'active' => AwardCategory::where('is_active', true)->where('is_suspended', false)->count(),
            'inactive' => AwardCategory::where('is_active', false)->orWhere('is_suspended', true)->count(),
        ];

        return Inertia::render('viewer/categories/index', [
            'categories' => $categories,
            'organizations' => $organizations,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'organization_id' => $organizationId,
            ],
        ]);
    }

    /**
     * Display the specified award category (read-only).
     */
    public function show(AwardCategory $category): Response
    {
        $category->load([
            'organization',
            'nomineeProfiles' => function($query) {
                $query->with(['user'])->approved();
            }
        ]);

        $categoryData = [
            'id' => $category->id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description,
            'organization_name' => $category->organization->name ?? 'N/A',
            'is_active' => $category->is_active,
            'is_suspended' => $category->is_suspended,
            'voting_status' => $category->isVotingActive() ? 'Active' : 'Inactive',
            'price_per_vote' => $category->price_per_vote,
            'discount_percentage' => $category->discount_percentage,
            'discount_min_votes' => $category->discount_min_votes,
            'voting_start_date' => $category->voting_start_date?->format('M d, Y g:i A'),
            'voting_end_date' => $category->voting_end_date?->format('M d, Y g:i A'),
            'created_at' => $category->created_at->format('M d, Y'),
            'updated_at' => $category->updated_at->format('M d, Y'),
        ];

        $nominees = $category->nomineeProfiles->map(function ($nominee) {
            return [
                'id' => $nominee->id,
                'display_name' => $nominee->display_name,
                'slug' => $nominee->slug,
                'nominee_code' => $nominee->nominee_code,
                'bio' => $nominee->bio,
                'profile_image' => $nominee->profile_image,
                'total_votes' => $nominee->total_votes,
                'is_approved' => $nominee->is_approved,
                'user_name' => $nominee->user->name ?? 'N/A',
                'user_email' => $nominee->user->email ?? 'N/A',
                'created_at' => $nominee->created_at->format('M d, Y'),
            ];
        });

        return Inertia::render('viewer/categories/show', [
            'category' => $categoryData,
            'nominees' => $nominees,
        ]);
    }
}
