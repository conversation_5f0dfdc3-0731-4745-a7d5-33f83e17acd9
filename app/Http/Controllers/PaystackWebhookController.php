<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\PaystackUssdService;
use App\Models\UssdSession;
use App\Models\Payment;
use App\Models\Vote;
use App\Models\NomineeProfile;

class PaystackWebhookController extends Controller
{
    protected $paystackService;

    public function __construct(PaystackUssdService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Handle Paystack webhook events
     */
    public function handle(Request $request)
    {
        Log::info('Paystack Webhook Received', [
            'headers' => $request->headers->all(),
            'body' => $request->all(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // Get the signature for verification
        $signature = $request->header('X-Paystack-Signature') ?? $request->header('x-paystack-signature');
        $body = $request->getContent();
        
        // Verify webhook signature if secret is configured
        $webhookSecret = config('services.paystack.secret_key');
        if ($webhookSecret && $webhookSecret !== 'null') {
            $computedSignature = hash_hmac('sha512', $body, $webhookSecret);
            
            // Check if signature exists and is valid
            if (!$signature) {
                Log::warning('Paystack Webhook Missing Signature', [
                    'environment' => app()->environment(),
                    'body_length' => strlen($body)
                ]);
                
                // In production, reject missing signatures
                if (app()->environment('production')) {
                    return response('Missing signature', 400);
                }
                // In development, log warning but continue processing
                Log::info('Development environment: Processing webhook despite missing signature');
            } elseif (!hash_equals($signature, $computedSignature)) {
                Log::warning('Paystack Webhook Signature Mismatch', [
                    'received_signature' => $signature,
                    'computed_signature' => $computedSignature,
                    'environment' => app()->environment()
                ]);
                
                // In production, reject invalid signatures
                if (app()->environment('production')) {
                    return response('Invalid signature', 400);
                }
                // In development, log warning but continue processing
                Log::info('Development environment: Processing webhook despite signature mismatch');
            } else {
                Log::info('Webhook signature verified successfully');
            }
        } else {
            Log::warning('No webhook secret configured - webhook not verified');
        }

        $payload = $request->all();
        $event = $payload['event'] ?? null;
        $data = $payload['data'] ?? [];

        Log::info('Processing Paystack Event', [
            'event' => $event,
            'reference' => $data['reference'] ?? null,
            'status' => $data['status'] ?? null,
            'channel' => $data['channel'] ?? null,
            'gateway_response' => $data['gateway_response'] ?? null
        ]);

        // Handle successful charges (including mobile money)
        if ($event === 'charge.success') {
            return $this->handleChargeSuccess($data);
        }

        // Handle failed charges
        if ($event === 'charge.failed') {
            Log::warning('Paystack Charge Failed', [
                'reference' => $data['reference'] ?? null,
                'gateway_response' => $data['gateway_response'] ?? null,
                'customer' => $data['customer'] ?? null
            ]);
        }

        Log::info('Paystack Webhook Event Not Handled', ['event' => $event]);
        return response('OK', 200);
    }

    /**
     * Handle successful charge
     */
    private function handleChargeSuccess($data)
    {
        $reference = $data['reference'] ?? null;
        $status = $data['status'] ?? null;
        $channel = $data['channel'] ?? null;

        if (!$reference) {
            Log::error('No reference found in successful charge');
            return response('No reference', 400);
        }

        if ($status !== 'success') {
            Log::warning('Charge marked as success but status is not success', [
                'reference' => $reference,
                'status' => $status
            ]);
            return response('Invalid status', 400);
        }

        // Check if payment already exists
        $payment = \App\Models\Payment::where('reference', $reference)->first();
        
        if (!$payment) {
            Log::error('Payment not found', [
                'reference' => $reference,
                'all_payments_count' => \App\Models\Payment::count(),
                'recent_payments' => \App\Models\Payment::latest()->take(5)->pluck('reference', 'id')->toArray()
            ]);
            return response('Payment not found', 404);
        }

        // Check if payment is already processed
        if ($payment->status === 'success') {
            Log::info('Payment already processed', ['reference' => $reference]);
            return response('Already processed', 200);
        }

        // Update payment status
        try {
            $payment->update([
                'status' => 'success',
                'paid_at' => now(),
                'paystack_reference' => $data['reference'] ?? null,
                'gateway_response' => json_encode($data)
            ]);
            
            Log::info('Payment updated successfully', [
                'payment_id' => $payment->id,
                'reference' => $reference,
                'old_status' => $payment->getOriginal('status'),
                'new_status' => 'success'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update payment', [
                'payment_id' => $payment->id,
                'reference' => $reference,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        Log::info('Payment marked as completed', [
            'reference' => $reference,
            'payment_id' => $payment->id,
            'channel' => $channel
        ]);

        // Get metadata for vote creation
        $metadata = $data['metadata'] ?? [];
        if (empty($metadata) && isset($data['meta'])) {
            $metadata = $data['meta'];
        }

        // Create vote record
        try {
            $this->createVoteFromPayment($payment, $data, $metadata);
            Log::info('Vote created successfully', ['payment_id' => $payment->id]);
        } catch (\Exception $e) {
            Log::error('Failed to create vote', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return response('Processed successfully', 200);
    }

    private function createVoteFromPayment($payment, $paymentData, $metadata)
    {
        // Find nominee by ID from metadata or payment metadata
        $nomineeId = $metadata['nominee_id'] ?? 
                    $metadata['nominee_profile_id'] ?? 
                    $payment->metadata['nominee_profile_id'] ?? 
                    null;

        if (!$nomineeId) {
            Log::error('No nominee ID found in payment or metadata', [
                'payment_id' => $payment->id,
                'metadata' => $metadata
            ]);
            throw new \Exception('Nominee ID not found');
        }

        $nominee = \App\Models\NomineeProfile::find($nomineeId);
        if (!$nominee) {
            Log::error('Nominee not found', ['nominee_id' => $nomineeId]);
            throw new \Exception('Nominee not found');
        }

        // Calculate vote count - get from metadata first, then calculate from amount
        $voteCount = $metadata['vote_count'] ?? 
                    $metadata['votes_requested'] ?? 
                    $payment->metadata['votes_requested'] ?? 
                    $this->calculateVoteCount($payment->amount, $metadata);

        // Detect payment method/channel
        $voteMethod = $this->detectVoteMethod($paymentData, $metadata, $payment);

        // Check for duplicate votes
        $existingVote = \App\Models\Vote::where('payment_id', $payment->id)->first();
        if ($existingVote) {
            Log::info('Vote already exists for payment', [
                'payment_id' => $payment->id,
                'vote_id' => $existingVote->id
            ]);
            return $existingVote;
        }

        // Get customer information
        $customer = $paymentData['customer'] ?? [];
        $customerPhone = $customer['phone'] ?? 
                        $metadata['msisdn'] ?? 
                        $metadata['phone'] ?? 
                        $payment->metadata['msisdn'] ?? 
                        $payment->customer_phone ?? 
                        null;

        // Create vote record
        $vote = \App\Models\Vote::create([
            'nominee_profile_id' => $nominee->id,
            'payment_id' => $payment->id,
            'voter_name' => $payment->customer_name ?? $customer['first_name'] ?? 'Mobile Money Voter',
            'voter_email' => $payment->customer_email ?? $customer['email'] ?? '<EMAIL>',
            'voter_phone' => $customerPhone,
            'vote_count' => $voteCount,
            'amount_paid' => $payment->amount,
            'discount_applied' => $metadata['discount_applied'] ?? $payment->metadata['discount_applied'] ?? 0,
            'ip_address' => $metadata['ip_address'] ?? null,
            'user_agent' => $metadata['user_agent'] ?? 'Mobile Money App'
        ]);

        // Update nominee vote count
        $nominee->increment('total_votes', $voteCount);

        Log::info('Vote created from webhook', [
            'vote_id' => $vote->id,
            'nominee_id' => $nominee->id,
            'vote_count' => $voteCount,
            'amount' => $payment->amount,
            'method' => $voteMethod
        ]);

        return $vote;
    }

    private function calculateVoteCount($amount, $metadata)
    {
        // Check if vote count is explicitly provided
        if (isset($metadata['vote_count'])) {
            return (int) $metadata['vote_count'];
        }

        // Default calculation: GHS 1 = 1 vote, with minimum of 1
        $voteCount = max(1, floor($amount / 100)); // Amount is in pesewas
        
        Log::info('Calculated vote count', [
            'amount_pesewas' => $amount,
            'vote_count' => $voteCount
        ]);

        return $voteCount;
    }

    private function detectVoteMethod($paymentData, $metadata, $payment)
    {
        $channel = $paymentData['channel'] ?? '';
        
        // Check for mobile money channels
        if (in_array($channel, ['mobile_money', 'mtn', 'vodafone', 'airtel', 'tigo'])) {
            return 'ussd';
        }
        
        // Check authorization channel for mobile money
        $authorization = $paymentData['authorization'] ?? [];
        $authChannel = $authorization['channel'] ?? '';
        if (in_array($authChannel, ['mobile_money', 'mtn', 'vodafone', 'airtel', 'tigo'])) {
            return 'ussd';
        }

        // Check metadata platform
        if (isset($metadata['platform'])) {
            if ($metadata['platform'] === 'telegram') {
                return 'telegram';
            } elseif ($metadata['platform'] === 'voteyourfav' || isset($metadata['session_id']) || isset($metadata['msisdn'])) {
                return 'ussd';
            }
        }

        // Check payment metadata
        if (isset($payment->metadata['platform'])) {
            if ($payment->metadata['platform'] === 'telegram') {
                return 'telegram';
            } elseif ($payment->metadata['platform'] === 'voteyourfav' || isset($payment->metadata['session_id']) || isset($payment->metadata['msisdn'])) {
                return 'ussd';
            }
        }

        // Check payment reference patterns
        $reference = $paymentData['reference'] ?? $payment->reference ?? '';
        if (str_starts_with($reference, 'TG_')) {
            return 'telegram';
        } elseif (str_starts_with($reference, 'NALO_') || str_starts_with($reference, 'USSD_') || str_starts_with($reference, 'MM_')) {
            return 'ussd';
        }

        // Default to web for card payments, ussd for mobile money
        return in_array($channel, ['card', 'bank']) ? 'web' : 'ussd';
    }
}
