<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\NomineeProfile;
use App\Models\Organization;
use App\Models\AwardCategory;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Str;
use App\Services\SmsService;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register');
    }

    /**
     * Handle an incoming organization registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'organization_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone_number' => 'required|string|max:20',
        ]);

        // Create the user as admin (3b3 manage the organization)
        $user = User::create([
            'name' => $request->organization_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => User::ROLE_ADMIN, // Organization administrators should be admins
            'phone_number' => $request->phone_number,
            'slug' => Str::slug($request->organization_name . '-' . Str::random(6)),
            'approval_status' => 'pending',
        ]);

        // Create the organization
        $organization = Organization::create([
            'name' => $request->organization_name,
            'slug' => Str::slug($request->organization_name),
            'is_active' => false, // Organizations are inactive by default until approved
        ]);

        // Associate the user with the organization
        $user->organization_id = $organization->id;
        $user->save();

        event(new Registered($user));

        // Send SMS notification to super admins about new organization signup
        $smsService = new SmsService();
        $smsService->notifyOrganizationSignup($request->organization_name, $request->organization_name);

        return to_route('login')->with('status', 'Your organization registration has been submitted and is pending approval.');
    }
}
