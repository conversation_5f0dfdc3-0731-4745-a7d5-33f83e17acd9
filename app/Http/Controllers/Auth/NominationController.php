<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\NomineeProfile;
use App\Models\Organization;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Str;

class NominationController extends Controller
{
    /**
     * Show the general nomination application form.
     */
    public function create(): Response
    {
        return Inertia::render('auth/nomination-application');
    }

    /**
     * Show the organization-specific nomination application form.
     */
    public function createForOrganization(Organization $organization): Response
    {
        // Check if organization is active and approved
        if (!$organization->is_active) {
            abort(404, 'Organization not found or not accepting nominations.');
        }

        // Get only categories for this organization
        $categories = $organization->awardCategories()
            ->where('is_active', true)
            ->where('is_suspended', false)
            ->select('id', 'name', 'description', 'organization_id')
            ->get();

        return Inertia::render('auth/organization-nomination-application', [
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'slug' => $organization->slug,
                'description' => $organization->description,
                'logo' => $organization->logo,
                'website' => $organization->website,
            ],
            'categories' => $categories,
        ]);
    }

    /**
     * Handle an incoming organization-specific nomination application.
     */
    public function storeForOrganization(Request $request, Organization $organization): RedirectResponse
    {
        // Check if organization is active
        if (!$organization->is_active) {
            abort(404, 'Organization not found or not accepting nominations.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'award_category_id' => 'required|exists:award_categories,id',
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120',
            'bio' => 'nullable|string',
            'achievements' => 'nullable|string',
            'why_vote_for_me' => 'required|string|max:1000',
            'phone' => 'required|string|max:20',
            'social_links.instagram' => 'nullable|string',
            'social_links.twitter' => 'nullable|string',
            'social_links.facebook' => 'nullable|string',
            'role' => 'required|string|in:nominee',
        ]);

        // Verify the category belongs to this organization
        $category = $organization->awardCategories()
            ->where('id', $request->award_category_id)
            ->first();

        if (!$category) {
            return back()->withErrors(['award_category_id' => 'Invalid category for this organization.']);
        }

        // Create the user with the organization pre-set
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'organization_id' => $organization->id, // Pre-set to the organization
            'role' => $request->role,
            'phone' => $request->phone,
            'slug' => Str::slug($request->name . '-' . Str::random(6)),
            'approval_status' => 'pending',
        ]);

        // Get the auto-created nominee profile
        $nomineeProfile = $user->nomineeProfile()->first();

        if (!$nomineeProfile) {
            \Log::error("NomineeProfile was not auto-created for user ID: {$user->id}. Creating now.");
            $nomineeProfile = new NomineeProfile(['user_id' => $user->id]);
        }
        
        // Store profile image
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile-images', 'public');
            \Log::info('Profile image saved at: ' . $profileImagePath);
        }

        // Update nominee profile
        $nomineeProfile->fill([
            'display_name' => $request->name,
            'bio' => $request->bio,
            'achievements' => $request->achievements,
            'why_vote_for_me' => $request->why_vote_for_me,
            'profile_image' => $profileImagePath ?: $nomineeProfile->profile_image,
            'social_links' => $request->social_links,
            'is_approved' => false,
        ]);
        $nomineeProfile->save();

        // Associate with the selected category
        $nomineeProfile->awardCategories()->syncWithoutDetaching([
            $request->award_category_id => [
                'assigned_at' => now(),
                'assigned_by' => null,
            ]
        ]);

        event(new Registered($user));

        Auth::login($user);

        return to_route('dashboard')->with('success', "Your nomination application for {$organization->name} has been submitted and is pending approval.");
    }

    /**
     * Handle the general nomination application.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'organization_id' => 'required|exists:organizations,id',
            'award_category_id' => 'required|exists:award_categories,id',
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120',
            'bio' => 'nullable|string',
            'achievements' => 'nullable|string',
            'why_vote_for_me' => 'required|string|max:1000',
            'phone' => 'required|string|max:20',
            'social_links.instagram' => 'nullable|string',
            'social_links.twitter' => 'nullable|string',
            'social_links.facebook' => 'nullable|string',
            'role' => 'required|string|in:nominee',
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'organization_id' => $request->organization_id,
            'role' => $request->role, // This will trigger the User model's 'created' event
            'phone' => $request->phone,
            'slug' => Str::slug($request->name . '-' . Str::random(6)), // User slug
            'approval_status' => 'pending', // New users need approval
        ]);

        // The User model's 'created' event an_hook should have already called $user->createNomineeProfile()
        // which creates a basic profile. We now fetch and update it.
        $nomineeProfile = $user->nomineeProfile()->first(); // Get the first profile

        if (!$nomineeProfile) {
            // This case should ideally not be reached if the User model's hook works.
            // But as a fallback, or if createNomineeProfile in User model is changed.
            // We'll log an error and attempt to create it here.
            \Log::error("NomineeProfile was not auto-created for user ID: {$user->id}. Creating now.");
            $nomineeProfile = new NomineeProfile(['user_id' => $user->id]);
        }
        
        // Store profile image if provided
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile-images', 'public');
            \Log::info('Profile image saved at: ' . $profileImagePath);
        }

        // Update nominee profile with all the details
        $nomineeProfile->fill([
            // 'user_id' is already set
            'display_name' => $request->name, // Use request name as display name, or a specific field if available
            'bio' => $request->bio,
            'achievements' => $request->achievements,
            'why_vote_for_me' => $request->why_vote_for_me,
            'profile_image' => $profileImagePath ?: $nomineeProfile->profile_image, // Keep existing if no new image
            'social_links' => $request->social_links,
            // Remove manual slug generation - let the model's creating event handle it with the new 5-character format
            'is_approved' => false, // Requires approval
        ]);
        $nomineeProfile->save();

        // Associate the nominee profile with the selected award category
        // Use syncWithoutDetaching to avoid issues if this somehow runs multiple times
        // or if the category is already attached (though it shouldn't be for a new profile).
        $nomineeProfile->awardCategories()->syncWithoutDetaching([
            $request->award_category_id => [
                'assigned_at' => now(),
                'assigned_by' => null, // Self-assigned during registration
            ]
        ]);

        event(new Registered($user));

        Auth::login($user);

        return to_route('dashboard')->with('success', 'Your nomination application has been submitted and is pending approval.');
    }
} 