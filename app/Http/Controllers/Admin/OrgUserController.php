<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class OrgUserController extends Controller
{
    public function index(Request $request)
    {
        $currentUser = auth()->user();
        
        // Organization admins can only see users from their organization
        $query = User::with(['organization', 'nomineeProfiles' => function($query) {
                $query->select('id', 'user_id', 'display_name', 'profile_image', 'nominee_code');
            }])
            ->where('organization_id', $currentUser->organization_id)
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->role, function ($query, $role) {
                $query->where('role', $role);
            })
            ->when($request->status, function ($query, $status) {
                if ($status === 'suspended') {
                    $query->where('is_suspended', true);
                } elseif ($status === 'active') {
                    $query->where('is_suspended', false);
                }
            });

        $users = $query->latest()
            ->paginate(15)
            ->withQueryString();

        // Calculate stats for the overview cards (organization specific)
        $stats = [
            'total_users' => User::where('organization_id', $currentUser->organization_id)->count(),
            'active_users' => User::where('organization_id', $currentUser->organization_id)->where('is_suspended', false)->where('approval_status', 'approved')->count(),
            'admins' => User::where('organization_id', $currentUser->organization_id)->where('role', User::ROLE_ADMIN)->count(),
            'nominees' => User::where('organization_id', $currentUser->organization_id)->where('role', User::ROLE_NOMINEE)->count(),
            'suspended_users' => User::where('organization_id', $currentUser->organization_id)->where('is_suspended', true)->count(),
            'pending_approval' => User::where('organization_id', $currentUser->organization_id)->where('approval_status', 'pending')->count(),
            'rejected_users' => User::where('organization_id', $currentUser->organization_id)->where('approval_status', 'rejected')->count(),
        ];

        return Inertia::render('admin/org-users/index', [
            'users' => $users,
            'organization' => $currentUser->organization,
            'filters' => $request->only(['search', 'role', 'status']),
            'roles' => [
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
            'stats' => $stats,
        ]);
    }

    public function create()
    {
        $currentUser = auth()->user();

        return Inertia::render('admin/org-users/create', [
            'organization' => $currentUser->organization,
            'roles' => [
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }

    public function store(Request $request)
    {
        $currentUser = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in([User::ROLE_ADMIN, User::ROLE_NOMINEE])],
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
        ]);

        $newUser = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'organization_id' => $currentUser->organization_id,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'slug' => Str::slug($request->name) . '-' . Str::random(6),
            'email_verified_at' => now(),
            'approval_status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $currentUser->id,
        ]);

        return redirect()->route('admin.org-users.index')
            ->with('success', 'User created successfully.');
    }

    public function show(User $user)
    {
        $currentUser = auth()->user();
        
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        // Load the user with their organization
        $user->load(['organization']);

        // Manually prepare nominee profiles with their award categories
        $nomineeProfilesData = $user->nomineeProfiles()->with('awardCategories')->get()->map(function ($profile) {
            // Ensure profile image path is correctly formatted
            $profileImagePath = $profile->profile_image;
            if ($profileImagePath) {
                // Get just the filename and construct the storage URL
                $filename = basename($profileImagePath);
                $profileImagePath = '/storage/app/public/profile-images' . $filename;
            }
            
            return [
                'id' => $profile->id,
                'slug' => $profile->slug,
                'display_name' => $profile->display_name,
                'bio' => $profile->bio,
                'profile_image' => $profileImagePath,
                'nominee_code' => $profile->nominee_code,
                'votes_count' => $profile->total_votes, // Assuming votes_count comes from total_votes
                'awardCategories' => $profile->awardCategories->map(function ($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'slug' => $category->slug,
                    ];
                })->all(),
            ];
        })->all();

        // Prepare the user data for Inertia, merging the manually prepared nominee profiles
        $userData = $user->toArray();
        $userData['nominee_profiles'] = $nomineeProfilesData;

        return Inertia::render('admin/org-users/show', [
            'user' => $userData,
            'organization' => $currentUser->organization,
        ]);
    }

    public function edit(User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        return Inertia::render('admin/org-users/edit', [
            'user' => $user,
            'organization' => $currentUser->organization,
            'roles' => [
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }

    public function update(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => ['required', Rule::in([User::ROLE_ADMIN, User::ROLE_NOMINEE])],
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'is_suspended' => 'boolean',
            'suspension_reason' => 'nullable|string|max:255',
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'is_suspended' => $request->is_suspended ?? false,
            'suspension_reason' => $request->is_suspended ? $request->suspension_reason : null,
            'organization_id' => $currentUser->organization_id, // Ensure organization_id is preserved
        ];

        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        // Prevent admin from changing their own role
        if ($user->id === $currentUser->id && $request->role !== User::ROLE_ADMIN) {
            return back()->with('error', 'You cannot change your own role.')->withInput();
        }

        $user->update($userData);

        return redirect()->route('admin.org-users.show', $user->slug)
            ->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        // Prevent deletion of the current user
        if ($user->id === $currentUser->id) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        // Prevent deletion if user has associated data
        if ($user->nomineeProfiles()->exists()) {
            return back()->with('error', 'Cannot delete user with associated nominee profiles.');
        }

        $user->delete();

        return redirect()->route('admin.org-users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function toggleStatus(User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        // Prevent suspending themselves
        if ($user->id === $currentUser->id) {
            return back()->with('error', 'You cannot suspend your own account.');
        }

        $user->update([
            'is_suspended' => !$user->is_suspended,
            'suspension_reason' => $user->is_suspended ? null : 'Status toggled by admin',
        ]);

        $status = $user->is_suspended ? 'suspended' : 'activated';
        
        return back()->with('success', "User {$status} successfully.");
    }

    public function approve(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        $user->approve($currentUser, $request->approval_notes);

        return back()->with('success', 'User approved successfully.');
    }

    public function reject(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user belongs to same organization
        if ($user->organization_id !== $currentUser->organization_id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $user->reject($currentUser, $request->rejection_reason);

        return back()->with('success', 'User rejected successfully.');
    }

    public function pendingApprovals(Request $request)
    {
        $currentUser = auth()->user();
        
        // Get pending users for this organization
        $query = User::with(['organization'])
            ->where('organization_id', $currentUser->organization_id)
            ->where('approval_status', 'pending')
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->role, function ($query, $role) {
                $query->where('role', $role);
            });

        $pendingUsers = $query->latest()
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/org-users/pending-approvals', [
            'users' => $pendingUsers,
            'organization' => $currentUser->organization,
            'filters' => $request->only(['search', 'role']),
            'roles' => [
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }
} 