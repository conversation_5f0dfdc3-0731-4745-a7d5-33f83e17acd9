<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vote;
use App\Models\Payment;
use App\Models\User;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        $organizationId = $user->isSuperAdmin() ? $request->organization_id : $user->organization_id;
        $categoryId = $request->category_id; // Add category filter
        
        // Date range
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Overview metrics
        $overview = $this->getOverviewMetrics($organizationId, $startDate, $endDate, $categoryId);
        
        // Revenue analytics
        $revenueAnalytics = $this->getRevenueAnalytics($organizationId, $startDate, $endDate, $categoryId);
        
        // Category performance
        $categoryPerformance = $this->getCategoryPerformance($organizationId, $startDate, $endDate, $categoryId);
        
        // Top performers
        $topPerformers = $this->getTopPerformers($organizationId, $startDate, $endDate, $categoryId);
        
        // Organizations for super admin
        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        // Structure data to match React component expectations
        $data = [
            'overview' => [
                'total_votes' => [
                    'value' => $overview['total_votes'],
                    'change' => abs($overview['votes_change']),
                    'change_type' => $overview['votes_change'] >= 0 ? 'positive' : 'negative'
                ],
                'total_revenue' => [
                    'value' => 'GHS ' . number_format($overview['total_revenue'], 2),
                    'change' => abs($overview['revenue_change']),
                    'change_type' => $overview['revenue_change'] >= 0 ? 'positive' : 'negative'
                ],
                'active_users' => [
                    'value' => User::where('is_suspended', false)->count(),
                    'change' => 5.1, // Mock data - calculate actual change if needed
                    'change_type' => 'positive'
                ],
                'conversion_rate' => [
                    'value' => '3.2%', // Mock data - calculate actual conversion rate
                    'change' => 2.1, // Mock data - calculate actual change
                    'change_type' => 'positive'
                ]
            ],
            'charts' => [
                'revenue_trend' => $revenueAnalytics['daily_revenue']->map(function($item) {
                    return [
                        'date' => $item->date,
                        'revenue' => (float) $item->revenue
                    ];
                })->toArray(),
                'voting_trend' => [], // Mock data - implement if needed
                'category_performance' => $categoryPerformance->map(function($category) {
                    return [
                        'name' => $category['name'],
                        'votes' => $category['total_votes'],
                        'revenue' => (float) $category['total_revenue']
                    ];
                })->toArray(),
                'user_activity' => [] // Mock data - implement if needed
            ],
            'top_performers' => [
                'categories' => $categoryPerformance->take(5)->map(function($category) {
                    return [
                        'id' => $category['id'],
                        'name' => $category['name'],
                        'votes' => $category['total_votes'],
                        'revenue' => 'GHS ' . number_format($category['total_revenue'], 2)
                    ];
                })->toArray(),
                'nominees' => $topPerformers['nominees']->toArray(),
                'organizations' => $organizations ? $organizations->map(function($org) {
                    return [
                        'id' => $org->id,
                        'name' => $org->name,
                        'total_votes' => 0, // Calculate actual votes
                        'total_revenue' => 'GHS 0.00' // Calculate actual revenue
                    ];
                })->toArray() : []
            ]
        ];

        return Inertia::render('admin/analytics/index', [
            'data' => $data,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'organization_id' => $organizationId,
                'category_id' => $categoryId,
            ],
            'user_role' => $user->role,
            'organizations' => $organizations,
        ]);
    }

    public function export(Request $request)
    {
        $user = auth()->user();
        $organizationId = $user->isSuperAdmin() ? $request->organization_id : $user->organization_id;
        
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Generate export data
        $exportData = $this->generateExportData($organizationId, $startDate, $endDate);
        
        return $this->exportToCsv($exportData, $startDate, $endDate);
    }

    private function getOverviewMetrics($organizationId, $startDate, $endDate, $categoryId = null)
    {
        $baseQuery = function($query) use ($organizationId, $categoryId) {
            if ($categoryId) {
                return $query->whereHas('nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.id', $categoryId));
            } elseif ($organizationId) {
                return $query->whereHas('nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.organization_id', $organizationId));
            }
            return $query;
        };

        // Current period
        $currentVotes = Vote::when(true, $baseQuery)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $rawCurrentRevenue = Payment::where('status', 'success')
            ->when($categoryId || $organizationId, function($query) use ($categoryId, $organizationId) {
                if ($categoryId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.id', $categoryId));
                } elseif ($organizationId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.organization_id', $organizationId));
                }
                return $query;
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        // Previous period for comparison
        $periodLength = Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate));
        $previousStartDate = Carbon::parse($startDate)->subDays($periodLength)->format('Y-m-d');
        $previousEndDate = $startDate;

        $previousVotes = Vote::when(true, $baseQuery)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $rawPreviousRevenue = Payment::where('status', 'success')
            ->when($categoryId || $organizationId, function($query) use ($categoryId, $organizationId) {
                if ($categoryId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.id', $categoryId));
                } elseif ($organizationId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.organization_id', $organizationId));
                }
                return $query;
            })
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');

        // Apply revenue sharing
        $currentRevenue = $this->calculateRevenueShare($rawCurrentRevenue, $organizationId);
        $previousRevenue = $this->calculateRevenueShare($rawPreviousRevenue, $organizationId);

        // Calculate percentage changes
        $votesChange = $previousVotes > 0 ? (($currentVotes - $previousVotes) / $previousVotes) * 100 : 0;
        $revenueChange = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return [
            'total_votes' => $currentVotes,
            'total_revenue' => $currentRevenue,
            'votes_change' => round($votesChange, 1),
            'revenue_change' => round($revenueChange, 1),
            'avg_vote_value' => $currentVotes > 0 ? round($currentRevenue / $currentVotes, 2) : 0,
        ];
    }

    private function getRevenueAnalytics($organizationId, $startDate, $endDate, $categoryId = null)
    {
        // Daily revenue
        $dailyRevenue = Payment::where('status', 'success')
            ->when($categoryId || $organizationId, function($query) use ($categoryId, $organizationId) {
                if ($categoryId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.id', $categoryId));
                } elseif ($organizationId) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('award_categories.organization_id', $organizationId));
                }
                return $query;
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(amount) as raw_revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function($item) use ($organizationId) {
                $item->revenue = $this->calculateRevenueShare($item->raw_revenue, $organizationId);
                unset($item->raw_revenue);
                return $item;
            });

        return [
            'daily_revenue' => $dailyRevenue,
        ];
    }

    private function getCategoryPerformance($organizationId, $startDate, $endDate, $categoryId = null)
    {
        return AwardCategory::when($organizationId, fn($q) => $q->where('organization_id', $organizationId))
            ->when($categoryId, fn($q) => $q->where('id', $categoryId))
            ->withCount(['nomineeProfiles as total_nominees'])
            ->get()
            ->map(function ($category) use ($startDate, $endDate, $organizationId) {
                $votes = Vote::whereHas('nomineeProfile.awardCategories', function($query) use ($category) {
                        $query->where('award_categories.id', $category->id);
                    })
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count();
                
                $rawRevenue = Payment::where('status', 'success')
                    ->whereHas('votes.nomineeProfile.awardCategories', function($query) use ($category) {
                        $query->where('award_categories.id', $category->id);
                    })
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->sum('amount');

                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'total_votes' => $votes,
                    'total_revenue' => $this->calculateRevenueShare($rawRevenue, $organizationId),
                    'total_nominees' => $category->total_nominees,
                ];
            })
            ->sortByDesc('total_votes');
    }

    private function getTopPerformers($organizationId, $startDate, $endDate, $categoryId = null)
    {
        $topNominees = NomineeProfile::when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('awardCategories', fn($q) => $q->where('award_categories.organization_id', $organizationId));
            })
            ->when($categoryId, function($query) use ($categoryId) {
                return $query->whereHas('awardCategories', fn($q) => $q->where('award_categories.id', $categoryId));
            })
            ->with('awardCategories')
            ->get()
            ->map(function ($nominee) use ($startDate, $endDate) {
                $votes = Vote::where('nominee_profile_id', $nominee->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count();
                
                $firstCategory = $nominee->awardCategories->first();
                return [
                    'id' => $nominee->id,
                    'display_name' => $nominee->display_name,
                    'category' => $firstCategory ? $firstCategory->name : 'No Category',
                    'votes_count' => $votes,
                ];
            })
            ->sortByDesc('votes_count')
            ->take(10)
            ->values();

        return [
            'nominees' => $topNominees,
        ];
    }

    private function generateExportData($organizationId, $startDate, $endDate)
    {
        return [
            'overview' => $this->getOverviewMetrics($organizationId, $startDate, $endDate),
            'categories' => $this->getCategoryPerformance($organizationId, $startDate, $endDate),
            'top_performers' => $this->getTopPerformers($organizationId, $startDate, $endDate),
        ];
    }

    private function exportToCsv($data, $startDate, $endDate)
    {
        $filename = "analytics_report_{$startDate}_to_{$endDate}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Write headers
            fputcsv($file, ['Category', 'Total Nominees', 'Total Votes', 'Total Revenue']);
            
            // Write category data
            foreach ($data['categories'] as $category) {
                fputcsv($file, [
                    $category['name'],
                    $category['total_nominees'],
                    $category['total_votes'],
                    $category['total_revenue'],
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Calculate revenue share based on user role and organization-specific revenue percentage.
     */
    private function calculateRevenueShare($amount, $organizationId = null)
    {
        $user = auth()->user();
        
        // If user is organization admin or super admin viewing specific organization data,
        // return organization's share based on their revenue percentage
        if (!$user->isSuperAdmin() || $organizationId) {
            $organization = Organization::find($organizationId ?: $user->organization_id);
            if ($organization) {
                $organizationShare = 100 - $organization->revenue_percentage;
                return $amount * ($organizationShare / 100);
            }
            // Fallback to 88% if organization not found
            return $amount * 0.88;
        }
        
        // If super admin viewing all system data, return platform's total share
        return $amount; // Return full amount for super admin dashboard overview
    }
} 