<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\Vote;
use App\Models\Payment;
use App\Models\NomineeProfile;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Check if user is pending approval
        if ($user->isAdmin() && !$user->isSuperAdmin() && !$user->isApproved()) {
            return $this->pendingApprovalDashboard($user);
        }
        
        // Get date range for filtering (default to last 30 days)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Base query constraints based on user role
        $organizationConstraint = $user->isSuperAdmin() ? null : $user->organization_id;
        
        // Core Statistics
        $stats = $this->getCoreStatistics($organizationConstraint, $startDate, $endDate);
        
        // Recent Activities
        $recentActivities = $this->getRecentActivities($organizationConstraint);
        
        // Top Performing Categories
        $topCategories = $this->getTopCategories($organizationConstraint);
        
        // Top Nominees
        $topNominees = $this->getTopNominees($organizationConstraint);
        
        // Revenue Analytics
        $revenueData = $this->getRevenueAnalytics($organizationConstraint, $startDate, $endDate);
        
        // Voting Trends (last 7 days)
        $votingTrends = $this->getVotingTrends($organizationConstraint);
        
        // Recent Payments
        $recentPayments = $this->getRecentPayments($organizationConstraint);
        
        // System Health (Super Admin only)
        $systemHealth = $user->isSuperAdmin() ? $this->getSystemHealth() : null;

        return Inertia::render('admin/Dashboard', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
            'topCategories' => $topCategories,
            'topNominees' => $topNominees,
            'revenueData' => $revenueData,
            'votingTrends' => $votingTrends,
            'recentPayments' => $recentPayments,
            'systemHealth' => $systemHealth,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
            'user_role' => $user->role,
            'organization' => $user->organization,
            'is_pending_approval' => false,
        ]);
    }

    /**
     * Show restricted dashboard for pending approval admins
     */
    private function pendingApprovalDashboard($user)
    {
        return Inertia::render('admin/Dashboard', [
            'stats' => null,
            'recentActivities' => [],
            'topCategories' => [],
            'topNominees' => [],
            'revenueData' => null,
            'votingTrends' => [],
            'recentPayments' => [],
            'systemHealth' => null,
            'filters' => [],
            'user_role' => $user->role,
            'organization' => $user->organization,
            'is_pending_approval' => true,
            'approval_status' => $user->approval_status,
            'created_at' => $user->created_at,
        ]);
    }

    private function getCoreStatistics($organizationId, $startDate, $endDate)
    {
        // Convert date strings to proper datetime ranges
        $startDateTime = Carbon::parse($startDate)->startOfDay();
        $endDateTime = Carbon::parse($endDate)->endOfDay();
        
        $baseQuery = $organizationId ? 
            fn($query) => $query->whereHas('nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId)) :
            fn($query) => $query;

        // Total votes in period (sum of actual vote counts, not just records)
        $totalVotes = Vote::when($organizationId, $baseQuery)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->sum('vote_count');

        // Previous period votes for comparison
        $previousPeriodStart = Carbon::parse($startDate)->subDays(Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate)));
        $previousVotes = Vote::when($organizationId, $baseQuery)
            ->whereBetween('created_at', [$previousPeriodStart, $startDateTime])
            ->sum('vote_count');

        $votesChange = $previousVotes > 0 ? (($totalVotes - $previousVotes) / $previousVotes) * 100 : 0;

        // Total revenue with revenue sharing model
        $rawTotalRevenue = Payment::where('status', 'success')
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->sum('amount');

        // Apply revenue sharing: Organizations get 88%, Super Admins get 12%
        $totalRevenue = $this->calculateRevenueShare($rawTotalRevenue, $organizationId);

        // Previous period revenue with revenue sharing
        $rawPreviousRevenue = Payment::where('status', 'success')
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->whereBetween('created_at', [$previousPeriodStart, $startDateTime])
            ->sum('amount');

        $previousRevenue = $this->calculateRevenueShare($rawPreviousRevenue, $organizationId);

        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        // Active nominees (approved and not suspended)
        $activeNominees = NomineeProfile::when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->where('is_approved', true)
            ->where(function($query) {
                $query->where('is_suspended', false)->orWhereNull('is_suspended');
            })
            ->count();

        // Active categories
        $activeCategories = AwardCategory::when($organizationId, fn($q) => $q->where('organization_id', $organizationId))
            ->where('is_active', true)
            ->count();

        // Organizations (Super Admin only)
        $totalOrganizations = $organizationId ? null : Organization::where('is_active', true)->count();

        // Gross revenue for super admins (100% before revenue sharing)
        $grossRevenue = null;
        if (!$organizationId) { // Super admin viewing all system data
            $user = auth()->user();
            if ($user->isSuperAdmin()) {
                // Previous period gross revenue for comparison
                $rawPreviousGrossRevenue = Payment::where('status', 'success')
                    ->whereBetween('created_at', [$previousPeriodStart, $startDateTime])
                    ->sum('amount');

                $grossRevenueChange = $rawPreviousGrossRevenue > 0 ? (($rawTotalRevenue - $rawPreviousGrossRevenue) / $rawPreviousGrossRevenue) * 100 : 0;
                
                $grossRevenue = [
                    'value' => 'GHS ' . number_format($rawTotalRevenue, 2),
                    'change' => round($grossRevenueChange, 1),
                    'change_type' => $grossRevenueChange >= 0 ? 'positive' : 'negative',
                ];
            }
        }

        return [
            'total_votes' => [
                'value' => number_format($totalVotes),
                'change' => round($votesChange, 1),
                'change_type' => $votesChange >= 0 ? 'positive' : 'negative',
            ],
            'total_revenue' => [
                'value' => 'GHS ' . number_format($totalRevenue, 2),
                'change' => round($revenueChange, 1),
                'change_type' => $revenueChange >= 0 ? 'positive' : 'negative',
            ],
            'active_nominees' => [
                'value' => number_format($activeNominees),
                'change' => 'Active',
                'change_type' => 'neutral',
            ],
            'active_categories' => [
                'value' => number_format($activeCategories),
                'change' => 'Active',
                'change_type' => 'neutral',
            ],
            'total_organizations' => $totalOrganizations ? [
                'value' => number_format($totalOrganizations),
                'change' => 'Active',
                'change_type' => 'neutral',
            ] : null,
            'gross_revenue' => $grossRevenue,
        ];
    }

    private function getRecentActivities($organizationId)
    {
        // Recent votes
        $recentVotes = Vote::with(['nomineeProfile.awardCategories', 'payment'])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($vote) {
                $firstCategory = $vote->nomineeProfile->awardCategories->first();
                return [
                    'type' => 'vote',
                    'description' => "Vote cast for {$vote->nomineeProfile->display_name}" . 
                                   ($firstCategory ? " in {$firstCategory->name}" : ""),
                    'amount' => $vote->payment ? 'GHS ' . number_format($vote->payment->amount, 2) : null,
                    'time' => $vote->created_at->diffForHumans(),
                    'status' => $vote->payment ? $vote->payment->status : 'pending',
                ];
            });

        return $recentVotes;
    }

    private function getTopCategories($organizationId)
    {
        return AwardCategory::with(['nomineeProfiles.votes.payment'])
            ->when($organizationId, fn($q) => $q->where('organization_id', $organizationId))
            ->get()
            ->map(function ($category) use ($organizationId) {
                $votesCount = $category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->sum('vote_count');
                });
                
                $rawTotalRevenue = $category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->sum(function ($vote) {
                        return $vote->payment && $vote->payment->status === 'success' ? $vote->payment->amount : 0;
                    });
                });

                // Apply revenue sharing for category revenue
                $totalRevenue = $this->calculateRevenueShare($rawTotalRevenue, $organizationId);

                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'votes_count' => $votesCount,
                    'total_revenue' => 'GHS ' . number_format($totalRevenue, 2),
                    'price_per_vote' => 'GHS ' . number_format($category->price_per_vote, 2),
                ];
            })
            ->sortByDesc('votes_count')
            ->take(5)
            ->values();
    }

    private function getTopNominees($organizationId)
    {
        return NomineeProfile::with(['awardCategories', 'votes'])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->get()
            ->map(function ($nominee) {
                $firstCategory = $nominee->awardCategories->first();
                $totalVotes = $nominee->votes->sum('vote_count');
                return [
                    'id' => $nominee->id,
                    'display_name' => $nominee->display_name,
                    'category' => $firstCategory ? $firstCategory->name : 'No Category',
                    'votes_count' => $totalVotes,
                    'profile_image' => $nominee->profile_image,
                    'nominee_code' => $nominee->nominee_code,
                    'slug' => $nominee->slug,
                ];
            })
            ->sortByDesc('votes_count')
            ->take(10)
            ->values();
    }

    private function getRevenueAnalytics($organizationId, $startDate, $endDate)
    {
        // Convert date strings to proper datetime ranges
        $startDateTime = Carbon::parse($startDate)->startOfDay();
        $endDateTime = Carbon::parse($endDate)->endOfDay();
        
        $dailyRevenue = Payment::where('status', 'success')
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) use ($organizationId) {
                // Apply revenue sharing to daily revenue
                $sharedRevenue = $this->calculateRevenueShare((float) $item->revenue, $organizationId);
                return [
                    'date' => Carbon::parse($item->date)->format('M d'),
                    'revenue' => $sharedRevenue,
                ];
            });

        return $dailyRevenue;
    }

    private function getVotingTrends($organizationId)
    {
        $last7Days = collect(range(6, 0))->map(function ($daysAgo) use ($organizationId) {
            $date = Carbon::now()->subDays($daysAgo);
            $votes = Vote::when($organizationId, function($query) use ($organizationId) {
                    return $query->whereHas('nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
                })
                ->whereDate('created_at', $date)
                ->sum('vote_count');

            return [
                'date' => $date->format('M d'),
                'votes' => $votes,
            ];
        });

        return $last7Days;
    }

    private function getRecentPayments($organizationId)
    {
        return Payment::with(['votes.nomineeProfile.awardCategories'])
            ->when($organizationId, function($query) use ($organizationId) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $organizationId));
            })
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($payment) {
                $vote = $payment->votes->first();
                $firstCategory = $vote && $vote->nomineeProfile ? $vote->nomineeProfile->awardCategories->first() : null;
                return [
                    'id' => $payment->id,
                    'reference' => $payment->reference,
                    'amount' => 'GHS ' . number_format($payment->amount, 2),
                    'status' => $payment->status,
                    'nominee' => $vote ? $vote->nomineeProfile->display_name : 'N/A',
                    'category' => $firstCategory ? $firstCategory->name : 'N/A',
                    'created_at' => $payment->created_at->diffForHumans(),
                ];
            });
    }

    private function getSystemHealth()
    {
        return [
            'total_users' => User::count(),
            'active_organizations' => Organization::where('is_active', true)->count(),
            'total_categories' => AwardCategory::count(),
            'total_nominees' => NomineeProfile::count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'database_size' => $this->getDatabaseSize(),
            'storage_usage' => $this->getStorageUsage(),
        ];
    }

    private function getDatabaseSize()
    {
        try {
            $size = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size' FROM information_schema.tables WHERE table_schema = DATABASE()")[0]->size ?? 0;
            return $size . ' MB';
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    private function getStorageUsage()
    {
        try {
            $bytes = disk_total_space(storage_path()) - disk_free_space(storage_path());
            $mb = round($bytes / 1024 / 1024, 2);
            return $mb . ' MB';
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    /**
     * Calculate revenue share based on user role and organization-specific revenue percentage.
     */
    private function calculateRevenueShare($amount, $organizationId = null)
    {
        $user = auth()->user();
        
        // If user is organization admin or super admin viewing specific organization data,
        // return organization's share based on their revenue percentage
        if (!$user->isSuperAdmin() || $organizationId) {
            $organization = Organization::find($organizationId ?: $user->organization_id);
            if ($organization) {
                $organizationShare = 100 - $organization->revenue_percentage;
                return $amount * ($organizationShare / 100);
            }
            // Fallback to 88% if organization not found
            return $amount * 0.88;
        }
        
        // If super admin viewing all system data, return platform's total share
        // This would be the sum of all revenue percentages from all organizations
        return $amount; // Return full amount for super admin dashboard overview
    }
}