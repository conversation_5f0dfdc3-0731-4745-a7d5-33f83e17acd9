<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::with(['organization', 'nomineeProfiles:id,user_id,display_name,nominee_code,profile_image'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->role, function ($query, $role) {
                $query->where('role', $role);
            })
            ->when($request->organization_id, function ($query, $orgId) {
                $query->where('organization_id', $orgId);
            })
            ->when($request->status, function ($query, $status) {
                if ($status === 'suspended') {
                    $query->where('is_suspended', true);
                } elseif ($status === 'active') {
                    $query->where('is_suspended', false);
                }
            });

        $users = $query->latest()
            ->paginate(15)
            ->withQueryString();

        $organizations = Organization::select('id', 'name')->get();

        // Calculate stats for the overview cards
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_suspended', false)->where('approval_status', 'approved')->count(),
            'super_admins' => User::where('role', User::ROLE_SUPER_ADMIN)->count(),
            'suspended_users' => User::where('is_suspended', true)->count(),
            'pending_approval' => User::where('approval_status', 'pending')->count(),
            'rejected_users' => User::where('approval_status', 'rejected')->count(),
        ];

        return Inertia::render('admin/users/index', [
            'users' => $users,
            'organizations' => $organizations,
            'filters' => $request->only(['search', 'role', 'organization_id', 'status']),
            'roles' => [
                ['value' => User::ROLE_SUPER_ADMIN, 'label' => 'Super Admin'],
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
            'stats' => $stats,
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        $organizations = Organization::select('id', 'name')->get();

        return Inertia::render('admin/users/create', [
            'organizations' => $organizations,
            'user_role' => $user->role,
            'roles' => [
                ['value' => User::ROLE_SUPER_ADMIN, 'label' => 'Super Admin'],
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in([User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN, User::ROLE_NOMINEE])],
            'organization_id' => 'nullable|exists:organizations,id',
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
        ]);

        $newUser = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'organization_id' => $request->organization_id,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'slug' => Str::slug($request->name) . '-' . Str::random(6),
            'email_verified_at' => now(),
            'approval_status' => 'approved', // Auto-approve users created by super admins
            'approved_at' => now(),
            'approved_by' => $user->id,
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function show(User $user)
    {
        // Load user with organization and nominee profiles with all necessary fields
        $user->load([
            'organization', 
            'nomineeProfiles' => function($query) {
                $query->select([
                    'id', 'user_id', 'display_name', 'bio', 'profile_image', 'nominee_code', 
                    'slug', 'total_votes', 'is_approved', 'is_suspended', 'why_vote_for_me',
                    'social_links', 'achievements', 'created_at', 'updated_at'
                ]);
            },
            'nomineeProfiles.awardCategories:id,name,organization_id',
            'nomineeProfiles.votes.payment'
        ]);

        // Get user statistics
        $stats = [
            'total_votes_received' => $user->nomineeProfiles->sum(function ($profile) {
                return $profile->votes->count();
            }),
            'total_revenue_generated' => $user->nomineeProfiles->sum(function ($profile) {
                return $profile->votes->sum(function ($vote) {
                    return $vote->payment && $vote->payment->status === 'success' ? $vote->payment->amount : 0;
                });
            }),
            'account_created' => $user->created_at->format('M d, Y'),
            'last_login' => $user->updated_at->format('M d, Y H:i'),
        ];

        return Inertia::render('admin/users/show', [
            'user' => $user,
            'stats' => $stats,
        ]);
    }

    public function edit(User $user)
    {
        $currentUser = auth()->user();
        $organizations = Organization::select('id', 'name')->get();

        return Inertia::render('admin/users/edit', [
            'user' => $user,
            'organizations' => $organizations,
            'user_role' => $currentUser->role,
            'roles' => [
                ['value' => User::ROLE_SUPER_ADMIN, 'label' => 'Super Admin'],
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }

    public function update(Request $request, User $user)
    {
        $currentUser = auth()->user(); // Get the currently authenticated user

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => ['required', Rule::in([User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN, User::ROLE_NOMINEE])],
            'organization_id' => 'nullable|exists:organizations,id',
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'is_suspended' => 'boolean',
            'suspension_reason' => 'nullable|string|max:255',
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'organization_id' => $request->organization_id,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'is_suspended' => $request->is_suspended ?? false,
            'suspension_reason' => $request->is_suspended ? $request->suspension_reason : null,
        ];

        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        // Prevent non-super admins from changing a user's organization if that user is already an admin of another org
        if (!$currentUser->isSuperAdmin() && $user->isAdmin() && $user->organization_id && $request->organization_id != $user->organization_id) {
            if ($user->organization_id !== $currentUser->organization_id) { // Check if the user being edited belongs to a different org than the current admin
                 return back()->with('error', 'You cannot change the organization of an admin belonging to another organization.')->withInput();
            }
        }
        
        // Prevent non-super admins from changing their own role or organization if they are an admin
        if (!$currentUser->isSuperAdmin() && $user->id === $currentUser->id && $currentUser->isAdmin()) {
            if ($request->role !== User::ROLE_ADMIN) {
                return back()->with('error', 'Admins cannot change their own role.')->withInput();
            }
            if ($request->organization_id !== $currentUser->organization_id) {
                return back()->with('error', 'Admins cannot change their own organization.')->withInput();
            }
        }

        $user->update($userData);

        return redirect()->route('admin.users.show', $user->slug)
            ->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        // Prevent deletion of the current user
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        // Prevent deletion if user has associated data
        if ($user->nomineeProfiles()->exists()) {
            return back()->with('error', 'Cannot delete user with associated nominee profiles.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function suspend(Request $request, User $user)
    {
        $request->validate([
            'reason' => 'required|string|max:255',
            'notice' => 'nullable|string|max:1000',
        ]);

        $user->update([
            'is_suspended' => true,
            'suspension_reason' => $request->reason,
            'suspension_notice' => $request->notice,
        ]);

        return back()->with('success', 'User suspended successfully.');
    }

    public function unsuspend(User $user)
    {
        $user->update([
            'is_suspended' => false,
            'suspension_reason' => null,
            'suspension_notice' => null,
        ]);

        return back()->with('success', 'User unsuspended successfully.');
    }

    public function toggleStatus(User $user)
    {
        // Prevent toggling status of the current user
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot change your own status.');
        }

        $user->update([
            'is_suspended' => !$user->is_suspended,
            'suspension_reason' => $user->is_suspended ? null : 'Status toggled by admin',
        ]);

        $status = $user->is_suspended ? 'suspended' : 'activated';
        
        return back()->with('success', "User {$status} successfully.");
    }

    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:suspend,unsuspend,delete,change_role',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id',
            'reason' => 'required_if:action,suspend|string|max:255',
            'notice' => 'nullable|string|max:1000',
            'new_role' => 'required_if:action,change_role|in:' . implode(',', [User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN, User::ROLE_NOMINEE]),
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        $currentUserId = auth()->id();

        switch ($request->action) {
            case 'suspend':
                $users->each(function ($user) use ($request, $currentUserId) {
                    if ($user->id !== $currentUserId) {
                        $user->update([
                            'is_suspended' => true,
                            'suspension_reason' => $request->reason,
                            'suspension_notice' => $request->notice,
                        ]);
                    }
                });
                $message = 'Users suspended successfully.';
                break;

            case 'unsuspend':
                $users->each(function ($user) {
                    $user->update([
                        'is_suspended' => false,
                        'suspension_reason' => null,
                        'suspension_notice' => null,
                    ]);
                });
                $message = 'Users unsuspended successfully.';
                break;

            case 'delete':
                $deletedCount = 0;
                $users->each(function ($user) use ($currentUserId, &$deletedCount) {
                    if ($user->id !== $currentUserId && 
                        !$user->nomineeProfiles()->exists()) {
                        $user->delete();
                        $deletedCount++;
                    }
                });
                $message = "{$deletedCount} users deleted successfully.";
                break;

            case 'change_role':
                $users->each(function ($user) use ($request, $currentUserId) {
                    if ($user->id !== $currentUserId) {
                        $user->update(['role' => $request->new_role]);
                    }
                });
                $message = 'User roles updated successfully.';
                break;

            default:
                $message = 'Invalid action.';
        }

        return back()->with('success', $message);
    }

    public function approve(Request $request, User $user)
    {
        $currentUser = auth()->user();

        $request->validate([
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        $user->approve($currentUser, $request->approval_notes);

        return back()->with('success', 'User approved successfully.');
    }

    public function reject(Request $request, User $user)
    {
        $currentUser = auth()->user();

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $user->reject($currentUser, $request->rejection_reason);

        return back()->with('success', 'User rejected successfully.');
    }

    public function pendingApprovals(Request $request)
    {
        // Get pending users across all organizations
        $query = User::with(['organization'])
            ->where('approval_status', 'pending')
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->role, function ($query, $role) {
                $query->where('role', $role);
            })
            ->when($request->organization_id, function ($query, $orgId) {
                $query->where('organization_id', $orgId);
            });

        $pendingUsers = $query->latest()
            ->paginate(15)
            ->withQueryString();

        $organizations = Organization::select('id', 'name')->get();

        return Inertia::render('admin/users/pending-approvals', [
            'users' => $pendingUsers,
            'organizations' => $organizations,
            'filters' => $request->only(['search', 'role', 'organization_id']),
            'roles' => [
                ['value' => User::ROLE_ADMIN, 'label' => 'Admin'],
                ['value' => User::ROLE_NOMINEE, 'label' => 'Nominee'],
            ],
        ]);
    }
}