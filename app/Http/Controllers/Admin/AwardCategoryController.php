<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AwardCategory;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;

class AwardCategoryController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = AwardCategory::with(['organization'])
            ->withCount(['nomineeProfiles'])
            ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                return $query->where('organization_id', $user->organization_id);
            })
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->organization_id && $user->isSuperAdmin(), function ($query, $orgId) {
                $query->where('organization_id', $orgId);
            })
            ->when($request->status, function ($query, $status) {
                if ($status === 'active') {
                    $query->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $query->where('is_active', false);
                }
            });

        $categories = $query->latest()
            ->paginate(15)
            ->withQueryString();

        Log::info('AwardCategoryController@index - Data for user', [
            'user_id' => $user->id,
            'is_super_admin' => $user->isSuperAdmin(),
            'user_organization_id' => $user->organization_id,
            'final_sql_query' => $query->toSql(),
            'query_bindings' => $query->getBindings(),
            'category_ids_being_sent' => $categories->pluck('id')->all(),
        ]);

        // Add votes count manually for each category
        $categories->getCollection()->transform(function ($category) {
            $category->votes_count = $category->votes()->count();
            return $category;
        });

        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        return Inertia::render('admin/categories/index', [
            'categories' => $categories,
            'organizations' => $organizations,
            'filters' => $request->only(['search', 'organization_id', 'status']),
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        return Inertia::render('admin/categories/create', [
            'organizations' => $organizations,
        ]);
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'price_per_vote' => 'required|numeric|min:0.01',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_min_votes' => 'nullable|integer|min:1',
            'organization_id' => $user->isSuperAdmin() ? 'required|exists:organizations,id' : 'nullable',
            'voting_start_date' => 'nullable|date|after:today',
            'voting_end_date' => 'nullable|date|after:voting_start_date',
        ]);

        $organizationId = $user->isSuperAdmin() ? $request->organization_id : $user->organization_id;

        $category = AwardCategory::create([
            'name' => $request->name,
            'description' => $request->description,
            'price_per_vote' => $request->price_per_vote,
            'discount_percentage' => $request->discount_percentage ?: 0,
            'discount_min_votes' => $request->discount_min_votes ?: 0,
            'organization_id' => $organizationId,
            'voting_start_date' => $request->voting_start_date,
            'voting_end_date' => $request->voting_end_date,
            'is_active' => true,
        ]);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Award category created successfully.');
    }

    public function show(AwardCategory $category)
    {
        $user = auth()->user();
        
        // Check access
        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            Log::error('Authorization failed for AwardCategory@show', [
                'user_id' => $user->id,
                'user_organization_id' => $user->organization_id,
                'category_id' => $category->id,
                'category_organization_id' => $category->organization_id,
            ]);
            abort(403, 'Access denied.');
        }

        $category->load(['organization', 'nomineeProfiles.votes']);

        // Calculate statistics
        $rawTotalRevenue = $category->nomineeProfiles->sum(function ($profile) {
            return $profile->votes->sum(function ($vote) {
                return $vote->payment && $vote->payment->status === 'success' ? $vote->payment->amount : 0;
            });
        });

        $stats = [
            'total_nominees' => $category->nomineeProfiles->count(),
            'total_votes' => $category->nomineeProfiles->sum(function ($profile) {
                return $profile->votes->count();
            }),
            'total_revenue' => $this->calculateRevenueShare($rawTotalRevenue, $category->organization_id),
            'avg_votes_per_nominee' => $category->nomineeProfiles->count() > 0 ? 
                round($category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->count();
                }) / $category->nomineeProfiles->count(), 1) : 0,
        ];

        // Top nominees
        $topNominees = $category->nomineeProfiles
            ->map(function ($profile) {
                return [
                    'id' => $profile->id,
                    'display_name' => $profile->display_name,
                    'votes_count' => $profile->votes->count(),
                    'profile_image' => $profile->profile_image,
                ];
            })
            ->sortByDesc('votes_count')
            ->take(10)
            ->values();

        return Inertia::render('admin/categories/show', [
            'category' => $category,
            'stats' => $stats,
            'topNominees' => $topNominees,
        ]);
    }

    public function edit(AwardCategory $category)
    {
        $user = auth()->user();
        
        // Check access
        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            Log::error('Authorization failed for AwardCategory@edit', [
                'user_id' => $user->id,
                'user_organization_id' => $user->organization_id,
                'category_id' => $category->id,
                'category_organization_id' => $category->organization_id,
            ]);
            abort(403, 'Access denied.');
        }

        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        return Inertia::render('admin/categories/edit', [
            'category' => $category,
            'organizations' => $organizations,
        ]);
    }

    public function update(Request $request, AwardCategory $category)
    {
        $user = auth()->user();
        
        // Check access
        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'price_per_vote' => 'required|numeric|min:0.01',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_min_votes' => 'nullable|integer|min:1',
            'organization_id' => $user->isSuperAdmin() ? 'required|exists:organizations,id' : 'nullable',
            'voting_start_date' => 'nullable|date',
            'voting_end_date' => 'nullable|date|after:voting_start_date',
        ]);

        $updateData = [
            'name' => $request->name,
            'description' => $request->description,
            'price_per_vote' => $request->price_per_vote,
            'discount_percentage' => $request->discount_percentage ?: 0,
            'discount_min_votes' => $request->discount_min_votes ?: 0,
            'voting_start_date' => $request->voting_start_date,
            'voting_end_date' => $request->voting_end_date,
        ];

        if ($user->isSuperAdmin()) {
            $updateData['organization_id'] = $request->organization_id;
        }

        $category->update($updateData);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Award category updated successfully.');
    }

    public function destroy(AwardCategory $category)
    {
        $user = auth()->user();
        
        // Check access
        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        // Check if category has nominees
        if ($category->nomineeProfiles()->exists()) {
            return back()->with('error', 'Cannot delete category with existing nominees.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Award category deleted successfully.');
    }

    public function toggleStatus(AwardCategory $category)
    {
        $user = auth()->user();
        
        // Check access
        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        $category->update([
            'is_active' => !$category->is_active,
        ]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Category {$status} successfully.");
    }

    /**
     * Show nominees assigned to a category and available nominees.
     */
    public function manageNominees(Request $request, AwardCategory $category)
    {
        $user = auth()->user();

        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        // Eager load necessary relationships for display and filtering
        $category->load([
            'organization', 
            'nomineeProfiles' => function($query) {
                $query->select('id', 'user_id', 'display_name', 'profile_image', 'bio', 'nominee_code', 'created_at');
            },
            'nomineeProfiles.user.organization'
        ]);

        $assignedNomineeProfileIds = $category->nomineeProfiles->pluck('id')->toArray();
        Log::info('[CategoryNomineeDebug] Category ID: ' . $category->id . ' - Assigned Nominee Profile IDs: ', $assignedNomineeProfileIds);

        $availableNomineesQuery = \App\Models\NomineeProfile::with(['user.organization'])
            ->select('id', 'user_id', 'display_name', 'profile_image', 'bio', 'nominee_code', 'created_at')
            ->whereNotIn('id', $assignedNomineeProfileIds)
            ->whereHas('user', function ($query) { // Ensure the user associated with the profile is a nominee
                $query->where('role', \App\Models\User::ROLE_NOMINEE);
            });

        if (!$user->isSuperAdmin()) {
            // Regular admin: Nominees must belong to the category's organization
            $availableNomineesQuery->whereHas('user', function ($q) use ($category) {
                $q->where('organization_id', $category->organization_id);
            });
            Log::info('[CategoryNomineeDebug] Regular Admin Scope: Nominees from Org ID ' . $category->organization_id);
        } elseif ($request->filled('organization_id') && $request->input('organization_id') !== 'all') {
            // Super admin with a specific organization filter applied
            $orgFilterId = $request->input('organization_id');
            $availableNomineesQuery->whereHas('user', function ($q) use ($orgFilterId) {
                $q->where('organization_id', $orgFilterId);
            });
            Log::info('[CategoryNomineeDebug] Super Admin Scope: Filtered by Org ID ' . $orgFilterId);
        } else {
            Log::info('[CategoryNomineeDebug] Super Admin Scope: All unassigned nominees (no org filter or \'all\').');
        }

        $availableNominees = $availableNomineesQuery->get();
        Log::info('[CategoryNomineeDebug] Available Nominees Found (Count: ' . $availableNominees->count() . '): ', $availableNominees->map(function($n){ return ['id' => $n->id, 'name' => $n->display_name, 'user_id' => $n->user_id, 'user_org_id' => $n->user->organization_id]; })->toArray());

        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        return Inertia::render('admin/categories/nominees', [
            'category' => $category,
            'assignedNominees' => $category->nomineeProfiles,
            'availableNominees' => $availableNominees,
            'organizations' => $organizations,
            'filters' => $request->only(['organization_id']),
        ]);
    }

    /**
     * Assign a nominee to a category.
     */
    public function assignNominee(Request $request, AwardCategory $category)
    {
        $user = auth()->user();

        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'nominee_profile_id' => 'required|exists:nominee_profiles,id',
        ]);

        $nomineeProfile = \App\Models\NomineeProfile::find($request->nominee_profile_id);

        // For regular admins, ensure they can only assign nominees from their organization
        if (!$user->isSuperAdmin()) {
            if ($nomineeProfile->user->organization_id !== $user->organization_id) {
                abort(403, 'You can only assign nominees from your organization.');
            }
        }
        
        // Check if nominee is already assigned to this category
        if ($category->nomineeProfiles()->where('nominee_profile_id', $nomineeProfile->id)->exists()) {
            return back()->with('error', 'Nominee is already assigned to this category.');
        }

        // Attach the nominee profile to the category
        // This assumes you have a pivot table like 'award_category_nominee_profile'
        // with 'award_category_id' and 'nominee_profile_id' columns.
        $category->nomineeProfiles()->attach($nomineeProfile->id, [
            'assigned_at' => now(),
            'assigned_by' => $user->id,
        ]);

        return back()->with('success', "Nominee {$nomineeProfile->display_name} assigned successfully.");
    }

    /**
     * Remove a nominee from a category.
     */
    public function removeNominee(AwardCategory $category, $nomineeProfileId)
    {
        $user = auth()->user();

        if (!$user->isSuperAdmin() && $category->organization_id !== $user->organization_id) {
            abort(403, 'Access denied.');
        }

        $nomineeProfile = \App\Models\NomineeProfile::findOrFail($nomineeProfileId);

        // Detach the nominee profile from the category
        $category->nomineeProfiles()->detach($nomineeProfile->id);

        return back()->with('success', "Nominee {$nomineeProfile->display_name} removed successfully.");
    }

    /**
     * Calculate revenue share based on user role and organization-specific revenue percentage.
     */
    private function calculateRevenueShare($amount, $organizationId = null)
    {
        $user = auth()->user();
        
        // If user is organization admin or super admin viewing specific organization data,
        // return organization's share based on their revenue percentage
        if (!$user->isSuperAdmin() || $organizationId) {
            $organization = Organization::find($organizationId ?: $user->organization_id);
            if ($organization) {
                $organizationShare = 100 - $organization->revenue_percentage;
                return $amount * ($organizationShare / 100);
            }
            // Fallback to 88% if organization not found
            return $amount * 0.88;
        }
        
        // If super admin viewing all system data, return platform's total share
        // This would be the sum of all revenue percentages from all organizations
        return $amount; // Return full amount for super admin dashboard overview
    }
}
