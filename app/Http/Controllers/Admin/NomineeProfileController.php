<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NomineeProfile;
use Illuminate\Http\Request;

class NomineeProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(NomineeProfile $nomineeProfile)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NomineeProfile $nomineeProfile)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, NomineeProfile $nomineeProfile)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NomineeProfile $nomineeProfile)
    {
        //
    }
}
