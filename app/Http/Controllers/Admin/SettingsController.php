<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Inertia\Inertia;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = [
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'app_timezone' => config('app.timezone'),
            'mail_from_address' => config('mail.from.address'),
            'mail_from_name' => config('mail.from.name'),
            'paystack_public_key' => config('services.paystack.public_key'),
            'maintenance_mode' => app()->isDownForMaintenance(),
        ];

        $systemInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'database_connection' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'queue_driver' => config('queue.default'),
            'mail_driver' => config('mail.default'),
            'storage_driver' => config('filesystems.default'),
        ];

        $diskUsage = [
            'total_space' => $this->formatBytes(disk_total_space(storage_path())),
            'free_space' => $this->formatBytes(disk_free_space(storage_path())),
            'used_space' => $this->formatBytes(disk_total_space(storage_path()) - disk_free_space(storage_path())),
        ];

        return Inertia::render('admin/settings/index', [
            'settings' => $settings,
            'systemInfo' => $systemInfo,
            'diskUsage' => $diskUsage,
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'app_timezone' => 'required|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
        ]);

        // In a real application, you would update the .env file or use a settings table
        // For now, we'll just return success
        
        return back()->with('success', 'Settings updated successfully.');
    }

    public function backup()
    {
        try {
            // Create database backup
            $filename = 'backup_' . date('Y_m_d_H_i_s') . '.sql';
            $path = storage_path('app/backups/' . $filename);
            
            // Ensure backup directory exists
            if (!Storage::exists('backups')) {
                Storage::makeDirectory('backups');
            }

            // Run database backup command
            $database = config('database.connections.' . config('database.default'));
            $command = sprintf(
                'mysqldump -h%s -u%s -p%s %s > %s',
                $database['host'],
                $database['username'],
                $database['password'],
                $database['database'],
                $path
            );

            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                return response()->download($path, $filename)->deleteFileAfterSend();
            } else {
                return back()->with('error', 'Failed to create database backup.');
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Backup failed: ' . $e->getMessage());
        }
    }

    public function logs(Request $request)
    {
        $logFile = storage_path('logs/laravel.log');
        $logs = [];

        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $logLines = explode("\n", $logContent);
            
            // Get last 100 lines
            $logLines = array_slice($logLines, -100);
            
            foreach ($logLines as $line) {
                if (trim($line) && preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                    $logs[] = [
                        'timestamp' => $matches[1],
                        'content' => $line,
                        'level' => $this->extractLogLevel($line),
                    ];
                }
            }
        }

        // System logs
        $systemLogs = [
            'error_logs' => $this->getErrorLogs(),
            'access_logs' => $this->getAccessLogs(),
            'performance_logs' => $this->getPerformanceLogs(),
        ];

        return Inertia::render('admin/SettingsLogs', [
            'logs' => array_reverse($logs),
            'systemLogs' => $systemLogs,
        ]);
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private function extractLogLevel($logLine)
    {
        if (strpos($logLine, '.ERROR:') !== false) return 'error';
        if (strpos($logLine, '.WARNING:') !== false) return 'warning';
        if (strpos($logLine, '.INFO:') !== false) return 'info';
        if (strpos($logLine, '.DEBUG:') !== false) return 'debug';
        return 'unknown';
    }

    private function getErrorLogs()
    {
        // Get recent error logs from database or log files
        return [
            'total_errors' => 0,
            'recent_errors' => [],
        ];
    }

    private function getAccessLogs()
    {
        // Get access logs if available
        return [
            'total_requests' => 0,
            'unique_visitors' => 0,
        ];
    }

    private function getPerformanceLogs()
    {
        // Get performance metrics
        return [
            'avg_response_time' => 0,
            'slow_queries' => 0,
        ];
    }
} 