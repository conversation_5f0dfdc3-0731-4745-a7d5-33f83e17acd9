<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Mail;
use App\Mail\OrganizationApprovedMail;
use App\Mail\OrganizationRejectedMail;
use App\Services\SmsService;

class AdminPendingOrganizationController extends Controller
{
    /**
     * Display a list of pending organization registrations.
     */
    public function index(): Response
    {
        $pendingOrganizations = User::where('role', User::ROLE_ADMIN)
            ->where('approval_status', 'pending')
            ->whereNotNull('organization_id')
            ->with('organization') 
            ->get();

        return Inertia::render('admin/pending-organizations/index', [
            'pendingOrganizations' => $pendingOrganizations,
        ]);
    }

    /**
     * Approve an organization registration.
     */
    public function approve(Request $request, User $user): RedirectResponse
    {
        \Log::info('=== APPROVAL PROCESS STARTED ===', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_status' => $user->approval_status,
            'request_data' => $request->all()
        ]);

        // Validate revenue percentage input
        $request->validate([
            'revenue_percentage' => 'required|numeric|min:0|max:100',
        ]);

        // Ensure the user has pending status and is associated with an organization
        if ($user->approval_status !== 'pending' || !$user->organization_id) {
            \Log::error('Approval failed - invalid user or status', [
                'user_id' => $user->id,
                'status' => $user->approval_status,
                'organization_id' => $user->organization_id
            ]);
            return back()->withErrors(['message' => 'Invalid user or status.']);
        }
        
        // User is already an admin, just approve them
        $user->approval_status = 'approved';
        $user->approved_at = now();
        $user->approved_by = auth()->id();
        $user->save();

        \Log::info('User status updated to approved', [
            'user_id' => $user->id,
            'approved_at' => $user->approved_at,
            'approved_by' => $user->approved_by
        ]);

        // Activate the associated organization and set revenue percentage
        if ($user->organization) {
            $user->organization->is_active = true;
            $user->organization->revenue_percentage = $request->revenue_percentage;
            $user->organization->save();
            
            \Log::info('Organization activated', [
                'organization_id' => $user->organization->id,
                'organization_name' => $user->organization->name,
                'revenue_percentage' => $request->revenue_percentage
            ]);
            
            // Send approval email to the organization
            try {
                Mail::to($user->email)->send(new OrganizationApprovedMail($user->organization));
                \Log::info('Approval email sent successfully', ['user_email' => $user->email]);
            } catch (\Exception $e) {
                // Log the error but don't fail the approval process
                \Log::error('Failed to send organization approval email: ' . $e->getMessage());
            }

            // Send SMS notification to organization
            \Log::info('Starting SMS notification process', [
                'user_phone_number' => $user->phone_number,
                'user_phone' => $user->phone
            ]);

            $phoneNumber = $user->phone_number ?? $user->phone;
            if ($phoneNumber) {
                \Log::info('Phone number found, sending SMS', [
                    'phone_number' => $phoneNumber,
                    'organization_name' => $user->organization->name
                ]);

                $smsService = new SmsService();
                $smsResult = $smsService->notifyOrganizationApproval(
                    $user->organization->name,
                    $phoneNumber,
                    $request->revenue_percentage
                );
                
                if ($smsResult) {
                    \Log::info('Organization approval SMS sent successfully', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'organization_name' => $user->organization->name,
                        'phone_number' => $phoneNumber,
                        'revenue_percentage' => $request->revenue_percentage
                    ]);
                } else {
                    \Log::error('Organization approval SMS failed to send', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'organization_name' => $user->organization->name,
                        'phone_number' => $phoneNumber,
                        'revenue_percentage' => $request->revenue_percentage
                    ]);
                }
            } else {
                // Log warning if no phone number is available
                \Log::warning('Organization approved but no SMS sent - missing phone number', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'organization_name' => $user->organization->name,
                    'phone_number' => $user->phone_number,
                    'phone' => $user->phone
                ]);
            }
        } else {
            \Log::error('No organization found for user', ['user_id' => $user->id]);
            return back()->withErrors(['message' => 'Organization not found for this user.']);
        }

        \Log::info('=== APPROVAL PROCESS COMPLETED ===', [
            'user_id' => $user->id,
            'organization_name' => $user->organization->name
        ]);

        return back()->with('success', 'Organization registration approved with ' . $request->revenue_percentage . '% revenue rate.');
    }

    /**
     * Reject an organization registration.
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        // Validate rejection reason
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        // Ensure the user has pending status and is associated with an organization
        if ($user->approval_status !== 'pending' || !$user->organization_id) {
            return back()->withErrors(['message' => 'Invalid user or status.']);
        }

        // Update user status
        $user->approval_status = 'rejected';
        $user->rejection_reason = $request->rejection_reason;
        $user->save();

        // Optionally, deactivate or soft-delete the associated organization
        // For now, we just set the user status, the organization remains inactive (is_active = false)

        // Send rejection email to the organization if organization exists
        if ($user->organization) {
            try {
                Mail::to($user->email)->send(new OrganizationRejectedMail($user->organization));
            } catch (\Exception $e) {
                // Log the error but don't fail the rejection process
                \Log::error('Failed to send organization rejection email: ' . $e->getMessage());
            }

            // Send SMS notification to organization
            $phoneNumber = $user->phone_number ?? $user->phone;
            if ($phoneNumber) {
                $smsService = new SmsService();
                $smsResult = $smsService->notifyOrganizationRejection(
                    $user->organization->name,
                    $phoneNumber,
                    $request->rejection_reason
                );
                
                if ($smsResult) {
                    \Log::info('Organization rejection SMS sent successfully', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'organization_name' => $user->organization->name,
                        'phone_number' => $phoneNumber,
                        'rejection_reason' => $request->rejection_reason
                    ]);
                } else {
                    \Log::error('Organization rejection SMS failed to send', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'organization_name' => $user->organization->name,
                        'phone_number' => $phoneNumber,
                        'rejection_reason' => $request->rejection_reason
                    ]);
                }
            } else {
                // Log warning if no phone number is available
                \Log::warning('Organization rejected but no SMS sent - missing phone number', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'organization_name' => $user->organization->name,
                    'phone_number' => $user->phone_number,
                    'phone' => $user->phone
                ]);
            }
        }

        return back()->with('success', 'Organization registration rejected.');
    }
} 