<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Organization;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = Payment::with(['votes.nomineeProfile.awardCategories'])
            ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
            })
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('reference', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->organization_id && $user->isSuperAdmin(), function ($query, $orgId) {
                $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $orgId));
            })
            ->when($request->date_from, function ($query, $date) {
                $query->whereDate('created_at', '>=', $date);
            })
            ->when($request->date_to, function ($query, $date) {
                $query->whereDate('created_at', '<=', $date);
            });

        $payments = $query->latest()
            ->paginate(15)
            ->withQueryString();

        // Transform payments data
        $payments->getCollection()->transform(function ($payment) {
            $vote = $payment->votes->first();
            return [
                'id' => $payment->id,
                'reference' => $payment->reference,
                'amount' => $payment->amount,
                'status' => $payment->status,
                'email' => $payment->email,
                'phone' => $payment->phone,
                'payment_method' => $payment->payment_method ?? 'Card',
                'nominee' => $vote ? $vote->nomineeProfile->display_name : 'N/A',
                'category' => $vote && $vote->award_category ? $vote->award_category->name : 'N/A',
                'votes_count' => $payment->votes->count(),
                'created_at' => $payment->created_at,
                'updated_at' => $payment->updated_at,
            ];
        });

        $organizations = $user->isSuperAdmin() ? Organization::select('id', 'name')->get() : null;

        // Payment statistics
        $stats = [
            'total_payments' => Payment::when(!$user->isSuperAdmin(), function($query) use ($user) {
                return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
            })->count(),
            'completed_payments' => Payment::where('status', 'success')
                ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
                })->count(),
            'pending_payments' => Payment::where('status', 'pending')
                ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
                })->count(),
            'failed_payments' => Payment::where('status', 'failed')
                ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                    return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
                })->count(),
            'total_revenue' => $this->calculateRevenueShare(
                Payment::where('status', 'success')
                    ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                        return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
                    })->sum('amount'),
                !$user->isSuperAdmin() ? $user->organization_id : null
            ),
            'today_revenue' => $this->calculateRevenueShare(
                Payment::where('status', 'success')
                    ->when(!$user->isSuperAdmin(), function($query) use ($user) {
                        return $query->whereHas('votes.nomineeProfile.awardCategories', fn($q) => $q->where('organization_id', $user->organization_id));
                    })
                    ->whereDate('created_at', today())
                    ->sum('amount'),
                !$user->isSuperAdmin() ? $user->organization_id : null
            ),
        ];

        return Inertia::render('admin/payments/index', [
            'payments' => $payments,
            'organizations' => $organizations,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'organization_id', 'date_from', 'date_to']),
            'statuses' => [
                ['value' => 'pending', 'label' => 'Pending'],
                ['value' => 'success', 'label' => 'Success'],
                ['value' => 'failed', 'label' => 'Failed'],
            ],
        ]);
    }

    public function show(Payment $payment)
    {
        $user = auth()->user();
        
        // Check if user can view this payment
        if (!$user->isSuperAdmin()) {
            $hasAccess = $payment->votes()->whereHas('nomineeProfile.awardCategories', function($query) use ($user) {
                $query->where('organization_id', $user->organization_id);
            })->exists();
            
            if (!$hasAccess) {
                abort(403, 'Access denied.');
            }
        }

        $payment->load(['votes.nomineeProfile.awardCategories']);

        // Payment details
        $paymentData = [
            'id' => $payment->id,
            'reference' => $payment->reference,
            'amount' => $payment->amount,
            'status' => $payment->status,
            'email' => $payment->email,
            'phone' => $payment->phone,
            'payment_method' => $payment->payment_method ?? 'Card',
            'gateway_response' => $payment->gateway_response,
            'created_at' => $payment->created_at,
            'updated_at' => $payment->updated_at,
            'votes' => $payment->votes->map(function ($vote) {
                return [
                    'id' => $vote->id,
                    'nominee' => $vote->nomineeProfile->display_name,
                    'category' => $vote->award_category ? $vote->award_category->name : 'N/A',
                    'votes_count' => $vote->votes_count,
                    'created_at' => $vote->created_at,
                ];
            }),
        ];

        return Inertia::render('admin/ShowPayment', [
            'payment' => $paymentData,
        ]);
    }

    /**
     * Calculate revenue share based on user role and organization-specific revenue percentage.
     */
    private function calculateRevenueShare($amount, $organizationId = null)
    {
        $user = auth()->user();
        
        // If user is organization admin or super admin viewing specific organization data,
        // return organization's share based on their revenue percentage
        if (!$user->isSuperAdmin() || $organizationId) {
            $organization = Organization::find($organizationId ?: $user->organization_id);
            if ($organization) {
                $organizationShare = 100 - $organization->revenue_percentage;
                return $amount * ($organizationShare / 100);
            }
            // Fallback to 88% if organization not found
            return $amount * 0.88;
        }
        
        // If super admin viewing all system data, return platform's total share
        return $amount; // Return full amount for super admin dashboard overview
    }
}