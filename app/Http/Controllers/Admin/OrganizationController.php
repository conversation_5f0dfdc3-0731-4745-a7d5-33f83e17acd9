<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\User;
use App\Models\Payment;
use App\Models\Vote;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;

class OrganizationController extends Controller
{
    public function index(Request $request)
    {
        $query = Organization::withCount(['users', 'awardCategories'])
            ->with(['awardCategories.nomineeProfiles.votes.payment', 'admins'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('contact_email', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($query, $status) {
                if ($status === 'active') {
                    $query->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $query->where('is_active', false);
                }
            });

        $organizations = $query->latest()
            ->paginate(15)
            ->withQueryString();

        // Transform the organization data to include calculated fields
        $organizations->getCollection()->transform(function ($organization) {
            // Calculate individual organization stats
            $totalVotes = $organization->awardCategories->sum(function ($category) {
                return $category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->count();
                });
            });

            $totalRevenue = $organization->awardCategories->sum(function ($category) {
                return $category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->sum(function ($vote) {
                        return $vote->payment && $vote->payment->status === 'success' ? $vote->payment->amount : 0;
                    });
                });
            });

            // Apply revenue sharing to organization revenue display
            $sharedRevenue = $this->calculateRevenueShare($totalRevenue, $organization->id);

            return [
                'id' => $organization->id,
                'slug' => $organization->slug,
                'name' => $organization->name,
                'email' => $organization->contact_email,
                'phone' => $organization->contact_phone,
                'website' => $organization->website,
                'status' => $organization->is_active ? 'active' : 'inactive',
                'created_at' => $organization->created_at,
                'users_count' => $organization->users_count,
                'categories_count' => $organization->award_categories_count,
                'total_revenue' => 'GHS ' . number_format($sharedRevenue, 2),
                'total_votes' => $totalVotes,
                'admin' => $organization->admins->first() ? [
                    'id' => $organization->admins->first()->id,
                    'name' => $organization->admins->first()->name,
                    'email' => $organization->admins->first()->email,
                ] : null,
            ];
        });

        // Calculate stats for the overview cards
        $stats = [
            'total_organizations' => Organization::count(),
            'active_organizations' => Organization::where('is_active', true)->count(),
            'total_revenue' => Payment::where('status', 'success')->sum('amount'),
            'total_votes' => Vote::count(),
        ];

        return Inertia::render('admin/organizations/index', [
            'organizations' => $organizations,
            'filters' => $request->only(['search', 'status']),
            'stats' => $stats,
        ]);
    }

    public function create()
    {
        return Inertia::render('admin/organizations/create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|max:2048',
            'website' => 'nullable|url|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
        ]);

        $logoPath = null;
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('organization-logos', 'public');
        }

        Organization::create([
            'name' => $request->name,
            'description' => $request->description,
            'logo' => $logoPath,
            'website' => $request->website,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'is_active' => true,
        ]);

        return redirect()->route('admin.organizations.index')
            ->with('success', 'Organization created successfully.');
    }

    public function show(Organization $organization)
    {
        $organization->load(['users', 'awardCategories.nomineeProfiles']);

        // Calculate organization statistics
        $rawTotalRevenue = $organization->awardCategories->sum(function ($category) {
            return $category->nomineeProfiles->sum(function ($profile) {
                return $profile->votes->sum(function ($vote) {
                    return $vote->payment && $vote->payment->status === 'success' ? $vote->payment->amount : 0;
                });
            });
        });

        $stats = [
            'total_users' => $organization->users->count(),
            'total_admins' => $organization->users->where('role', User::ROLE_ADMIN)->count(),
            'total_nominees' => $organization->users->where('role', User::ROLE_NOMINEE)->count(),
            'total_categories' => $organization->awardCategories->count(),
            'active_categories' => $organization->awardCategories->where('is_active', true)->count(),
            'total_nominees_profiles' => $organization->awardCategories->sum(function ($category) {
                return $category->nomineeProfiles->count();
            }),
            'total_votes' => $organization->awardCategories->sum(function ($category) {
                return $category->nomineeProfiles->sum(function ($profile) {
                    return $profile->votes->count();
                });
            }),
            'total_revenue' => $this->calculateRevenueShare($rawTotalRevenue, $organization->id),
        ];

        return Inertia::render('admin/organizations/show', [
            'organization' => $organization,
            'stats' => $stats,
        ]);
    }

    public function edit(Organization $organization)
    {
        return Inertia::render('admin/organizations/edit', [
            'organization' => $organization,
        ]);
    }

    public function update(Request $request, Organization $organization)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|max:2048',
            'website' => 'nullable|url|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
        ]);

        $updateData = $request->only(['name', 'description', 'website', 'contact_email', 'contact_phone']);

        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($organization->logo) {
                Storage::disk('public')->delete($organization->logo);
            }
            $updateData['logo'] = $request->file('logo')->store('organization-logos', 'public');
        }

        // Do not update slug on update
        // $updateData['slug'] = Str::slug($request->name);

        $organization->update($updateData);

        return redirect()->route('admin.organizations.show', $organization->slug)
            ->with('success', 'Organization updated successfully.');
    }

    public function destroy(Organization $organization)
    {
        // Check if organization has associated data
        if ($organization->users()->exists() || $organization->awardCategories()->exists()) {
            return back()->with('error', 'Cannot delete organization with associated users or award categories.');
        }

        // Delete logo if exists
        if ($organization->logo) {
            \Storage::disk('public')->delete($organization->logo);
        }

        $organization->delete();

        return redirect()->route('admin.organizations.index')
            ->with('success', 'Organization deleted successfully.');
    }

    public function toggleStatus(Organization $organization)
    {
        $organization->update([
            'is_active' => !$organization->is_active,
        ]);

        $status = $organization->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Organization {$status} successfully.");
    }

    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'organization_ids' => 'required|array|min:1',
            'organization_ids.*' => 'exists:organizations,id',
        ]);

        $organizations = Organization::whereIn('id', $request->organization_ids)->get();

        switch ($request->action) {
            case 'activate':
                $organizations->each(function ($org) {
                    $org->update(['is_active' => true]);
                });
                $message = 'Organizations activated successfully.';
                break;

            case 'deactivate':
                $organizations->each(function ($org) {
                    $org->update(['is_active' => false]);
                });
                $message = 'Organizations deactivated successfully.';
                break;

            case 'delete':
                $deletedCount = 0;
                $organizations->each(function ($org) use (&$deletedCount) {
                    if (!$org->users()->exists() && !$org->awardCategories()->exists()) {
                        if ($org->logo) {
                            \Storage::disk('public')->delete($org->logo);
                        }
                        $org->delete();
                        $deletedCount++;
                    }
                });
                $message = "{$deletedCount} organizations deleted successfully.";
                break;

            default:
                $message = 'Invalid action.';
        }

        return back()->with('success', $message);
    }

    /**
     * Calculate revenue share based on user role and organization-specific revenue percentage.
     */
    private function calculateRevenueShare($amount, $organizationId = null)
    {
        $user = auth()->user();
        
        // If user is organization admin or super admin viewing specific organization data,
        // return organization's share based on their revenue percentage
        if (!$user->isSuperAdmin() || $organizationId) {
            $organization = Organization::find($organizationId ?: $user->organization_id);
            if ($organization) {
                $organizationShare = 100 - $organization->revenue_percentage;
                return $amount * ($organizationShare / 100);
            }
            // Fallback to 88% if organization not found
            return $amount * 0.88;
        }
        
        // If super admin viewing all system data, return platform's total share
        return $amount; // Return full amount for super admin dashboard overview
    }
}
