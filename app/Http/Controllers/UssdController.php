<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\UssdSession;
use App\Services\UssdService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UssdController extends Controller
{
    protected UssdService $ussdService;

    public function __construct(UssdService $ussdService)
    {
        $this->ussdService = $ussdService;
    }

    /**
     * Handle USSD webhook requests from NALO
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            // Validate incoming request
            $validator = Validator::make($request->all(), [
                'USERID' => 'required|string',
                'MSISDN' => 'required|string|size:12',
                'USERDATA' => 'nullable|string|max:120',
                'MSGTYPE' => 'required|boolean',
                'NETWORK' => 'required|string|max:10',
                'SESSIONID' => 'required|string',
            ]);

            if ($validator->fails()) {
                Log::error('USSD validation failed', [
                    'errors' => $validator->errors(),
                    'request' => $request->all()
                ]);
                
                return $this->errorResponse($request, 'Invalid request format');
            }

            $sessionId = $request->input('SESSIONID');
            $msisdn = $request->input('MSISDN');
            $userInput = $request->input('USERDATA', '');
            $isFirstRequest = $request->input('MSGTYPE');
            $network = $request->input('NETWORK');
            $userId = $request->input('USERID');

            Log::info('USSD Request', [
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'user_input' => $userInput,
                'is_first' => $isFirstRequest,
                'network' => $network
            ]);

            // Get or create session
            $session = $this->getOrCreateSession($sessionId, $msisdn, $userId, $network);

            // Process the request
            $response = $this->ussdService->processRequest($session, $userInput, $isFirstRequest);

            // Update session
            $session->updateActivity();

            Log::info('USSD Response', [
                'session_id' => $sessionId,
                'response' => $response
            ]);

            return response()->json([
                'USERID' => $userId,
                'MSISDN' => $msisdn,
                'USERDATA' => $userInput,
                'MSG' => $response['message'],
                'MSGTYPE' => $response['msgtype']
            ]);

        } catch (\Exception $e) {
            Log::error('USSD webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return $this->errorResponse($request, 'System error. Please try again.');
        }
    }

    /**
     * Get or create USSD session
     */
    private function getOrCreateSession(string $sessionId, string $msisdn, string $userId, string $network): UssdSession
    {
        $session = UssdSession::where('session_id', $sessionId)->first();

        if (!$session) {
            $session = UssdSession::create([
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'user_id' => $userId,
                'network' => $network,
                'current_screen' => 'main_menu',
                'last_activity' => Carbon::now(),
                'is_active' => true,
            ]);
        }

        return $session;
    }

    /**
     * Return error response
     */
    private function errorResponse(Request $request, string $message): JsonResponse
    {
        return response()->json([
            'USERID' => $request->input('USERID', ''),
            'MSISDN' => $request->input('MSISDN', ''),
            'USERDATA' => $request->input('USERDATA', ''),
            'MSG' => $message . "\n\n0. Exit",
            'MSGTYPE' => false
        ]);
    }
}
