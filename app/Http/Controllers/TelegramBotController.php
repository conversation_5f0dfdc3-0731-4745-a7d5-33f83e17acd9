<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Telegram\Bot\Laravel\Facades\Telegram;
use Telegram\Bot\Keyboard\Keyboard;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use App\Services\PaystackService;
use App\Services\TelegramVotingService;
use Illuminate\Support\Facades\Log;

class TelegramBotController extends Controller
{
    protected TelegramVotingService $telegramVotingService;
    protected PaystackService $paystackService;

    public function __construct(TelegramVotingService $telegramVotingService, PaystackService $paystackService)
    {
        $this->telegramVotingService = $telegramVotingService;
        $this->paystackService = $paystackService;
    }    /**
     * Handle incoming webhook from Telegram
     * FAST implementation: Respond immediately, process in background
     */
    public function webhook(Request $request)
    {
        // Use the SDK to get the update, which is more robust than parsing JSO<PERSON> from the request.
        $update = Telegram::getWebhookUpdate();

        // The rest of the method expects an array, so convert the Update object.
        $data = $update->toArray();

        // Validate basic structure
        if (!isset($data['update_id'])) {
            return response('OK', 200); // Still return OK to prevent retries
        }
        
        try {
            // Process critical updates immediately, queue others
            if (isset($data['message'])) {
                $this->handleMessageSync($data['message']);
            } elseif (isset($data['callback_query'])) {
                $this->handleCallbackQuerySync($data['callback_query']);
            }
        } catch (\Exception $e) {
            // Log error but don't fail the webhook
            Log::error('Webhook processing error', [
                'error' => $e->getMessage(),
                'update_id' => $data['update_id']
            ]);
        }
        
        return response('OK', 200);
    }
    
    /**
     * Handle message synchronously (fast path)
     */
    private function handleMessageSync($messageData)
    {
        $chatId = $messageData['chat']['id'];
        $text = $messageData['text'] ?? '';
        $userId = $messageData['from']['id'];
        
        // Handle only critical commands immediately
        if ($text === '/start') {
            $this->sendQuickWelcome($chatId);
        } elseif ($text === '/categories') {
            $this->showCategories($chatId);
        } else {
            // Queue other processing
            $this->processMessage($messageData);
        }
    }
    
    /**
     * Handle callback query synchronously (fast path)
     */
    private function handleCallbackQuerySync($callbackData)
    {
        // Answer callback immediately to remove loading state
        try {
            Telegram::answerCallbackQuery([
                'callback_query_id' => $callbackData['id']
            ]);
        } catch (\Exception $e) {
            // Don't let this fail the webhook
        }
        
        // Process the callback
        $this->processCallbackQuery($callbackData);
    }
    
    /**
     * Send quick welcome message
     */
    private function sendQuickWelcome($chatId)
    {
        try {
            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => '🎉 Welcome to VoteYourFav! Use /categories to browse and vote.',
                'disable_notification' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Quick welcome failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Process message from webhook data
     */
    private function processMessage($messageData)
    {
        if (!isset($messageData['chat']['id']) || !isset($messageData['from']['id'])) {
            return;
        }
        
        $chatId = $messageData['chat']['id'];
        $userId = $messageData['from']['id'];
        $text = $messageData['text'] ?? '';
        
        Log::info('Processing message', [
            'chat_id' => $chatId,
            'user_id' => $userId,
            'text' => $text
        ]);
        
        // Handle commands
        if (str_starts_with($text, '/')) {
            $this->handleCommand($chatId, $text, $userId);
        } elseif ($text) {
            $this->handleTextInput($chatId, $text, $userId);
        }
    }
    
    /**
     * Process callback query from webhook data
     */
    private function processCallbackQuery($callbackData)
    {
        if (!isset($callbackData['message']['chat']['id']) || !isset($callbackData['from']['id'])) {
            return;
        }
        
        $chatId = $callbackData['message']['chat']['id'];
        $userId = $callbackData['from']['id'];
        $data = $callbackData['data'] ?? '';
        $callbackQueryId = $callbackData['id'] ?? '';
        
        Log::info('Processing callback query', [
            'chat_id' => $chatId,
            'user_id' => $userId,
            'data' => $data
        ]);
        
        // Answer callback query first
        try {
            Telegram::answerCallbackQuery(['callback_query_id' => $callbackQueryId]);
        } catch (\Exception $e) {
            Log::error('Failed to answer callback query', ['error' => $e->getMessage()]);
        }
        
        // Process the callback
        $this->handleCallbackData($chatId, $data, $userId);
    }

    /**
     * Handle text messages and commands (accepts both SDK objects and arrays)
     */
    private function handleMessage($message)
    {
        try {
            if (!$message) {
                Log::error('No message received');
                return;
            }

            // Handle both SDK objects and raw arrays
            if (is_array($message)) {
                $chatId = $message['chat']['id'];
                $text = $message['text'] ?? '';
                $userId = $message['from']['id'];
            } else {
                // SDK object
                $chatId = $message->getChat()->getId();
                $text = $message->getText();
                $userId = $message->getFrom()->getId();
            }

            Log::info('Handling Telegram message', [
                'chat_id' => $chatId,
                'user_id' => $userId,
                'text' => $text,
                'is_command' => str_starts_with($text ?? '', '/')
            ]);

            // Handle commands
            if ($text && str_starts_with($text, '/')) {
                $this->handleCommand($chatId, $text, $userId);
                return;
            }

            // Handle regular text (could be search queries)
            if ($text) {
                $this->handleTextInput($chatId, $text, $userId);
            } else {
                Log::info('Message has no text content, skipping');
            }

        } catch (\Exception $e) {
            Log::error('Error in handleMessage', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * Handle bot commands
     */
    private function handleCommand($chatId, $command, $userId)
    {
        $parts = explode(' ', $command, 2);
        $cmd = $parts[0];
        $args = $parts[1] ?? '';

        switch ($cmd) {
            case '/start':
                $this->sendWelcomeMessage($chatId);
                break;
            
            case '/categories':
                $this->showCategories($chatId);
                break;
            
            case '/vote':
                if ($args) {
                    $this->showNomineeVoting($chatId, $args);
                } else {
                    $this->sendMessage($chatId, "Please provide a nominee code. Usage: /vote [code]\nExample: /vote A1B2C or /vote JOHN001");
                }
                break;
            
            case '/search':
                if ($args) {
                    $this->searchNominees($chatId, $args);
                } else {
                    $this->sendMessage($chatId, "Please provide a search term. Usage: /search [query]");
                }
                break;
            
            case '/status':
                if ($args) {
                    $this->checkPaymentStatus($chatId, $args);
                } else {
                    $this->sendMessage($chatId, "Please provide a payment reference. Usage: /status [payment_ref]");
                }
                break;
            
            case '/share':
                if ($args) {
                    $this->shareNominee($chatId, $args);
                } else {
                    $this->sendMessage($chatId, "Please provide a nominee code. Usage: /share [code]\nExample: /share A1B2C or /share JOHN001");
                }
                break;
            
            case '/help':
                $this->sendHelpMessage($chatId);
                break;
            
            default:
                $this->sendMessage($chatId, "Unknown command. Type /help to see available commands.");
        }
    }

    /**
     * Send welcome message with main menu
     */
    private function sendWelcomeMessage($chatId)
    {
        $keyboard = Keyboard::make()
            ->inline()
            ->row([
                Keyboard::inlineButton(['text' => '🏆 Browse Categories', 'callback_data' => 'categories']),
                Keyboard::inlineButton(['text' => '🔍 Search Nominees', 'callback_data' => 'search'])
            ])
            ->row([
                Keyboard::inlineButton(['text' => '📊 Help', 'callback_data' => 'help']),
                Keyboard::inlineButton(['text' => '🌐 Visit Website', 'url' => 'https://voteyourfav.com'])
            ]);        $message = "🗳️ *Welcome to Vote Your Fav Bot!*\n\n";
        $message .= "Cast your votes for your favorite nominees in various award categories.\n\n";
        $message .= "🔹 Browse categories to see all nominees\n";
        $message .= "🔹 Search for specific nominees\n";
        $message .= "🔹 Vote securely with mobile money\n";
        $message .= "🔹 Share nominee links with friends\n\n";
        $message .= "💡 *Quick Vote Examples:*\n";
        $message .= "• `/vote A1B2C` (5-character code)\n";
        $message .= "• `/vote JOHN001` (nominee code)\n\n";
        $message .= "Choose an option below to get started:";

        $this->sendMessage($chatId, $message, $keyboard);
    }

    /**
     * Show available categories
     */
    private function showCategories($chatId)
    {
        $categories = AwardCategory::where('is_active', true)
            ->where('is_suspended', false)
            ->with('organization')
            ->get();

        if ($categories->isEmpty()) {
            $this->sendMessage($chatId, "No active categories available at the moment.");
            return;
        }

        $keyboard = Keyboard::make()->inline();
        
        foreach ($categories as $category) {
            if ($category->isVotingActive()) {
                $keyboard->row([
                    Keyboard::inlineButton([
                        'text' => "🏆 {$category->name}",
                        'callback_data' => "category_{$category->slug}"
                    ])
                ]);
            }
        }

        $keyboard->row([
            Keyboard::inlineButton(['text' => '« Back to Menu', 'callback_data' => 'start'])
        ]);

        $message = "🏆 *Available Award Categories:*\n\n";
        $message .= "Select a category to view nominees:";

        $this->sendMessage($chatId, $message, $keyboard);
    }    /**
     * Handle callback queries (inline button presses)
     */
    private function handleCallbackQuery($callbackQuery)
    {
        try {
            $chatId = $callbackQuery['message']['chat']['id'];
            $data = $callbackQuery['data'] ?? '';
            $userId = $callbackQuery['from']['id'];

            Log::info('Handling callback query', [
                'chat_id' => $chatId,
                'user_id' => $userId,
                'callback_data' => $data
            ]);

            // Answer the callback query to remove loading state
            Telegram::answerCallbackQuery([
                'callback_query_id' => $callbackQuery['id']
            ]);

            Log::info('Callback query answered, processing action', [
                'action' => $data
            ]);

            if ($data === 'categories') {
                $this->showCategories($chatId);
            } elseif ($data === 'search') {
                $this->sendMessage($chatId, "🔍 Type your search query to find nominees:");
            } elseif ($data === 'help') {
                $this->sendHelpMessage($chatId);
            } elseif ($data === 'start') {
                $this->sendWelcomeMessage($chatId);
            } elseif (str_starts_with($data, 'category_')) {
                $categorySlug = str_replace('category_', '', $data);
                $this->showCategoryNominees($chatId, $categorySlug);
            } elseif (str_starts_with($data, 'nominee_')) {
                $nomineeSlug = str_replace('nominee_', '', $data);
                $this->telegramVotingService->showNomineeVoting($chatId, $nomineeSlug);
            } elseif (str_starts_with($data, 'quickvote_')) {
                // Handle quick voting: quickvote_nomineeSlug_voteCount
                $parts = explode('_', $data, 3);
                if (count($parts) === 3) {
                    $nomineeSlug = $parts[1];
                    $voteCount = (int)$parts[2];
                    $this->telegramVotingService->handleQuickVote($chatId, $nomineeSlug, $voteCount, $userId);
                }
            } elseif (str_starts_with($data, 'customvote_')) {
                $nomineeSlug = str_replace('customvote_', '', $data);
                $this->askForCustomVoteAmount($chatId, $nomineeSlug);
            } elseif (str_starts_with($data, 'share_')) {
                $nomineeSlug = str_replace('share_', '', $data);
                $this->telegramVotingService->shareNominee($chatId, $nomineeSlug);
            } elseif (str_starts_with($data, 'status_')) {
                $reference = str_replace('status_', '', $data);
                $this->telegramVotingService->checkPaymentStatus($chatId, $reference);
            } else {
                Log::warning('Unknown callback data received', [
                    'callback_data' => $data
                ]);
                $this->sendMessage($chatId, "Unknown action. Please try again.");
            }

            Log::info('Callback query action completed successfully', [
                'action' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Error in handleCallbackQuery: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'callback_data' => $callbackQuery['data'] ?? 'unknown'
            ]);
            
            try {
                $chatId = $callbackQuery['message']['chat']['id'];
                $this->sendMessage($chatId, "Sorry, there was an error processing your request. Please try again.");
            } catch (\Exception $innerE) {
                Log::error('Failed to send error message: ' . $innerE->getMessage());
            }
        }
    }

    /**
     * Show nominees in a category
     */
    private function showCategoryNominees($chatId, $categorySlug)
    {
        $category = AwardCategory::where('slug', $categorySlug)
            ->where('is_active', true)
            ->first();

        if (!$category || !$category->isVotingActive()) {
            $this->sendMessage($chatId, "Category not found or voting is not active.");
            return;
        }

        $nominees = $category->nomineeProfiles()
            ->approved()
            ->byVotes()
            ->take(10)
            ->get();

        if ($nominees->isEmpty()) {
            $this->sendMessage($chatId, "No nominees found in this category.");
            return;
        }

        $keyboard = Keyboard::make()->inline();
        
        foreach ($nominees as $index => $nominee) {
            $position = $index + 1;
            $votes = $nominee->total_votes;
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => "#{$position} {$nominee->display_name} ({$votes} votes)",
                    'callback_data' => "nominee_{$nominee->slug}"
                ])
            ]);
        }

        $keyboard->row([
            Keyboard::inlineButton(['text' => '« Back to Categories', 'callback_data' => 'categories'])
        ]);

        $message = "🏆 *{$category->name}*\n\n";
        $message .= "📍 Organization: {$category->organization->name}\n";
        $message .= "💰 Price per vote: GHS {$category->price_per_vote}\n\n";
        $message .= "🏅 *Top Nominees:*\n";
        $message .= "Tap on a nominee to vote:";

        $this->sendMessage($chatId, $message, $keyboard);
    }    /**
     * Send a message to chat
     */
    private function sendMessage($chatId, $text, $keyboard = null)
    {
        try {
            $params = [
                'chat_id' => $chatId,
                'text' => $text,
                'parse_mode' => 'Markdown',
                'disable_web_page_preview' => true
            ];

            if ($keyboard) {
                $params['reply_markup'] = $keyboard;
            }

            Log::info('Sending Telegram message', [
                'chat_id' => $chatId,
                'text_length' => strlen($text),
                'has_keyboard' => $keyboard !== null
            ]);

            return Telegram::sendMessage($params);
        } catch (\Exception $e) {
            Log::error('Failed to send Telegram message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage(),
                'params' => $params ?? []
            ]);
            throw $e;
        }
    }

    /**
     * Send help message
     */    private function sendHelpMessage($chatId)
    {
        $message = "📚 *Vote Your Fav Bot Help*\n\n";
        $message .= "*Available Commands:*\n";
        $message .= "/start - Show main menu\n";
        $message .= "/categories - Browse award categories\n";
        $message .= "/vote [code] - Vote for a nominee (e.g., /vote A1B2C or /vote JOHN001)\n";
        $message .= "/search [query] - Search nominees\n";
        $message .= "/status [payment_ref] - Check payment status\n";
        $message .= "/share [code] - Get shareable link (e.g., /share A1B2C)\n";
        $message .= "/help - Show this help message\n\n";
        $message .= "*How it works:*\n";
        $message .= "1️⃣ Browse categories or search nominees\n";
        $message .= "2️⃣ Select a nominee to vote for\n";
        $message .= "3️⃣ Choose number of votes to purchase\n";
        $message .= "4️⃣ Complete payment via secure link\n";
        $message .= "5️⃣ Votes are counted instantly!\n\n";
        $message .= "Need more help? Visit our website: " . url('/');

        $keyboard = Keyboard::make()
            ->inline()
            ->row([
                Keyboard::inlineButton(['text' => '« Back to Menu', 'callback_data' => 'start'])
            ]);

        $this->sendMessage($chatId, $message, $keyboard);
    }    /**
     * Handle text input (search queries, etc.)
     */
    private function handleTextInput($chatId, $text, $userId)
    {
        // Check if user is in custom vote mode
        $nomineeSlug = session("telegram_bot_custom_vote_nominee_{$chatId}");
        
        if ($nomineeSlug && is_numeric(trim($text))) {
            $voteCount = (int)trim($text);
              if ($voteCount >= 1) {
                // Clear the session
                session()->forget("telegram_bot_custom_vote_nominee_{$chatId}");
                
                // Process the custom vote
                $this->telegramVotingService->handleCustomVote($chatId, $nomineeSlug, $voteCount, $userId);
                return;
            } else {
                $this->sendMessage($chatId, "Please enter a valid number of votes (minimum 1):");
                return;
            }
        }
        
        // If user types something, treat it as a search query
        if (strlen(trim($text)) > 2) {
            $this->telegramVotingService->searchNominees($chatId, trim($text));
        } else {
            $this->sendMessage($chatId, "Please type at least 3 characters to search for nominees.");
        }
    }

    /**
     * Show nominee voting interface using the service
     */
    private function showNomineeVoting($chatId, $nomineeSlug)
    {
        $this->telegramVotingService->showNomineeVoting($chatId, $nomineeSlug);
    }

    /**
     * Search nominees using the service
     */
    private function searchNominees($chatId, $query)
    {
        $this->telegramVotingService->searchNominees($chatId, $query);
    }

    /**
     * Check payment status using the service
     */
    private function checkPaymentStatus($chatId, $reference)
    {
        $this->telegramVotingService->checkPaymentStatus($chatId, $reference);
    }

    /**
     * Share nominee using the service
     */
    private function shareNominee($chatId, $nomineeSlug)
    {
        $this->telegramVotingService->shareNominee($chatId, $nomineeSlug);
    }

    /**
     * Initiate voting process
     */
    private function initiateVoting($chatId, $nomineeSlug, $userId)
    {
        $this->showNomineeVoting($chatId, $nomineeSlug);
    }

    /**
     * Set webhook for the bot
     */
    public function setWebhook()
    {
        try {
            $webhookUrl = route('telegram.webhook');
            
            $response = Telegram::setWebhook([
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query']
            ]);

            Log::info('Telegram webhook set', [
                'url' => $webhookUrl,
                'response' => $response
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Webhook set successfully',
                'url' => $webhookUrl,
                'response' => $response
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to set Telegram webhook: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to set webhook: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public method for testing message processing
     * This method is specifically designed for testing environments
     * and should not be used in production webhook handling
     */
    public function processMessageForTesting($message)
    {
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';
        $userId = $message['from']['id'];

        // Handle commands
        if (str_starts_with($text, '/')) {
            $this->handleCommand($chatId, $text, $userId);
            return;
        }

        // Handle regular text (could be search queries)
        $this->handleTextInput($chatId, $text, $userId);
    }

    /**
     * Public method for testing callback query processing
     * This method is specifically designed for testing environments
     * and should not be used in production webhook handling
     */
    public function processCallbackQueryForTesting($callbackQuery)
    {
        $this->handleCallbackQuery($callbackQuery);
    }

    /**
     * Get bot info and webhook status
     */
    public function getBotInfo()
    {
        try {
            $me = Telegram::getMe();
            $webhookInfo = Telegram::getWebhookInfo();

            return response()->json([
                'bot_info' => $me,
                'webhook_info' => $webhookInfo
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get bot info: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bot info: ' . $e->getMessage()
            ], 500);
        }
    }    /**
     * Ask user for custom vote amount
     */
    private function askForCustomVoteAmount($chatId, $nomineeSlug)
    {
        $nominee = NomineeProfile::where('slug', $nomineeSlug)
            ->with('awardCategories')
            ->first();
        
        if (!$nominee) {
            $this->sendMessage($chatId, "Nominee not found.");
            return;
        }

        $category = $nominee->awardCategories->first();
        if (!$category) {
            $this->sendMessage($chatId, "No category found for this nominee.");
            return;
        }

        $message = "💰 *Custom Vote Amount*\n\n";
        $message .= "How many votes would you like to purchase for *{$nominee->display_name}*?\n\n";
        $message .= "💵 Price per vote: GHS {$category->price_per_vote}\n\n";        $message .= "Please type the number of votes (minimum 1):";

        // Store nominee slug in user session/cache for next input
        session()->put("telegram_bot_custom_vote_nominee_{$chatId}", $nomineeSlug);

        $keyboard = Keyboard::make()
            ->inline()
            ->row([
                Keyboard::inlineButton(['text' => '« Back to Nominee', 'callback_data' => "nominee_{$nomineeSlug}"])
            ]);

        $this->sendMessage($chatId, $message, $keyboard);
    }

    /**
     * Test endpoint to debug webhook processing
     */
    public function testWebhook(Request $request)
    {
        try {
            Log::info('Test webhook called', [
                'method' => $request->getMethod(),
                'body' => $request->getContent(),
                'headers' => $request->headers->all()
            ]);

            // Try to get bot info to test API connection
            $botInfo = Telegram::getMe();
            
            Log::info('Bot API connection successful', [
                'bot_username' => $botInfo->getUsername(),
                'bot_id' => $botInfo->getId()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Test webhook is working',
                'bot_info' => [
                    'username' => $botInfo->getUsername(),
                    'id' => $botInfo->getId()
                ],
                'timestamp' => now()->toDateTimeString()
            ]);

        } catch (\Exception $e) {
            Log::error('Test webhook error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }
}
