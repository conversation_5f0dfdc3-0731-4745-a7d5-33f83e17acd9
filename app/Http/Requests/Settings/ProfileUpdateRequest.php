<?php

namespace App\Http\Requests\Settings;

use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
        ];

        // Add nominee profile validation rules if user is a nominee
        if ($this->user()->isNominee()) {
            $rules = array_merge($rules, [
                'profile_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:5120'], // 5MB max
                'display_name' => ['nullable', 'string', 'max:255'],
                'bio' => ['nullable', 'string', 'max:2000'],
                'achievements' => ['nullable', 'string', 'max:2000'],
                'why_vote_for_me' => ['nullable', 'string', 'max:2000'],
                'social_links' => ['nullable', 'array'],
                'social_links.facebook' => ['nullable', 'string', 'max:255'],
                'social_links.twitter' => ['nullable', 'string', 'max:255'],
                'social_links.instagram' => ['nullable', 'string', 'max:255'],
                'social_links.linkedin' => ['nullable', 'string', 'max:255'],
                'social_links.youtube' => ['nullable', 'string', 'max:255'],
                'social_links.tiktok' => ['nullable', 'string', 'max:255'],
            ]);
        }

        return $rules;
    }

    /**
     * Get the custom validation messages.
     */
    public function messages(): array
    {
        return [
            'profile_image.image' => 'The profile image must be a valid image file.',
            'profile_image.mimes' => 'The profile image must be a file of type: jpeg, png, jpg, gif.',
            'profile_image.max' => 'The profile image may not be greater than 5MB.',
            'display_name.max' => 'The display name may not be greater than 255 characters.',
            'bio.max' => 'The bio may not be greater than 2000 characters.',
            'achievements.max' => 'The achievements section may not be greater than 2000 characters.',
            'why_vote_for_me.max' => 'The "why vote for me" section may not be greater than 2000 characters.',
        ];
    }
}
