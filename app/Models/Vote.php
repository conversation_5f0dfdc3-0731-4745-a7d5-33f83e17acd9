<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Vote extends Model
{
    use HasFactory;

    protected $fillable = [
        'nominee_profile_id',
        'payment_id',
        'voter_name',
        'voter_email',
        'voter_phone',
        'vote_count',
        'amount_paid',
        'discount_applied',
        'vote_method',
        'session_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'discount_applied' => 'decimal:2',
    ];

    /**
     * Get the nominee profile that received these votes.
     */
    public function nomineeProfile(): BelongsTo
    {
        return $this->belongsTo(NomineeProfile::class);
    }

    /**
     * Get the payment associated with these votes.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the award categories through the nominee profile.
     * Since nominees can be in multiple categories, this returns all categories.
     */
    public function awardCategories()
    {
        return $this->hasManyThrough(
            AwardCategory::class,
            'award_category_nominee_profile',
            'nominee_profile_id', // Foreign key on pivot table
            'id', // Foreign key on award_categories table
            'nominee_profile_id', // Local key on votes table
            'award_category_id' // Local key on pivot table
        );
    }

    /**
     * Get the first award category through the nominee profile.
     * This uses a simple approach that works with eager loading.
     */
    public function awardCategory()
    {
        return $this->nomineeProfile()->first()?->awardCategories()->first();
    }

    /**
     * Get the first award category through the nominee profile.
     * This is for backward compatibility where a single category is expected.
     * This is an accessor that doesn't require database queries if nomineeProfile is already loaded.
     */
    public function getAwardCategoryAttribute()
    {
        // If nomineeProfile is already loaded, use it
        if ($this->relationLoaded('nomineeProfile') && $this->nomineeProfile) {
            return $this->nomineeProfile->awardCategories->first() ?? null;
        }
        
        // Otherwise load it
        return $this->nomineeProfile?->awardCategories()->first() ?? null;
    }
}
