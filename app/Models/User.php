<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * User roles
     */
    const ROLE_SUPER_ADMIN = 'super_admin';
    const ROLE_ADMIN = 'admin';
    const ROLE_NOMINEE = 'nominee';
    const ROLE_VIEWER = 'viewer';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'organization_id',
        'phone',
        'phone_number',
        'bio',
        'profile_image',
        'slug',
        'is_suspended',
        'suspension_reason',
        'suspension_notice',
        'approval_status',
        'approved_at',
        'approved_by',
        'approval_notes',
        'rejection_reason',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically create nominee profile when a nominee user is created
        static::created(function ($user) {
            if ($user->role === self::ROLE_NOMINEE) {
                $user->createNomineeProfile();
            }
        });

        // Handle role changes
        static::updated(function ($user) {
            if ($user->isDirty('role')) {
                $originalRole = $user->getOriginal('role');
                $newRole = $user->role;

                // If user becomes a nominee, create profile
                if ($newRole === self::ROLE_NOMINEE && $originalRole !== self::ROLE_NOMINEE) {
                    $user->createNomineeProfile();
                }
                
                // If user is no longer a nominee, we might want to handle this
                // For now, we'll keep the profile but could add logic here if needed
            }
        });
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_suspended' => 'boolean',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Get the organization that the user belongs to.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the nominee profiles for this user.
     */
    public function nomineeProfiles(): HasMany
    {
        return $this->hasMany(NomineeProfile::class);
    }



    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole(self::ROLE_SUPER_ADMIN);
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Check if user is a nominee.
     */
    public function isNominee(): bool
    {
        return $this->hasRole(self::ROLE_NOMINEE);
    }

    /**
     * Check if user is a viewer (read-only access).
     */
    public function isViewer(): bool
    {
        return $this->hasRole(self::ROLE_VIEWER);
    }

    /**
     * Check if user can manage a specific organization.
     */
    public function canManageOrganization(Organization $organization): bool
    {
        return $this->isSuperAdmin() || 
               ($this->isAdmin() && $this->organization_id === $organization->id);
    }

    /**
     * Check if user is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Check if user is approved.
     */
    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    /**
     * Check if user is rejected.
     */
    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    /**
     * Approve the user.
     */
    public function approve(User $approver, string $notes = null): void
    {
        $this->update([
            'approval_status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approver->id,
            'approval_notes' => $notes,
            'rejection_reason' => null,
        ]);

        // If the user is a nominee and has a nominee profile, approve the profile as well.
        if ($this->isNominee() && $this->nomineeProfile()->exists()) {
            $this->nomineeProfile()->update(['is_approved' => true]);
        }
    }

    /**
     * Reject the user.
     */
    public function reject(User $approver, string $reason): void
    {
        $this->update([
            'approval_status' => 'rejected',
            'approved_at' => null,
            'approved_by' => $approver->id,
            'approval_notes' => null,
            'rejection_reason' => $reason,
        ]);
    }

    /**
     * Get the user who approved this user.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Create a nominee profile for this user.
     */
    public function createNomineeProfile(): NomineeProfile
    {
        // Check if profile already exists
        if ($this->nomineeProfiles()->exists()) {
            return $this->nomineeProfiles()->first();
        }

        return $this->nomineeProfiles()->create([
            'display_name' => $this->name,
            'bio' => $this->bio ?? '',
            'is_approved' => true, // Auto-approve for now
        ]);
    }

    /**
     * Get the nominee profile for this user (if they are a nominee).
     */
    public function nomineeProfile()
    {
        return $this->hasOne(NomineeProfile::class);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
