<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Organization extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'website',
        'contact_email',
        'contact_phone',
        'is_active',
        'revenue_percentage',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'revenue_percentage' => 'decimal:2',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($organization) {
            $baseSlug = Str::slug($organization->name);
            $randomPart = strtolower(Str::random(6)) . '-' . strtolower(Str::random(6));
            $organization->slug = $baseSlug . '-' . $randomPart;
        });
    }

    /**
     * Get the users that belong to this organization.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the award categories for this organization.
     */
    public function awardCategories(): HasMany
    {
        return $this->hasMany(AwardCategory::class);
    }

    /**
     * Get the admins for this organization.
     */
    public function admins(): HasMany
    {
        return $this->hasMany(User::class)->where('role', User::ROLE_ADMIN);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
