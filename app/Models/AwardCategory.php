<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AwardCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'organization_id',
        'voting_start_date',
        'voting_end_date',
        'price_per_vote',
        'discount_percentage',
        'discount_min_votes',
        'is_active',
        'is_suspended',
        'suspension_reason',
        'suspension_notice',
    ];

    protected $casts = [
        'voting_start_date' => 'datetime',
        'voting_end_date' => 'datetime',
        'price_per_vote' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'is_suspended' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();
        // Generate a random slug for the category
        static::creating(function ($category) {
            $baseSlug = Str::slug($category->name);
            $randomPart = strtolower(Str::random(6)) . '-' . strtolower(Str::random(6));
            $category->slug = $baseSlug . '-' . $randomPart;
        });
    }

    /**
     * Get the organization that owns this award category.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the nominee profiles for this category.
     */
    public function nomineeProfiles(): BelongsToMany
    {
        return $this->belongsToMany(NomineeProfile::class, 'award_category_nominee_profile')
            ->withTimestamps()
            ->withPivot(['assigned_at', 'assigned_by']);
    }

    /**
     * Get all votes for this category through approved nominee profiles.
     */
    public function votes()
    {
        $approvedNomineeProfileIds = $this->nomineeProfiles()
            ->approved()
            ->pluck('nominee_profiles.id');
            
        return Vote::whereIn('nominee_profile_id', $approvedNomineeProfileIds);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Check if voting is currently active for this category.
     */
    public function isVotingActive(): bool
    {
        if (!$this->is_active || $this->is_suspended) {
            return false;
        }

        $now = Carbon::now();
        
        if ($this->voting_start_date && $now->lt($this->voting_start_date)) {
            return false;
        }

        if ($this->voting_end_date && $now->gt($this->voting_end_date)) {
            return false;
        }

        return true;
    }

    /**
     * Get the voting status text.
     */
    public function getVotingStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        if ($this->is_suspended) {
            return 'Suspended';
        }

        $now = Carbon::now();

        if ($this->voting_start_date && $now->lt($this->voting_start_date)) {
            return 'Not Started';
        }

        if ($this->voting_end_date && $now->gt($this->voting_end_date)) {
            return 'Ended';
        }

        return 'Active';
    }

    /**
     * Calculate the discounted price for a given number of votes.
     * Includes 3% surcharge to cover Paystack transaction fees.
     */
    public function calculatePrice(int $voteCount): array
    {
        $basePrice = $this->price_per_vote * $voteCount;
        $discount = 0;
        $discountPercentage = 0;

        if ($this->discount_percentage > 0 && $voteCount >= $this->discount_min_votes) {
            $discountPercentage = $this->discount_percentage;
            $discount = ($basePrice * $this->discount_percentage) / 100;
        }

        $priceAfterDiscount = $basePrice - $discount;
        
        // Add 3% surcharge to cover Paystack transaction fees (2% + 1% buffer)
        // This ensures we receive the full intended amount after Paystack fees
        $paystackSurcharge = $priceAfterDiscount * 0.03;
        $finalPrice = $priceAfterDiscount + $paystackSurcharge;

        return [
            'base_price' => $basePrice,
            'discount' => $discount,
            'paystack_surcharge' => $paystackSurcharge,
            'final_price' => round($finalPrice, 2), // Round to 2 decimal places
            'discount_percentage' => $discountPercentage,
        ];
    }

    /**
     * Get the total votes count for this category.
     */
    public function getTotalVotesAttribute(): int 
    {
        return $this->votes()->count();
    }

    /**
     * Get the total revenue for this category.
     */
    public function getTotalRevenueAttribute(): float
    {
        return $this->votes()
            ->whereHas('payment', function ($query) {
                $query->where('status', 'success');
            })
            ->with('payment')
            ->get()
            ->sum(function ($vote) {
                return $vote->payment ? $vote->payment->amount : 0;
            });
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_suspended', false);
    }

    /**
     * Scope a query to only include categories with active voting.
     */
    public function scopeVotingActive($query)
    {
        $now = Carbon::now();
        
        return $query->active()
            ->where(function ($q) use ($now) {
                $q->whereNull('voting_start_date')
                  ->orWhere('voting_start_date', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('voting_end_date')
                  ->orWhere('voting_end_date', '>=', $now);
            });
    }

    /**
     * Set the discount percentage attribute.
     */
    public function setDiscountPercentageAttribute($value)
    {
        $this->attributes['discount_percentage'] = empty($value) ? 0 : $value;
    }

    /**
     * Set the discount min votes attribute.
     */
    public function setDiscountMinVotesAttribute($value)
    {
        $this->attributes['discount_min_votes'] = empty($value) ? 0 : $value;
    }
}
