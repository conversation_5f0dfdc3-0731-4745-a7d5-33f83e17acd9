<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class UssdSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'msisdn',
        'user_id',
        'network',
        'menu_state',
        'user_data',
        'current_screen',
        'last_input',
        'last_activity',
        'is_active',
    ];

    protected $casts = [
        'menu_state' => 'array',
        'user_data' => 'array',
        'last_activity' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get sessions that are not expired
     */
    public function scopeNotExpired($query)
    {
        return $query->where('last_activity', '>', Carbon::now()->subMinutes(5));
    }

    /**
     * Check if session is expired
     */
    public function isExpired(): bool
    {
        return $this->last_activity < Carbon::now()->subMinutes(5);
    }

    /**
     * Update session activity
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity' => Carbon::now()]);
    }

    /**
     * Store data in user_data field
     */
    public function storeData(string $key, $value): void
    {
        $userData = $this->user_data ?? [];
        $userData[$key] = $value;
        $this->update(['user_data' => $userData]);
    }

    /**
     * Get data from user_data field
     */
    public function getData(string $key, $default = null)
    {
        return $this->user_data[$key] ?? $default;
    }
}
