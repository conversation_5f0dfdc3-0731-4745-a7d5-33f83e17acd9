<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference',
        'paystack_reference',
        'amount',
        'currency',
        'status',
        'payment_method',
        'gateway_response',
        'metadata',
        'paid_at',
        'customer_email',
        'customer_name',
        'customer_phone',
        'email',
        'phone',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Payment statuses
     */
    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the votes associated with this payment.
     */
    public function votes(): HasMany
    {
        return $this->hasMany(Vote::class);
    }



    /**
     * Check if payment is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if payment failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Mark payment as successful.
     */
    public function markAsSuccessful(array $data = []): void
    {
        $this->update([
            'status' => self::STATUS_SUCCESS,
            'paid_at' => now(),
            'paystack_reference' => $data['paystack_reference'] ?? null,
            'payment_method' => $data['payment_method'] ?? null,
            'gateway_response' => $data['gateway_response'] ?? null,
            'metadata' => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(?string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'gateway_response' => $reason,
        ]);
    }

    /**
     * Scope a query to only include successful payments.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }
}
