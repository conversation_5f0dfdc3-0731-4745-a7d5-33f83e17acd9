<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class NomineeProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'slug',
        'nominee_code',
        'display_name',
        'bio',
        'profile_image',
        'additional_images',
        'social_links',
        'achievements',
        'why_vote_for_me',
        'total_votes',
        'link_clicks',
        'is_approved',
        'is_suspended',
        'suspension_reason',
        'suspension_notice',
        'admin_notes',
    ];

    protected $casts = [
        'additional_images' => 'array',
        'social_links' => 'array',
        'achievements' => 'array',
        'is_approved' => 'boolean',
        'is_suspended' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($profile) {
            if (empty($profile->slug)) {
                $profile->slug = static::generateUniqueSlug();
            }
            if (empty($profile->nominee_code)) {
                $profile->nominee_code = static::generateUniqueNomineeCode($profile->display_name);
            }
        });
    }

    /**
     * Generate a unique 5-character alphanumeric slug
     */
    public static function generateUniqueSlug(): string
    {
        do {
            $slug = static::generateAlphanumericSlug();
        } while (static::where('slug', $slug)->exists());

        return $slug;
    }

    /**
     * Generate a 5-character alphanumeric string (mix of letters and numbers)
     */
    private static function generateAlphanumericSlug(): string
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $slug = '';
        
        for ($i = 0; $i < 5; $i++) {
            $slug .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $slug;
    }

    /**
     * Generate a unique nominee code based on display name
     */
    public static function generateUniqueNomineeCode(string $displayName): string
    {
        $baseCode = static::generateBaseNomineeCode($displayName);
        
        do {
            $randomDigits = static::generateRandomThreeDigits();
            $code = $baseCode . $randomDigits;
        } while (static::where('nominee_code', $code)->exists());

        return $code;
    }

    /**
     * Generate a random 3-digit number as a string
     */
    private static function generateRandomThreeDigits(): string
    {
        return str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT);
    }

    /**
     * Generate base 3-letter code from display name
     */
    private static function generateBaseNomineeCode(string $displayName): string
    {
        // Remove special characters and get first 3 letters
        $cleanName = preg_replace('/[^A-Za-z]/', '', $displayName);
        $baseCode = strtoupper(substr($cleanName, 0, 3));
        
        // If name is too short, pad with 'X'
        while (strlen($baseCode) < 3) {
            $baseCode .= 'X';
        }
        
        return $baseCode;
    }

    /**
     * Find nominee by code (searches both nominee_code and slug fields)
     */
    public static function findByCode(string $code): ?NomineeProfile
    {
        $code = strtoupper($code);
        
        // Search in both nominee_code and slug fields to support both formats
        return static::where('nominee_code', $code)
                    ->orWhere('slug', $code)
                    ->first();
    }

    /**
     * Get the user that owns this profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the votes for this nominee profile.
     */
    public function votes(): HasMany
    {
        return $this->hasMany(Vote::class);
    }

    /**
     * The award categories that this nominee profile belongs to.
     */
    public function awardCategories()
    {
        return $this->belongsToMany(AwardCategory::class, 'award_category_nominee_profile')
                    ->withTimestamps()
                    ->withPivot(['assigned_at', 'assigned_by']);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the public voting URL for this nominee.
     */
    public function getVotingUrlAttribute(): string
    {
        return route('vote.show', $this->slug);
    }

    /**
     * Check if this profile can receive votes.
     */
    public function canReceiveVotes(): bool
    {
        return $this->is_approved && 
               !$this->is_suspended && 
               $this->awardCategories()->where('is_active', true)->where('is_suspended', false)->exists();
    }

    /**
     * Increment the link clicks counter.
     */
    public function incrementLinkClicks(): void
    {
        $this->increment('link_clicks');
    }

    /**
     * Scope a query to only include approved profiles.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true)->where('is_suspended', false);
    }

    /**
     * Scope a query to order by total votes descending.
     */
    public function scopeByVotes($query)
    {
        return $query->orderBy('total_votes', 'desc');
    }

    /**
     * Regenerate the slug for this nominee (useful for admin actions)
     */
    public function regenerateSlug(): string
    {
        $newSlug = static::generateUniqueSlug();
        $this->update(['slug' => $newSlug]);
        return $newSlug;
    }

    /**
     * Get a human-readable format for display
     */
    public function getDisplaySlugAttribute(): string
    {
        return strtoupper($this->slug);
    }
}
