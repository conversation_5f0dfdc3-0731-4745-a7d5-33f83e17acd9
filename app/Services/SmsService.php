<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    private string $apiKey;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.mnotify.api_key');
        $this->baseUrl = 'https://api.mnotify.com/api/sms/quick';

        if (empty($this->apiKey)) {
            throw new \Exception('MNotify API key is not configured. Please set MNOTIFY_API_KEY in your .env file.');
        }
    }

    /**
     * Send SMS notification
     *
     * @param string $recipient Phone number in international format (e.g., 233201234567)
     * @param string $message Message content (max 120 characters, no emojis)
     * @return bool Success status
     */
    public function sendSms(string $recipient, string $message): bool
    {
        try {
            // Validate message length
            if (strlen($message) > 120) {
                Log::warning('SMS message too long', ['length' => strlen($message), 'message' => $message]);
                $message = substr($message, 0, 117) . '...';
            }

            // Remove emojis and special characters
            $message = $this->sanitizeMessage($message);

            // Format phone number (ensure it starts with country code)
            $recipient = $this->formatPhoneNumber($recipient);

            // Build URL with API key as parameter (MNotify format)
            $url = $this->baseUrl . '?key=' . $this->apiKey;

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post($url, [
                'recipient' => [$recipient],
                'sender' => 'VoteYourFav',
                'message' => $message,
                'is_schedule' => false,
                'schedule_date' => '',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('SMS sent successfully', [
                    'recipient' => $recipient,
                    'message' => $message,
                    'response' => $data
                ]);
                return true;
            } else {
                Log::error('SMS sending failed', [
                    'recipient' => $recipient,
                    'message' => $message,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS service error', [
                'recipient' => $recipient,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send organization signup notification to super admins
     */
    public function notifyOrganizationSignup(string $organizationName, string $adminName): void
    {
        $superAdmins = \App\Models\User::where('role', \App\Models\User::ROLE_SUPER_ADMIN)
            ->where(function($query) {
                $query->whereNotNull('phone_number')
                      ->orWhereNotNull('phone');
            })
            ->get();

        $message = "New org signup: {$organizationName} by {$adminName}. Login to approve.";

        foreach ($superAdmins as $admin) {
            $phoneNumber = $admin->phone_number ?? $admin->phone;
            if ($phoneNumber) {
                $this->sendSms($phoneNumber, $message);
            }
        }
    }

    /**
     * Send approval notification to organization
     */
    public function notifyOrganizationApproval(string $organizationName, string $phoneNumber, float $revenuePercentage): bool
    {
        $message = "Your org {$organizationName} is approved! Revenue rate: {$revenuePercentage}%. Start creating categories now.";
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Send rejection notification to organization
     */
    public function notifyOrganizationRejection(string $organizationName, string $phoneNumber, string $reason): bool
    {
        $shortReason = strlen($reason) > 50 ? substr($reason, 0, 47) . '...' : $reason;
        $message = "Your org {$organizationName} was rejected. Reason: {$shortReason}";
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Remove emojis and sanitize message
     */
    private function sanitizeMessage(string $message): string
    {
        // Remove emojis and special unicode characters
        $message = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $message); // Emoticons
        $message = preg_replace('/[\x{1F300}-\x{1F5FF}]/u', '', $message); // Misc Symbols
        $message = preg_replace('/[\x{1F680}-\x{1F6FF}]/u', '', $message); // Transport
        $message = preg_replace('/[\x{2600}-\x{26FF}]/u', '', $message);   // Misc symbols
        $message = preg_replace('/[\x{2700}-\x{27BF}]/u', '', $message);   // Dingbats
        
        // Clean up extra spaces
        return trim(preg_replace('/\s+/', ' ', $message));
    }

    /**
     * Format phone number to international format
     */
    public function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // If starts with 0, replace with 233 (Ghana country code)
        if (str_starts_with($phone, '0')) {
            $phone = '233' . substr($phone, 1);
        }
        
        // If doesn't start with country code, add Ghana code
        if (!str_starts_with($phone, '233')) {
            $phone = '233' . $phone;
        }
        
        return $phone;
    }
} 