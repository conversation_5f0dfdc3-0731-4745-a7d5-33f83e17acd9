<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Vote;
use App\Models\NomineeProfile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PaystackService
{
    protected ?string $secretKey;
    protected ?string $publicKey;
    protected ?string $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret_key');
        $this->publicKey = config('services.paystack.public_key');
        $this->baseUrl = config('services.paystack.payment_url');

        if (is_null($this->secretKey) && !app()->runningInConsole()) {
            throw new Exception('Paystack secret key is not configured.');
        }
    }

    /**
     * Initialize a payment transaction
     */
    public function initializePayment(array $data): array
    {
        try {
            if (is_null($this->secretKey)) {
                throw new Exception('Paystack service is not configured.');
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transaction/initialize', [
                'email' => $data['email'],
                'amount' => $this->formatAmount($data['amount']), // Convert to kobo/pesewas
                'currency' => $data['currency'] ?? 'GHS',
                'reference' => $data['reference'],
                'callback_url' => $data['callback_url'] ?? null,
                'metadata' => $data['metadata'] ?? [],
            ]);

            if ($response->successful()) {
                return [
                    'status' => true,
                    'data' => $response->json()['data'],
                    'message' => 'Payment initialized successfully'
                ];
            }

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'Payment initialization failed',
                'data' => null
            ];

        } catch (Exception $e) {
            Log::error('Paystack initialization error: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => 'Payment service unavailable',
                'data' => null
            ];
        }
    }    /**
     * Direct mobile money charge for USSD payments
     */
    public function chargeMobileMoney(array $data): array
    {
        try {
            // If mobile_money array is provided, use it directly
            if (isset($data['mobile_money'])) {
                $mobileMoneyData = $data['mobile_money'];
            } else {
                // Legacy support - determine provider based on phone number prefix
                $provider = $this->getMobileMoneyProvider($data['phone']);
                $mobileMoneyData = [
                    'phone' => $this->formatPhoneForProvider($data['phone']),
                    'provider' => $provider
                ];
            }
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charge', [
                'email' => $data['email'],
                'amount' => $this->formatAmount($data['amount']), // Convert to kobo/pesewas
                'currency' => $data['currency'] ?? 'GHS',
                'reference' => $data['reference'],
                'mobile_money' => $mobileMoneyData,
                'metadata' => $data['metadata'] ?? [],
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                
                return [
                    'status' => true,
                    'data' => $responseData['data'],
                    'message' => $responseData['message'] ?? 'Mobile money charge initiated'
                ];
            }

            $errorData = $response->json();
            Log::error('Paystack mobile money charge failed', [
                'response' => $errorData,
                'data' => $data
            ]);

            return [
                'status' => false,
                'message' => $errorData['message'] ?? 'Mobile money charge failed',
                'data' => $errorData['data'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Paystack mobile money charge error: ' . $e->getMessage(), [
                'data' => $data
            ]);
            
            return [
                'status' => false,
                'message' => 'Mobile money service unavailable',
                'data' => null
            ];
        }
    }

    /**
     * Charge API - handles both mobile money and USSD payments
     */
    public function charge(array $data): array
    {
        try {
            $chargeData = [
                'email' => $data['email'],
                'amount' => $this->formatAmount($data['amount']),
                'currency' => $data['currency'] ?? 'GHS',
                'reference' => $data['reference'],
                'metadata' => $data['metadata'] ?? [],
            ];

            // Add mobile money or USSD specific data
            if (isset($data['mobile_money'])) {
                $chargeData['mobile_money'] = $data['mobile_money'];
            }
            
            if (isset($data['ussd'])) {
                $chargeData['ussd'] = $data['ussd'];
            }

            // Add callback URL if provided
            if (isset($data['callback_url'])) {
                $chargeData['callback_url'] = $data['callback_url'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charge', $chargeData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                return [
                    'status' => true,
                    'data' => $responseData['data'],
                    'message' => $responseData['message'] ?? 'Charge initiated successfully'
                ];
            }

            $errorData = $response->json();
            Log::error('Paystack charge failed', [
                'response' => $errorData,
                'data' => $chargeData
            ]);

            return [
                'status' => false,
                'message' => $errorData['message'] ?? 'Charge failed',
                'data' => $errorData['data'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Paystack charge error: ' . $e->getMessage(), [
                'data' => $data
            ]);
            
            return [
                'status' => false,
                'message' => 'Payment service unavailable',
                'data' => null
            ];
        }
    }

    /**
     * Charge USSD specifically for bank USSD payments
     */
    public function chargeUssd(array $data): array
    {
        try {
            $chargeData = [
                'email' => $data['email'],
                'amount' => $this->formatAmount($data['amount']),
                'currency' => $data['currency'] ?? 'GHS',
                'reference' => $data['reference'],
                'metadata' => $data['metadata'] ?? [],
            ];

            // Add USSD specific data
            if (isset($data['ussd'])) {
                $chargeData['ussd'] = $data['ussd'];
            }

            // Add callback URL if provided
            if (isset($data['callback_url'])) {
                $chargeData['callback_url'] = $data['callback_url'];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charge', $chargeData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                return [
                    'status' => true,
                    'data' => $responseData['data'],
                    'message' => $responseData['message'] ?? 'USSD charge initiated successfully'
                ];
            }

            $errorData = $response->json();
            Log::error('Paystack USSD charge failed', [
                'response' => $errorData,
                'data' => $chargeData
            ]);

            return [
                'status' => false,
                'message' => $errorData['message'] ?? 'USSD charge failed',
                'data' => $errorData['data'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Paystack USSD charge error: ' . $e->getMessage(), [
                'data' => $data
            ]);
            
            return [
                'status' => false,
                'message' => 'USSD service unavailable',
                'data' => null
            ];
        }
    }

    /**
     * Submit OTP to complete mobile money payment
     */
    public function submitOtp(string $reference, string $otp): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charge/submit_otp', [
                'otp' => $otp,
                'reference' => $reference,
            ]);

            if ($response->successful()) {
                $data = $response->json()['data'];
                
                return [
                    'status' => true,
                    'data' => $data,
                    'message' => 'OTP submitted successfully'
                ];
            }

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'OTP submission failed',
                'data' => null
            ];

        } catch (Exception $e) {
            Log::error('Paystack OTP submission error: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => 'OTP submission failed',
                'data' => null
            ];
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verifyPayment(string $reference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/transaction/verify/' . $reference);

            if ($response->successful()) {
                $data = $response->json()['data'];
                
                return [
                    'status' => true,
                    'data' => $data,
                    'message' => 'Payment verified successfully'
                ];
            }

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'Payment verification failed',
                'data' => null
            ];

        } catch (Exception $e) {
            Log::error('Paystack verification error: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => 'Payment verification failed',
                'data' => null
            ];
        }
    }

    /**
     * Process a successful payment and create votes
     */
    public function processSuccessfulPayment(Payment $payment, array $paystackData): bool
    {
        try {
            // Update payment status
            $payment->markAsSuccessful([
                'paystack_reference' => $paystackData['reference'],
                'payment_method' => $paystackData['channel'] ?? null,
                'gateway_response' => $paystackData['gateway_response'] ?? null,
                'metadata' => array_merge($payment->metadata ?? [], [
                    'paystack_data' => $paystackData
                ])
            ]);

            // Get nominee profile from payment metadata
            $nomineeProfileId = $payment->metadata['nominee_profile_id'];
            $voteCount = $payment->metadata['vote_count'];
            $discountApplied = $payment->metadata['discount_applied'] ?? 0;

            $nomineeProfile = NomineeProfile::findOrFail($nomineeProfileId);

            // Create vote record
            Vote::create([
                'nominee_profile_id' => $nomineeProfile->id,
                'payment_id' => $payment->id,
                'voter_name' => $payment->customer_name,
                'voter_email' => $payment->customer_email,
                'voter_phone' => $payment->customer_phone,
                'vote_count' => $voteCount,
                'amount_paid' => $payment->amount,
                'discount_applied' => $discountApplied,
                'ip_address' => $payment->metadata['ip_address'] ?? null,
                'user_agent' => $payment->metadata['user_agent'] ?? null,
            ]);

            // Update nominee's total votes
            $nomineeProfile->increment('total_votes', $voteCount);

            Log::info("Payment processed successfully", [
                'payment_id' => $payment->id,
                'nominee_profile_id' => $nomineeProfile->id,
                'votes_added' => $voteCount
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Error processing successful payment: ' . $e->getMessage(), [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Handle webhook from Paystack
     */
    public function handleWebhook(array $payload): bool
    {
        try {
            $event = $payload['event'];
            $data = $payload['data'];

            if ($event === 'charge.success') {
                $reference = $data['reference'];
                
                // Find the payment record
                $payment = Payment::where('reference', $reference)->first();
                
                if (!$payment) {
                    Log::warning('Payment not found for webhook', ['reference' => $reference]);
                    return false;
                }

                if ($payment->isSuccessful()) {
                    Log::info('Payment already processed', ['reference' => $reference]);
                    return true;
                }

                // Process the successful payment
                return $this->processSuccessfulPayment($payment, $data);
            }

            return true;

        } catch (Exception $e) {
            Log::error('Webhook processing error: ' . $e->getMessage(), [
                'payload' => $payload
            ]);

            return false;
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $computedSignature = hash_hmac('sha512', $payload, $this->secretKey);
        return hash_equals($signature, $computedSignature);
    }

    /**
     * Get public key for frontend
     */
    public function getPublicKey(): string
    {
        return $this->publicKey;
    }

    /**
     * Format amount for Paystack (convert to kobo/pesewas)
     * Ensures the result is always an integer
     */
    public function formatAmount(float $amount): int
    {
        // Round to 2 decimal places first to handle floating point precision issues
        $rounded = round($amount, 2);
        // Convert to kobo/pesewas and ensure it's an integer
        return (int) round($rounded * 100);
    }

    /**
     * Format amount from Paystack (convert from kobo/pesewas)
     */
    public function parseAmount(int $amount): float
    {
        return $amount / 100;
    }

    /**
     * Check charge status
     */
    public function checkChargeStatus(string $reference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/charge/' . $reference);

            if ($response->successful()) {
                $data = $response->json()['data'];
                
                return [
                    'status' => true,
                    'data' => $data,
                    'message' => 'Charge status retrieved successfully'
                ];
            }

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'Failed to check charge status',
                'data' => null
            ];

        } catch (Exception $e) {
            Log::error('Paystack charge status check error: ' . $e->getMessage());
            
            return [
                'status' => false,
                'message' => 'Failed to check payment status',
                'data' => null
            ];
        }
    }    /**
     * Determine mobile money provider based on phone number
     */
    private function getMobileMoneyProvider(string $phone): string
    {
        // Remove country code and normalize
        $normalizedPhone = preg_replace('/^233/', '', $phone);
        $normalizedPhone = preg_replace('/^\+233/', '', $normalizedPhone);
        
        // Ghana mobile money prefixes (complete mapping - check 4-digit first, then 3-digit)
        $providers4digit = [
            // MTN 4-digit prefixes
            '0256' => 'mtn', '0257' => 'mtn',
            '0597' => 'mtn', '0598' => 'mtn', '0599' => 'mtn',
        ];
        
        $providers3digit = [
            // MTN 3-digit prefixes
            '024' => 'mtn', '025' => 'mtn', '053' => 'mtn', 
            '054' => 'mtn', '055' => 'mtn', '059' => 'mtn',
            
            // Telecel (formerly Vodafone) prefixes  
            '020' => 'telecel', '050' => 'telecel',
            
            // AirtelTigo prefixes
            '026' => 'airteltigo', '027' => 'airteltigo',
            '056' => 'airteltigo', '057' => 'airteltigo'
        ];
        
        // Check 4-digit prefixes first
        $prefix4 = substr($normalizedPhone, 0, 4);
        if (isset($providers4digit[$prefix4])) {
            return $providers4digit[$prefix4];
        }
        
        // Fall back to 3-digit prefixes
        $prefix3 = substr($normalizedPhone, 0, 3);
        return $providers3digit[$prefix3] ?? null; // Return null if provider not found
    }

    /**
     * Format phone number for Ghana mobile money providers
     */
    private function formatPhoneForProvider(string $msisdn): string
    {
        // Ensure proper format for Ghana (0XXXXXXXXX)
        $phone = preg_replace('/^233/', '0', $msisdn);
        $phone = preg_replace('/^\+233/', '0', $phone);
        
        if (!str_starts_with($phone, '0')) {
            $phone = '0' . $phone;
        }
        
        return $phone;
    }
}