<?php

namespace App\Services;

use App\Models\UssdSession;
use App\Models\NomineeProfile;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\Vote;
use App\Models\Payment;
use App\Services\PaystackUssdService;
use Illuminate\Support\Facades\Log;

class UssdService
{
    protected $paystackService;

    public function __construct(PaystackUssdService $paystackService)
    {
        $this->paystackService = $paystackService;
    }
    /**
     * Process USSD request and return response
     */
    public function processRequest(UssdSession $session, string $userInput, bool $isFirstRequest): array
    {
        // Clean user input
        $userInput = trim($userInput);

        // If this is the first request, show main menu
        if ($isFirstRequest) {
            return $this->showMainMenu($session);
        }

        // Handle session timeout
        if ($session->isExpired()) {
            $session->update(['is_active' => false]);
            return $this->timeoutResponse();
        }        // Process based on current screen
        switch ($session->current_screen) {
            case 'main_menu':
                return $this->handleMainMenuInput($session, $userInput);
            
            case 'enter_nominee_code':
                return $this->handleNomineeCodeInput($session, $userInput);
            
            case 'enter_vote_quantity':
                return $this->handleVoteQuantityInput($session, $userInput);
            
            case 'confirm_vote':
                return $this->handleVoteConfirmation($session, $userInput);
            
            case 'select_payment_method':
                return $this->handlePaymentMethodSelection($session, $userInput);
            
            case 'enter_payment_pin':
                return $this->handlePaymentPin($session, $userInput);
            
            case 'payment_processing':
                return $this->handlePaymentProcessing($session, $userInput);
            
            case 'vote_success':
                return $this->handleVoteSuccess($session, $userInput);
            
            case 'browse_organizations':
                return $this->handleBrowseOrganizations($session, $userInput);
            
            case 'browse_categories':
                return $this->handleBrowseCategories($session, $userInput);
            
            case 'browse_nominees':
                return $this->handleBrowseNominees($session, $userInput);
            
            case 'vote_history':
                return $this->handleVoteHistory($session, $userInput);
            
            case 'account_balance':
                return $this->handleAccountBalance($session, $userInput);
            
            case 'help_menu':
                return $this->handleHelpMenu($session, $userInput);
            
            default:
                return $this->showMainMenu($session);
        }
    }

    /**
     * Show main menu
     */
    private function showMainMenu(UssdSession $session): array
    {
        $session->update([
            'current_screen' => 'main_menu',
            'user_data' => null
        ]);

        $message = "Welcome to voteyourfav!\nCast your vote easily.\n\n";
        $message .= "1. Vote for Nominee\n";
        $message .= "2. Browse Nominees\n";
        $message .= "3. My Vote History\n";
        $message .= "4. Account Balance\n";
        $message .= "5. Help & Support\n";
        $message .= "0. Exit\n\n";
        $message .= "Enter your choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle main menu input
     */
    private function handleMainMenuInput(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->showEnterNomineeCode($session);
            
            case '2':
                return $this->showBrowseOptions($session);
            
            case '3':
                return $this->showVoteHistory($session);
            
            case '4':
                return $this->showAccountBalance($session);
            
            case '5':
                return $this->showHelpMenu($session);
            
            case '0':
                return $this->exitSession($session);
            
            default:
                return $this->invalidInput($session, "Please enter 0-5:");
        }
    }

    /**
     * Show enter nominee code screen
     */
    private function showEnterNomineeCode(UssdSession $session): array
    {
        $session->update(['current_screen' => 'enter_nominee_code']);

        $message = "VOTE FOR NOMINEE\n\n";
        $message .= "Enter nominee code:\n";
        $message .= "(e.g., SAR001, STO002, SHA003)\n\n";
        $message .= "Example: SAR001\n\n";
        $message .= "_____\n\n";
        $message .= "9. Browse all nominees\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle nominee code input
     */
    private function handleNomineeCodeInput(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '9':
                return $this->showBrowseOptions($session);
            
            case '0':
                return $this->showMainMenu($session);
            
            default:
                // Validate and find nominee by code
                if (strlen($input) !== 6) {
                    $message = "INVALID CODE FORMAT ❌\n\n";
                    $message .= "Codes must be 6 characters.\n";
                    $message .= "Example: SAR001\n\n";
                    $message .= "You entered: " . $input . "\n\n";
                    $message .= "Enter nominee code:\n";
                    $message .= "_____\n\n";
                    $message .= "1. Code format help\n";
                    $message .= "9. Browse nominees\n";
                    $message .= "0. Main Menu";

                    return [
                        'message' => $message,
                        'msgtype' => false
                    ];
                }

                $nominee = NomineeProfile::findByCode($input);
                
                if (!$nominee) {
                    $message = "NOMINEE NOT FOUND ❌\n\n";
                    $message .= "Code \"" . strtoupper($input) . "\" not found.\n";
                    $message .= "Please check and try again.\n\n";
                    $message .= "Enter nominee code:\n";
                    $message .= "_____\n\n";
                    $message .= "1. Get help with codes\n";
                    $message .= "9. Browse all nominees\n";
                    $message .= "0. Main Menu";

                    return [
                        'message' => $message,
                        'msgtype' => false
                    ];
                }

                // Store nominee in session and show vote quantity screen
                $session->storeData('nominee_id', $nominee->id);
                $session->storeData('nominee_code', $nominee->nominee_code);
                
                return $this->showVoteQuantity($session, $nominee);
        }
    }    /**
     * Show vote quantity input screen
     */
    private function showVoteQuantity(UssdSession $session, NomineeProfile $nominee): array
    {
        $session->update(['current_screen' => 'enter_vote_quantity']);

        // Get category and price information
        $category = $nominee->awardCategories()->with('organization')->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;

        $message = "NOMINEE FOUND ✅\n\n";
        $message .= "Code: " . $nominee->nominee_code . "\n";
        $message .= "Name: " . $nominee->display_name . "\n";
        
        if ($category) {
            $message .= "Category: " . $category->name . "\n";
            if ($category->organization) {
                $message .= "Organization: " . $category->organization->name . "\n";
            }
        }
        
        $message .= "Price: GHS " . number_format($pricePerVote, 2) . " per vote\n\n";
        $message .= "Enter number of votes (1-100):\n";
        $message .= "_____\n\n";
        $message .= "9. Try different code\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle vote quantity input
     */
    private function handleVoteQuantityInput(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '9':
                return $this->showEnterNomineeCode($session);
            
            case '0':
                return $this->showMainMenu($session);
            
            default:
                // Validate quantity
                if (!is_numeric($input) || $input < 1 || $input > 100) {
                    $nominee = NomineeProfile::find($session->getData('nominee_id'));
                    
                    $message = "INVALID QUANTITY ❌\n\n";
                    $message .= "Please enter 1-100 votes.\n";
                    $message .= "You entered: " . $input . "\n\n";
                    $message .= "Enter votes (1-100): ___\n\n";
                    $message .= "Current nominee: " . ($nominee ? $nominee->display_name : 'Unknown') . "\n";
                    $message .= "Price: GHS 2.00 per vote\n\n";
                    $message .= "9. Change nominee\n";
                    $message .= "0. Main Menu";

                    return [
                        'message' => $message,
                        'msgtype' => false
                    ];
                }

                $quantity = (int) $input;
                $session->storeData('vote_quantity', $quantity);
                
                return $this->showVoteConfirmation($session);
        }
    }

    /**
     * Show vote confirmation screen
     */
    private function showVoteConfirmation(UssdSession $session): array
    {
        $session->update(['current_screen' => 'confirm_vote']);

        $nominee = NomineeProfile::find($session->getData('nominee_id'));
        $quantity = $session->getData('vote_quantity');
          // Get category and calculate total
        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $total = $quantity * $pricePerVote;

        $message = "CONFIRM YOUR VOTE\n\n";
        $message .= "Nominee: " . $nominee->display_name . "\n";
        $message .= "Code: " . $nominee->nominee_code . "\n";
        
        if ($category) {
            $message .= "Category: " . $category->name . "\n";
        }
        
        $message .= "Votes: " . $quantity . "\n";
        $message .= "Total: GHS " . number_format($total, 2) . "\n\n";
        $message .= "1. Confirm & Pay\n";
        $message .= "2. Change quantity\n";
        $message .= "3. Change nominee\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }    /**
     * Handle vote confirmation
     */
    private function handleVoteConfirmation(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->showPaymentMethods($session);
            
            case '2':
                $nominee = NomineeProfile::find($session->getData('nominee_id'));
                return $this->showVoteQuantity($session, $nominee);
            
            case '3':
                return $this->showEnterNomineeCode($session);
            
            case '0':
                return $this->showMainMenu($session);
            
            default:
                return $this->invalidInput($session, "Please enter 1, 2, 3, or 0:");
        }
    }    /**
     * Show payment methods selection
     */
    private function showPaymentMethods(UssdSession $session): array
    {
        $session->update(['current_screen' => 'select_payment_method']);

        $nominee = NomineeProfile::find($session->getData('nominee_id'));
        $quantity = $session->getData('vote_quantity');
        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $total = $quantity * $pricePerVote;

        $message = "SELECT PAYMENT METHOD\n\n";
        $message .= "Total: GHS " . number_format($total, 2) . "\n";
        $message .= "For: " . $nominee->display_name . "\n\n";
        $message .= "Choose payment method:\n\n";
        $message .= "1. MTN Mobile Money\n";
        $message .= "2. Vodafone Cash\n";
        $message .= "3. AirtelTigo Money\n";
        $message .= "4. GTBank (*737#)\n";
        $message .= "5. Other Bank USSD\n\n";
        $message .= "8. Back to confirmation\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle payment method selection
     */
    private function handlePaymentMethodSelection(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                $session->storeData('payment_method', 'mtn_momo');
                return $this->initiateMobileMoneyPayment($session);
            
            case '2':
                $session->storeData('payment_method', 'vodafone_cash');
                return $this->initiateMobileMoneyPayment($session);
            
            case '3':
                $session->storeData('payment_method', 'airteltigo_money');
                return $this->initiateMobileMoneyPayment($session);
            
            case '4':
                $session->storeData('payment_method', 'gtbank_ussd');
                $session->storeData('ussd_type', '737');
                return $this->initiateUssdPayment($session);
            
            case '5':
                return $this->showOtherBankOptions($session);
            
            case '8':
                return $this->showVoteConfirmation($session);
            
            case '0':
                return $this->showMainMenu($session);
            
            default:
                return $this->invalidInput($session, "Please enter 1-5, 8, or 0:");
        }
    }

    /**
     * Show other bank USSD options
     */
    private function showOtherBankOptions(UssdSession $session): array
    {
        $message = "SELECT YOUR BANK\n\n";
        $message .= "1. Access Bank (*901#)\n";
        $message .= "2. First Bank (*894#)\n";
        $message .= "3. Zenith Bank (*966#)\n";
        $message .= "4. Sterling Bank (*822#)\n";
        $message .= "5. Fidelity Bank (*770#)\n\n";
        $message .= "8. Back to payment methods\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }    /**
     * Initiate mobile money payment
     */
    private function initiateMobileMoneyPayment(UssdSession $session): array
    {
        $nominee = NomineeProfile::find($session->getData('nominee_id'));
        $quantity = $session->getData('vote_quantity');
        $paymentMethod = $session->getData('payment_method');
        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;

        // Map payment methods to Paystack providers
        $providerMap = [
            'mtn_momo' => 'mtn',
            'vodafone_cash' => 'vod',
            'airteltigo_money' => 'atl'
        ];

        $provider = $providerMap[$paymentMethod] ?? 'mtn';

        try {
            // Initiate Paystack mobile money charge
            $chargeResponse = $this->paystackService->initiateMobileMoneyPayment(
                $session->msisdn,
                $quantity,
                $nominee->nominee_code,
                $session->session_id,
                $provider
            );

            if ($chargeResponse['status'] && isset($chargeResponse['data'])) {
                $session->storeData('payment_reference', $chargeResponse['data']['reference']);
                  $message = "MOBILE MONEY PAYMENT\n\n";
                $message .= "Payment request sent!\n\n";
                $message .= "Amount: GHS " . number_format($quantity * $pricePerVote, 2) . "\n";
                $message .= "Reference: " . substr($chargeResponse['data']['reference'], -6) . "\n\n";
                
                if (isset($chargeResponse['data']['display_text'])) {
                    $message .= $chargeResponse['data']['display_text'] . "\n\n";
                } else {
                    switch ($paymentMethod) {
                        case 'mtn_momo':
                            $message .= "Approve payment on your MTN phone.\n\n";
                            break;
                        case 'vodafone_cash':
                            $message .= "Approve payment on your Vodafone phone.\n\n";
                            break;
                        case 'airteltigo_money':
                            $message .= "Approve payment on your AirtelTigo phone.\n\n";
                            break;
                    }
                }
                
                $message .= "Votes will be recorded automatically after successful payment.\n\n";
                $message .= "Thank you for using voteyourfav!";

                // Update session to completed state for webhook processing
                $session->update([
                    'current_screen' => 'payment_initiated',
                    'is_active' => false // Mark as inactive since USSD session ends
                ]);

                return [
                    'message' => $message,
                    'msgtype' => true // End USSD session - mobile money prompt will take over
                ];
            } else {
                $message = "PAYMENT ERROR ❌\n\n";
                $message .= "Unable to process payment.\n";
                $message .= "Please try again.\n\n";
                $message .= "1. Try again\n";
                $message .= "2. Different payment method\n";
                $message .= "0. Main Menu";

                return [
                    'message' => $message,
                    'msgtype' => false
                ];
            }
        } catch (\Exception $e) {
            Log::error('Mobile Money Payment Error', [
                'session_id' => $session->session_id,
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            $message = "PAYMENT SERVICE ERROR ❌\n\n";
            $message .= "Service temporarily unavailable.\n";
            $message .= "Please try again later.\n\n";
            $message .= "1. Try again\n";
            $message .= "0. Main Menu";

            return [
                'message' => $message,
                'msgtype' => false
            ];
        }
    }    /**
     * Initiate USSD bank payment
     */
    private function initiateUssdPayment(UssdSession $session): array
    {
        $nominee = NomineeProfile::find($session->getData('nominee_id'));
        $quantity = $session->getData('vote_quantity');
        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $ussdType = $session->getData('ussd_type');

        try {
            // Initiate Paystack USSD charge
            $chargeResponse = $this->paystackService->initiateVotePayment(
                $session->msisdn,
                $quantity,
                $nominee->nominee_code,
                $session->session_id,
                $ussdType
            );

            if ($chargeResponse['status'] && isset($chargeResponse['data'])) {
                $session->storeData('payment_reference', $chargeResponse['data']['reference']);
                  $message = "BANK USSD PAYMENT\n\n";
                $message .= "Please complete payment:\n\n";
                $message .= "1. Dial: *{$ussdType}#\n";
                $message .= "2. Follow bank prompts\n";
                $message .= "3. Enter amount: GHS " . number_format($quantity * $pricePerVote, 2) . "\n";
                $message .= "4. Use reference: " . substr($chargeResponse['data']['reference'], -6) . "\n\n";
                
                if (isset($chargeResponse['data']['display_text'])) {
                    $message .= $chargeResponse['data']['display_text'] . "\n\n";
                }
                
                $message .= "Votes will be recorded automatically after successful payment.\n\n";
                $message .= "Thank you for using voteyourfav!";

                // Update session to completed state for webhook processing
                $session->update([
                    'current_screen' => 'payment_initiated',
                    'is_active' => false // Mark as inactive since user will use bank USSD
                ]);

                return [
                    'message' => $message,
                    'msgtype' => true // End USSD session - user will use bank USSD
                ];
            } else {
                $message = "PAYMENT ERROR ❌\n\n";
                $message .= "Unable to process payment.\n";
                $message .= "Please try again.\n\n";
                $message .= "1. Try again\n";
                $message .= "2. Different payment method\n";
                $message .= "0. Main Menu";

                return [
                    'message' => $message,
                    'msgtype' => false
                ];
            }
        } catch (\Exception $e) {
            Log::error('USSD Payment Error', [
                'session_id' => $session->session_id,
                'error' => $e->getMessage()
            ]);

            $message = "PAYMENT SERVICE ERROR ❌\n\n";
            $message .= "Service temporarily unavailable.\n";
            $message .= "Please try again later.\n\n";
            $message .= "1. Try again\n";
            $message .= "0. Main Menu";

            return [
                'message' => $message,
                'msgtype' => false
            ];
        }
    }

    /**
     * Handle payment processing
     */
    private function handlePaymentProcessing(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->verifyPayment($session);
            
            case '2':
                $message = "PAYMENT FAILED ❌\n\n";
                $message .= "Payment could not be completed.\n\n";
                $message .= "1. Try again\n";
                $message .= "2. Different payment method\n";
                $message .= "0. Main Menu";

                return [
                    'message' => $message,
                    'msgtype' => false
                ];
            
            case '3':
                return $this->showPaymentMethods($session);
            
            case '0':
                return $this->showMainMenu($session);
            
            default:
                return $this->invalidInput($session, "Please enter 1, 2, 3, or 0:");
        }
    }

    /**
     * Verify payment and record vote
     */
    private function verifyPayment(UssdSession $session): array
    {
        $paymentReference = $session->getData('payment_reference');
        
        if (!$paymentReference) {
            return $this->simulateSuccessfulVote($session);
        }

        try {
            // Check payment status with Paystack
            $paymentStatus = $this->paystackService->checkPaymentStatus($paymentReference);
            
            if ($paymentStatus['status'] && $paymentStatus['data']['status'] === 'success') {
                // Process successful payment
                $result = $this->paystackService->processPaymentSuccess(
                    $paymentReference,
                    $session->session_id
                );
                
                if ($result['success']) {
                    return $this->showVoteSuccessScreen($session, $result);
                }
            } else if ($paymentStatus['data']['status'] === 'pending') {
                $message = "PAYMENT PENDING ⏳\n\n";
                $message .= "Payment is still being processed.\n";
                $message .= "Please wait a moment...\n\n";
                $message .= "1. Check again\n";
                $message .= "2. Payment failed\n";
                $message .= "0. Main Menu";

                return [
                    'message' => $message,
                    'msgtype' => false
                ];
            }
            
            // Payment failed or not found
            $message = "PAYMENT VERIFICATION FAILED ❌\n\n";
            $message .= "Could not verify payment.\n";
            $message .= "Please try again.\n\n";
            $message .= "1. Try payment again\n";
            $message .= "2. Different payment method\n";
            $message .= "0. Main Menu";

            return [
                'message' => $message,
                'msgtype' => false
            ];

        } catch (\Exception $e) {
            Log::error('Payment Verification Error', [
                'session_id' => $session->session_id,
                'reference' => $paymentReference,
                'error' => $e->getMessage()
            ]);

            return $this->simulateSuccessfulVote($session);
        }
    }

    /**
     * Show vote success screen
     */
    private function showVoteSuccessScreen(UssdSession $session, array $result): array
    {
        $session->update(['current_screen' => 'vote_success']);

        $vote = $result['vote'];
        $payment = $result['payment'];
        $nominee = $result['nominee'];

        $message = "✅ VOTE SUCCESSFUL!\n\n";
        $message .= "Votes: " . $vote->votes_count . "\n";
        $message .= "For: " . $nominee->display_name . "\n";
        $message .= "Amount: GHS " . number_format($payment->amount, 2) . "\n";
        $message .= "Reference: " . $payment->reference . "\n\n";
        $message .= "Thank you for voting!\n\n";
        $message .= "1. Vote Again\n";
        $message .= "2. View Receipt\n";
        $message .= "3. Share Success\n";
        $message .= "0. Exit\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle vote success actions
     */
    private function handleVoteSuccess(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->showMainMenu($session);
            
            case '2':
                return $this->showVoteReceipt($session);
            
            case '3':
                return $this->showShareOptions($session);
            
            case '0':
                return $this->exitSession($session);
            
            default:
                return $this->invalidInput($session, "Please enter 1, 2, 3, or 0:");
        }
    }

    /**
     * Show vote receipt
     */
    private function showVoteReceipt(UssdSession $session): array
    {
        $message = "VOTE RECEIPT\n\n";
        $message .= "Transaction completed successfully.\n";
        $message .= "Receipt sent via SMS.\n\n";
        $message .= "1. Vote Again\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Show share options
     */
    private function showShareOptions(UssdSession $session): array
    {
        $message = "SHARE YOUR VOTE\n\n";
        $message .= "Encourage others to vote!\n";
        $message .= "Share link sent via SMS.\n\n";
        $message .= "1. Vote Again\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Simulate successful vote (fallback for testing)
     */
    private function simulateSuccessfulVote(UssdSession $session): array
    {
        $nominee = NomineeProfile::find($session->getData('nominee_id'));
        $quantity = $session->getData('vote_quantity');
        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $total = $quantity * $pricePerVote;

        $transactionId = 'VTC' . time() . rand(100, 999);

        $message = "✅ VOTE SUCCESSFUL!\n\n";
        $message .= $quantity . " votes for " . $nominee->display_name . "\n";
        $message .= "Total: GHS " . number_format($total, 2) . "\n";
        $message .= "Transaction ID: " . $transactionId . "\n\n";
        $message .= "Thank you for voting!\n\n";
        $message .= "1. Vote Again\n";
        $message .= "2. View Receipt\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        $session->update(['current_screen' => 'vote_success']);

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Show browse options
     */
    private function showBrowseOptions(UssdSession $session): array
    {
        $session->update(['current_screen' => 'browse_options']);

        $message = "BROWSE NOMINEES\n\n";
        $message .= "1. By Organization\n";
        $message .= "2. By Category\n";
        $message .= "3. Search by name\n";
        $message .= "4. Popular nominees\n";
        $message .= "5. Recent nominees\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Show vote history placeholder
     */
    private function showVoteHistory(UssdSession $session): array
    {
        $message = "YOUR VOTE HISTORY\n\n";
        $message .= "Feature coming soon!\n\n";
        $message .= "1. Vote Now\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Show account balance placeholder
     */
    private function showAccountBalance(UssdSession $session): array
    {
        $message = "ACCOUNT SUMMARY\n\n";
        $message .= "Phone: " . $session->msisdn . "\n";
        $message .= "Feature coming soon!\n\n";
        $message .= "1. Vote Now\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Show help menu
     */
    private function showHelpMenu(UssdSession $session): array
    {
        $session->update(['current_screen' => 'help_menu']);

        $message = "HELP & SUPPORT\n\n";
        $message .= "1. How to Vote\n";
        $message .= "2. Payment Methods\n";
        $message .= "3. Vote Pricing\n";
        $message .= "4. Contact Support\n";
        $message .= "5. Terms & Conditions\n";
        $message .= "0. Main Menu\n\n";
        $message .= "Enter choice:";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Handle help menu
     */
    private function handleHelpMenu(UssdSession $session, string $input): array
    {
        switch ($input) {
            case '1':
                return $this->showHowToVote($session);
            case '0':
                return $this->showMainMenu($session);
            default:
                return $this->invalidInput($session, "Please enter 1 or 0:");
        }
    }

    /**
     * Show how to vote help
     */
    private function showHowToVote(UssdSession $session): array
    {
        $message = "HOW TO VOTE\n\n";
        $message .= "1. Choose 'Vote for Nominee'\n";
        $message .= "2. Enter nominee code (6 chars)\n";
        $message .= "3. Enter vote quantity (1-100)\n";
        $message .= "4. Confirm payment\n";
        $message .= "5. Complete mobile money payment\n\n";
        $message .= "Votes are recorded instantly after successful payment.\n\n";
        $message .= "9. Back to Help\n";
        $message .= "0. Main Menu";

        return [
            'message' => $message,
            'msgtype' => false
        ];
    }

    /**
     * Exit session
     */
    private function exitSession(UssdSession $session): array
    {
        $session->update(['is_active' => false]);

        return [
            'message' => "Thank you for using voteyourfav!\n\nDial *920# anytime to vote again.",
            'msgtype' => true // End session
        ];
    }

    /**
     * Session timeout response
     */
    private function timeoutResponse(): array
    {
        return [
            'message' => "Session expired due to inactivity.\nPlease dial *920# to start again.\n\nThank you for using voteyourfav!",
            'msgtype' => true
        ];
    }

    /**
     * Invalid input response
     */
    private function invalidInput(UssdSession $session, string $prompt = "Invalid choice. Please try again."): array
    {
        $currentMessage = $this->getCurrentScreenMessage($session);
        
        return [
            'message' => "Invalid choice. Please try again.\n\n" . $currentMessage,
            'msgtype' => false
        ];
    }

    /**
     * Get current screen message (placeholder)
     */
    private function getCurrentScreenMessage(UssdSession $session): string
    {
        switch ($session->current_screen) {
            case 'main_menu':
                return "1. Vote for Nominee\n2. Browse Nominees\n3. My Vote History\n4. Account Balance\n5. Help & Support\n0. Exit";
            default:
                return "0. Main Menu";
        }
    }

    // Placeholder methods for browse functionality
    private function handleBrowseOrganizations(UssdSession $session, string $input): array
    {
        return $this->showMainMenu($session);
    }

    private function handleBrowseCategories(UssdSession $session, string $input): array
    {
        return $this->showMainMenu($session);
    }

    private function handleBrowseNominees(UssdSession $session, string $input): array
    {
        return $this->showMainMenu($session);
    }

    private function handleVoteHistory(UssdSession $session, string $input): array
    {
        return $this->showMainMenu($session);
    }

    private function handleAccountBalance(UssdSession $session, string $input): array
    {
        return $this->showMainMenu($session);
    }
}
