<?php

namespace App\Services;

use App\Models\NomineeProfile;
use App\Models\AwardCategory;
use App\Models\Payment;
use Illuminate\Support\Str;
use Telegram\Bot\Laravel\Facades\Telegram;
use Telegram\Bot\Keyboard\Keyboard;
use Illuminate\Support\Facades\Log;

class TelegramVotingService
{
    protected PaystackService $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Show nominee voting interface
     */
    public function showNomineeVoting($chatId, $nomineeCode)
    {
        // Use findByCode to support both slug and nominee_code formats
        $nominee = NomineeProfile::findByCode($nomineeCode);

        if (!$nominee || !$nominee->canReceiveVotes()) {
            $this->sendMessage($chatId, "Nominee not found or voting is not available.");
            return;
        }

        $category = $nominee->awardCategories->first();
        
        if (!$category || !$category->isVotingActive()) {
            $this->sendMessage($chatId, "Voting is not currently active for this nominee.");
            return;
        }

        // Calculate ranking
        $ranking = $category->nomineeProfiles()
            ->approved()
            ->where('total_votes', '>', $nominee->total_votes)
            ->count() + 1;

        $keyboard = Keyboard::make()->inline();
        
        // Quick vote options
        $voteOptions = [1, 5, 10, 25, 50];
        $rows = array_chunk($voteOptions, 3);
        
        foreach ($rows as $row) {
            $buttons = [];
            foreach ($row as $votes) {
                $price = $category->calculatePrice($votes)['final_price'];
                $buttons[] = Keyboard::inlineButton([
                    'text' => "🗳️ {$votes} vote" . ($votes > 1 ? 's' : '') . " (GHS {$price})",
                    'callback_data' => "quickvote_{$nominee->slug}_{$votes}"
                ]);
            }
            $keyboard->row($buttons);
        }

        // Custom amount and other options
        $keyboard->row([
            Keyboard::inlineButton([
                'text' => '⚡ Custom Amount',
                'callback_data' => "customvote_{$nominee->slug}"
            ])
        ]);

        $keyboard->row([
            Keyboard::inlineButton([
                'text' => '📊 View Full Profile',
                'url' => route('vote.show', $nominee->slug)
            ]),
            Keyboard::inlineButton([
                'text' => '📤 Share',
                'callback_data' => "share_{$nominee->slug}"
            ])
        ]);

        $keyboard->row([
            Keyboard::inlineButton(['text' => '« Back', 'callback_data' => "category_{$category->slug}"])
        ]);

        $message = "🎯 *{$nominee->display_name}*\n\n";
        $message .= "🏆 Category: {$category->name}\n";
        $message .= "🥇 Current Ranking: #{$ranking}\n";
        $message .= "📊 Total Votes: {$nominee->total_votes}\n";
        $message .= "💰 Price per vote: GHS {$category->price_per_vote}\n\n";
        
        if ($nominee->bio) {
            $message .= "📝 " . Str::limit($nominee->bio, 150) . "\n\n";
        }

        if ($category->discount_percentage > 0) {
            $message .= "🎉 *Special Offer:* {$category->discount_percentage}% discount on {$category->discount_min_votes}+ votes!\n\n";
        }

        $message .= "Choose how many votes to purchase:";

        $this->sendMessage($chatId, $message, $keyboard);
    }

    /**
     * Handle quick voting
     */
    public function handleQuickVote($chatId, $nomineeCode, $voteCount, $userId)
    {
        // Use findByCode to support both slug and nominee_code formats
        $nominee = NomineeProfile::findByCode($nomineeCode);
        
        if (!$nominee || !$nominee->canReceiveVotes()) {
            $this->sendMessage($chatId, "❌ Voting not available for this nominee.");
            return;
        }

        $category = $nominee->awardCategories->first();
        $pricing = $category->calculatePrice($voteCount);

        // Create a temporary payment record with Telegram user info
        $payment = Payment::create([
            'reference' => 'TG_' . Str::upper(Str::random(10)),
            'amount' => $pricing['final_price'],
            'currency' => 'GHS',
            'status' => Payment::STATUS_PENDING,
            'customer_email' => "telegram_user_{$userId}@voteyourfav.com", // Temporary email
            'customer_name' => "Telegram User {$userId}",
            'metadata' => [
                'nominee_profile_id' => $nominee->id,
                'vote_count' => $voteCount,
                'discount_applied' => $pricing['discount_percentage'],
                'telegram_user_id' => $userId,
                'telegram_chat_id' => $chatId,
                'platform' => 'telegram'
            ],
        ]);

        // Initialize payment with Paystack
        $paystackResponse = $this->paystackService->initializePayment([
            'email' => $payment->customer_email,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
            'reference' => $payment->reference,
            'callback_url' => route('payment.callback'),
            'metadata' => [
                'nominee_name' => $nominee->display_name,
                'category_name' => $category->name,
                'vote_count' => $voteCount,
                'telegram_user_id' => $userId,
                'platform' => 'telegram'
            ]
        ]);

        if ($paystackResponse['status']) {
            $keyboard = Keyboard::make()->inline();
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => '💳 Complete Payment',
                    'url' => $paystackResponse['data']['authorization_url']
                ])
            ]);
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => '📊 Check Status Later',
                    'callback_data' => "status_{$payment->reference}"
                ])
            ]);

            $message = "💳 *Payment Ready!*\n\n";
            $message .= "🎯 Nominee: {$nominee->display_name}\n";
            $message .= "🗳️ Votes: {$voteCount}\n";
            $message .= "💰 Total: GHS {$pricing['final_price']}\n";
            $message .= "📝 Reference: `{$payment->reference}`\n\n";
            $message .= "Click the button below to complete your payment securely:";

            $this->sendMessage($chatId, $message, $keyboard);
        } else {
            $this->sendMessage($chatId, "❌ Failed to initialize payment. Please try again.");
        }
    }

    /**
     * Handle custom vote amount
     */
    public function handleCustomVote($chatId, $nomineeCode, $voteCount, $userId)
    {
        // Use findByCode to support both slug and nominee_code formats
        $nominee = NomineeProfile::findByCode($nomineeCode);
        
        if (!$nominee || !$nominee->canReceiveVotes()) {
            $this->sendMessage($chatId, "❌ Voting not available for this nominee.");
            return;
        }

        $category = $nominee->awardCategories->first();
        
        // Validate vote count
        if ($voteCount < 1 || $voteCount > 100000) {
            $this->sendMessage($chatId, "❌ Please enter a valid vote count between 1 and 100,000.");
            return;
        }

        $pricing = $category->calculatePrice($voteCount);

        // Create a temporary payment record with Telegram user info
        $payment = Payment::create([
            'reference' => 'TG_' . Str::upper(Str::random(10)),
            'amount' => $pricing['final_price'],
            'currency' => 'GHS',
            'status' => Payment::STATUS_PENDING,
            'customer_email' => "telegram_user_{$userId}@voteyourfav.com", // Temporary email
            'customer_name' => "Telegram User {$userId}",
            'metadata' => [
                'nominee_profile_id' => $nominee->id,
                'vote_count' => $voteCount,
                'discount_applied' => $pricing['discount_percentage'],
                'telegram_user_id' => $userId,
                'telegram_chat_id' => $chatId,
                'platform' => 'telegram'
            ],
        ]);

        // Initialize payment with Paystack
        $paystackResponse = $this->paystackService->initializePayment([
            'email' => $payment->customer_email,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
            'reference' => $payment->reference,
            'callback_url' => route('payment.callback'),
            'metadata' => [
                'nominee_name' => $nominee->display_name,
                'category_name' => $category->name,
                'vote_count' => $voteCount,
                'telegram_user_id' => $userId,
                'platform' => 'telegram'
            ]
        ]);

        if ($paystackResponse['status']) {
            $keyboard = Keyboard::make()->inline();
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => '💳 Complete Payment',
                    'url' => $paystackResponse['data']['authorization_url']
                ])
            ]);
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => '📊 Check Status Later',
                    'callback_data' => "status_{$payment->reference}"
                ])
            ]);
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => '« Back to Nominee',
                    'callback_data' => "nominee_{$nominee->slug}"
                ])
            ]);

            $message = "💳 *Custom Vote Payment Ready!*\n\n";
            $message .= "🎯 Nominee: {$nominee->display_name}\n";
            $message .= "🗳️ Votes: {$voteCount}\n";
            
            if ($pricing['discount'] > 0) {
                $message .= "💰 Base Price: GHS {$pricing['base_price']}\n";
                $message .= "🎉 Discount: -GHS {$pricing['discount']} ({$pricing['discount_percentage']}%)\n";
                $message .= "💳 Processing Fee: GHS {$pricing['paystack_surcharge']}\n";
                $message .= "✨ *Total: GHS {$pricing['final_price']}*\n\n";
            } else {
                $message .= "💰 Total: GHS {$pricing['final_price']}\n\n";
            }
            
            $message .= "📝 Reference: `{$payment->reference}`\n\n";
            $message .= "Click the button below to complete your payment securely:";

            $this->sendMessage($chatId, $message, $keyboard);
        } else {
            $this->sendMessage($chatId, "❌ Failed to initialize payment. Please try again.");
        }
    }

    /**
     * Search nominees by name or category
     */    public function searchNominees($chatId, $query)
    {
        $nominees = NomineeProfile::approved()
            ->with(['awardCategories'])
            ->where(function($q) use ($query) {
                $q->where('display_name', 'like', "%{$query}%")
                  ->orWhere('bio', 'like', "%{$query}%")
                  ->orWhere('slug', 'like', "%{$query}%") // Search by slug
                  ->orWhere('slug', $query) // Exact slug match
                  ->orWhere('nominee_code', 'like', "%{$query}%") // Search by nominee_code
                  ->orWhere('nominee_code', $query) // Exact nominee_code match
                  ->orWhereHas('awardCategories', function($cat) use ($query) {
                      $cat->where('name', 'like', "%{$query}%");
                  });
            })
            ->byVotes()
            ->take(10)
            ->get();

        if ($nominees->isEmpty()) {
            $this->sendMessage($chatId, "🔍 No nominees found for '{$query}'. Try a different search term.");
            return;
        }

        $keyboard = Keyboard::make()->inline();
        
        foreach ($nominees as $nominee) {
            $category = $nominee->awardCategories->first();
            $keyboard->row([
                Keyboard::inlineButton([
                    'text' => "🎯 {$nominee->display_name} - {$category->name} ({$nominee->total_votes} votes)",
                    'callback_data' => "nominee_{$nominee->slug}"
                ])
            ]);
        }

        $keyboard->row([
            Keyboard::inlineButton(['text' => '« Back to Menu', 'callback_data' => 'start'])
        ]);

        $message = "🔍 *Search Results for '{$query}'*\n\n";
        $message .= "Found {$nominees->count()} nominee(s):\n";
        $message .= "Tap on a nominee to vote:";

        $this->sendMessage($chatId, $message, $keyboard);
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus($chatId, $reference)
    {
        $payment = Payment::where('reference', $reference)->first();

        if (!$payment) {
            $this->sendMessage($chatId, "❌ Payment reference not found: `{$reference}`");
            return;
        }

        $nominee = null;
        if (isset($payment->metadata['nominee_profile_id'])) {
            $nominee = NomineeProfile::find($payment->metadata['nominee_profile_id']);
        }

        $message = "📊 *Payment Status*\n\n";
        $message .= "📝 Reference: `{$payment->reference}`\n";
        $message .= "💰 Amount: GHS {$payment->amount}\n";
        $message .= "📅 Date: {$payment->created_at->format('M d, Y H:i')}\n";

        if ($nominee) {
            $message .= "🎯 Nominee: {$nominee->display_name}\n";
            $message .= "🗳️ Votes: " . ($payment->metadata['vote_count'] ?? 'N/A') . "\n";
        }

        switch ($payment->status) {
            case 'success':
                $message .= "\n✅ *Status: Payment Successful*\n";
                $message .= "Your votes have been counted!";
                break;
            case 'pending':
                $message .= "\n⏳ *Status: Payment Pending*\n";
                $message .= "Please complete your payment to count your votes.";
                
                // Try to get payment URL if still available
                $keyboard = Keyboard::make()->inline();
                $keyboard->row([
                    Keyboard::inlineButton([
                        'text' => '🔄 Refresh Status',
                        'callback_data' => "status_{$reference}"
                    ])
                ]);
                break;
            case 'failed':
                $message .= "\n❌ *Status: Payment Failed*\n";
                $message .= "Please try voting again.";
                break;
            default:
                $message .= "\n❓ *Status: Unknown*\n";
                $message .= "Please contact support if you need help.";
        }

        $this->sendMessage($chatId, $message, $keyboard ?? null);
    }

    /**
     * Share nominee voting link
     */
    public function shareNominee($chatId, $nomineeCode)
    {
        // Use findByCode to support both slug and nominee_code formats
        $nominee = NomineeProfile::findByCode($nomineeCode);

        if (!$nominee) {
            $this->sendMessage($chatId, "❌ Nominee not found.");
            return;
        }

        $votingUrl = route('vote.show', $nominee->slug);
        $category = $nominee->awardCategories->first();

        $message = "📤 *Share {$nominee->display_name}*\n\n";
        $message .= "🏆 Category: {$category->name}\n";
        $message .= "📊 Current Votes: {$nominee->total_votes}\n\n";
        $message .= "🔗 *Voting Link:*\n{$votingUrl}\n\n";
        $message .= "📱 *Share Message:*\n";
        $message .= "Vote for {$nominee->display_name} in the {$category->name} category! 🗳️✨\n\n";
        $message .= "Cast your vote now: {$votingUrl}";

        $keyboard = Keyboard::make()->inline();
        $keyboard->row([
            Keyboard::inlineButton([
                'text' => '📤 Share with Telegram',
                'url' => "https://t.me/share/url?url=" . urlencode($votingUrl) . "&text=" . urlencode("Vote for {$nominee->display_name}! 🗳️")
            ])
        ]);

        $this->sendMessage($chatId, $message, $keyboard);
    }

    /**
     * Send message helper
     */
    private function sendMessage($chatId, $text, $keyboard = null)
    {
        $params = [
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ];

        if ($keyboard) {
            $params['reply_markup'] = $keyboard;
        }

        return Telegram::sendMessage($params);
    }
}
