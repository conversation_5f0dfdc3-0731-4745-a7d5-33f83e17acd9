<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Payment;
use App\Models\Vote;

class PaystackUssdService
{
    private $secretKey;
    private $baseUrl = 'https://api.paystack.co';

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret');
    }    /**
     * Initialize mobile money payment for Ghana
     */
    public function initiateMobileMoneyPayment($msisdn, $votes, $nomineeCode, $sessionId, $provider = 'mtn')
    {
        // Find nominee to get actual price
        $nominee = \App\Models\NomineeProfile::where('nominee_code', $nomineeCode)->first();
        if (!$nominee) {
            Log::error('Nominee not found for mobile money payment', ['nominee_code' => $nomineeCode]);
            return ['status' => false, 'message' => 'Nominee not found'];
        }

        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $totalAmount = $votes * $pricePerVote;

        // Store payment record first
        $payment = \App\Models\UssdPayment::create([
            'session_id' => $sessionId,
            'nominee_profile_id' => $nominee->id,
            'msisdn' => $msisdn,
            'votes_requested' => $votes,
            'amount' => $totalAmount * 100, // Store in pesewas
            'status' => 'pending',
            'gateway' => 'paystack',
            'payment_method' => 'mobile_money',
            'provider' => $provider,
            'customer_name' => null,
            'customer_email' => $this->generateEmailFromPhone($msisdn),
            'customer_phone' => $msisdn,
            'payment_reference' => 'MM_' . $sessionId . '_' . time(),
            'gateway_reference' => null,
            'initiated_at' => now()
        ]);

        // Map providers to Paystack mobile money format
        $providerMap = [
            'mtn' => 'mtn',
            'vodafone' => 'vod',
            'airtel' => 'atl',
            'tigo' => 'atl' // Airtel-Tigo merger
        ];

        $paystackProvider = $providerMap[$provider] ?? 'mtn';
        
        $data = [
            'email' => $payment->customer_email,
            'amount' => $payment->amount, // Amount in pesewas
            'currency' => 'GHS',
            'reference' => $payment->payment_reference,
            'mobile_money' => [
                'phone' => $msisdn,
                'provider' => $paystackProvider
            ],
            'metadata' => [
                'payment_id' => $payment->id,
                'vote_count' => $votes,
                'nominee_id' => $nominee->id,
                'nominee_code' => $nomineeCode,
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'source' => 'mobile_money_voting',
                'platform' => 'voteyourfav',
                'price_per_vote' => $pricePerVote,
                'total_amount_ghs' => $totalAmount,
                'provider' => $provider
            ],
            'callback_url' => env('APP_URL') . '/api/payments/webhook'
        ];

        Log::info('Initiating Paystack Mobile Money Payment', [
            'payment_id' => $payment->id,
            'reference' => $payment->payment_reference,
            'msisdn' => $msisdn,
            'votes' => $votes,
            'amount_ghs' => $totalAmount,
            'amount_pesewas' => $payment->amount,
            'provider' => $provider,
            'paystack_provider' => $paystackProvider,
            'nominee_code' => $nomineeCode
        ]);

        $response = $this->makeRequest('POST', '/charge', $data);

        if ($response['status']) {
            // Update payment with gateway reference
            $payment->update([
                'gateway_reference' => $response['data']['reference'] ?? null,
                'gateway_response' => json_encode($response['data'])
            ]);

            Log::info('Mobile Money Payment Initiated Successfully', [
                'payment_id' => $payment->id,
                'gateway_reference' => $response['data']['reference'] ?? null,
                'display_text' => $response['data']['display_text'] ?? null
            ]);
        } else {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => json_encode($response)
            ]);

            Log::error('Mobile Money Payment Initiation Failed', [
                'payment_id' => $payment->id,
                'error' => $response['message'] ?? 'Unknown error',
                'response' => $response
            ]);
        }

        return $response;
    }

    /**
     * Initialize USSD payment charge for votes (for bank USSD)
     */
    public function initiateVotePayment($msisdn, $votes, $nomineeCode, $sessionId, $ussdType = '737')
    {
        // Note: This method is for bank USSD (*737#, *901#, etc.)
        // For mobile money USSD, use initiateMobileMoneyPayment instead
        
        $nominee = \App\Models\NomineeProfile::where('nominee_code', $nomineeCode)->first();
        if (!$nominee) {
            Log::error('Nominee not found for USSD payment', ['nominee_code' => $nomineeCode]);
            return ['status' => false, 'message' => 'Nominee not found'];
        }

        $category = $nominee->awardCategories()->first();
        $pricePerVote = $category ? $category->price_per_vote : 2.00;
        $totalAmount = $votes * $pricePerVote;

        // Store payment record first
        $payment = \App\Models\UssdPayment::create([
            'session_id' => $sessionId,
            'nominee_profile_id' => $nominee->id,
            'msisdn' => $msisdn,
            'votes_requested' => $votes,
            'amount' => $totalAmount * 100, // Store in pesewas
            'status' => 'pending',
            'gateway' => 'paystack',
            'payment_method' => 'ussd_bank',
            'provider' => $ussdType,
            'customer_name' => null,
            'customer_email' => $this->generateEmailFromPhone($msisdn),
            'customer_phone' => $msisdn,
            'payment_reference' => 'USSD_' . $sessionId . '_' . time(),
            'gateway_reference' => null,
            'initiated_at' => now()
        ]);
        
        $data = [
            'email' => $payment->customer_email,
            'amount' => $payment->amount, // Amount in pesewas
            'currency' => 'GHS',
            'reference' => $payment->payment_reference,
            'ussd' => [
                'type' => $ussdType
            ],
            'metadata' => [
                'payment_id' => $payment->id,
                'vote_count' => $votes,
                'nominee_id' => $nominee->id,
                'nominee_code' => $nomineeCode,
                'session_id' => $sessionId,
                'msisdn' => $msisdn,
                'source' => 'ussd_bank_voting',
                'platform' => 'voteyourfav',
                'price_per_vote' => $pricePerVote,
                'total_amount_ghs' => $totalAmount,
                'ussd_type' => $ussdType
            ],
            'callback_url' => env('APP_URL') . '/api/payments/webhook'
        ];

        Log::info('Initiating Paystack Bank USSD Payment', [
            'payment_id' => $payment->id,
            'reference' => $payment->payment_reference,
            'msisdn' => $msisdn,
            'votes' => $votes,
            'amount_ghs' => $totalAmount,
            'amount_pesewas' => $payment->amount,
            'ussd_type' => $ussdType,
            'nominee_code' => $nomineeCode
        ]);

        $response = $this->makeRequest('POST', '/charge', $data);

        if ($response['status']) {
            $payment->update([
                'gateway_reference' => $response['data']['reference'] ?? null,
                'gateway_response' => json_encode($response['data'])
            ]);

            Log::info('Bank USSD Payment Initiated Successfully', [
                'payment_id' => $payment->id,
                'gateway_reference' => $response['data']['reference'] ?? null,
                'display_text' => $response['data']['display_text'] ?? null
            ]);
        } else {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => json_encode($response)
            ]);

            Log::error('Bank USSD Payment Initiation Failed', [
                'payment_id' => $payment->id,
                'error' => $response['message'] ?? 'Unknown error',
                'response' => $response
            ]);
        }

        return $response;
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus($reference)
    {
        return $this->makeRequest('GET', "/charge/{$reference}");
    }

    /**
     * Submit PIN for payment completion
     */
    public function submitPin($reference, $pin)
    {
        $data = [
            'reference' => $reference,
            'pin' => $pin
        ];

        return $this->makeRequest('POST', '/charge/submit_pin', $data);
    }

    /**
     * Submit OTP for payment completion
     */
    public function submitOtp($reference, $otp)
    {
        $data = [
            'reference' => $reference,
            'otp' => $otp
        ];

        return $this->makeRequest('POST', '/charge/submit_otp', $data);
    }

    /**
     * Get available USSD banks
     */
    public function getUssdBanks()
    {
        return [
            '737' => 'GTBank (*737#)',
            '322' => 'Access Bank (*901#)',
            '058' => 'Diamond Bank (*426#)',
            '221' => 'Stanbic IBTC (*909#)',
            '214' => 'First City Monument Bank (*329#)',
            '070' => 'Fidelity Bank (*770#)',
            '011' => 'First Bank (*894#)',
            '232' => 'Sterling Bank (*822#)',
            '035' => 'Wema Bank (*945#)',
            '057' => 'Zenith Bank (*966#)'
        ];
    }

    /**
     * Process payment completion and record vote
     */
    public function processPaymentSuccess($reference, $sessionId)
    {
        $chargeDetails = $this->checkPaymentStatus($reference);
        
        if ($chargeDetails['status'] && $chargeDetails['data']['status'] === 'success') {
            $metadata = $chargeDetails['data']['metadata'];
            
            // Record the payment
            $payment = Payment::create([
                'reference' => $reference,
                'amount' => $chargeDetails['data']['amount'] / 100, // Convert from kobo
                'currency' => $chargeDetails['data']['currency'],
                'status' => 'success',
                'payment_method' => 'ussd',
                'gateway' => 'paystack',
                'gateway_response' => json_encode($chargeDetails['data']),
                'metadata' => json_encode($metadata)
            ]);

            // Find nominee and create vote
            $nominee = \App\Models\NomineeProfile::where('nominee_code', $metadata['nominee_code'])->first();
            
            if ($nominee) {
                $vote = Vote::create([
                    'nominee_profile_id' => $nominee->id,
                    'votes_count' => $metadata['votes'],
                    'session_id' => $sessionId,
                    'payment_id' => $payment->id,
                    'voter_phone' => $metadata['msisdn'],
                    'vote_method' => 'ussd'
                ]);

                Log::info('USSD Vote Recorded', [
                    'vote_id' => $vote->id,
                    'nominee' => $nominee->name,
                    'votes' => $metadata['votes'],
                    'payment_ref' => $reference
                ]);

                return [
                    'success' => true,
                    'vote' => $vote,
                    'payment' => $payment,
                    'nominee' => $nominee
                ];
            }
        }

        return ['success' => false, 'message' => 'Payment verification failed'];
    }

    /**
     * Generate email from phone number for Paystack
     */
    private function generateEmailFromPhone($msisdn)
    {
        return "voter_{$msisdn}@voteyourfav.com";
    }

    /**
     * Make HTTP request to Paystack
     */
    private function makeRequest($method, $endpoint, $data = [])
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->$method($this->baseUrl . $endpoint, $data);

            $result = $response->json();

            Log::info('Paystack API Request', [
                'endpoint' => $endpoint,
                'method' => $method,
                'status' => $response->status(),
                'response' => $result
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Paystack API Error', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => false,
                'message' => 'Payment service unavailable'
            ];
        }
    }

    /**
     * Format USSD instruction for user
     */
    public function formatUssdInstruction($chargeData)
    {
        if (!isset($chargeData['data']['display_text'])) {
            return "Payment initiated. Check your phone for USSD prompt.";
        }

        return $chargeData['data']['display_text'];
    }
}
