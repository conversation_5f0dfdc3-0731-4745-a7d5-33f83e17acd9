<?php

namespace Tests\Feature;

use App\Models\Payment;
use App\Models\Vote;
use App\Models\NomineeProfile;
use App\Models\AwardCategory;
use App\Models\Organization;
use App\Models\UssdSession;
use App\Services\PaystackService;
use App\Services\TelegramVotingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PaymentSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $organization;
    protected $category;
    protected $nominee;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
            'slug' => 'test-org',
            'is_active' => true,
        ]);

        $this->category = AwardCategory::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'A test award category',
            'organization_id' => $this->organization->id,
            'voting_start_date' => now()->subDays(1),
            'voting_end_date' => now()->addDays(30),
            'price_per_vote' => 2.00,
            'discount_percentage' => 10.00,
            'discount_min_votes' => 5,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $this->nominee = NomineeProfile::create([
            'display_name' => 'Test Nominee',
            'slug' => 'test-nominee',
            'bio' => 'A test nominee',
            'nominee_code' => 'TEST001',
            'is_active' => true,
            'total_votes' => 0,
        ]);

        // Associate nominee with category
        $this->nominee->awardCategories()->attach($this->category->id);
    }

    /** @test */
    public function test_web_payment_flow_initialization()
    {
        Http::fake([
            'api.paystack.co/*' => Http::response([
                'status' => true,
                'message' => 'Authorization URL created',
                'data' => [
                    'authorization_url' => 'https://checkout.paystack.com/test123',
                    'access_code' => 'test_access_code',
                    'reference' => 'TEST_WEB_' . time(),
                ]
            ], 200)
        ]);

        $response = $this->postJson('/vote/test-nominee/initiate-payment', [
            'vote_count' => 5,
            'voter_name' => 'Test Voter',
            'voter_email' => '<EMAIL>',
            'voter_phone' => '233241234567',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'authorization_url',
            'reference'
        ]);

        // Verify payment record was created
        $this->assertDatabaseHas('payments', [
            'status' => 'pending',
            'customer_email' => '<EMAIL>',
            'amount' => 9.00, // 5 votes with 10% discount
        ]);
    }

    /** @test */
    public function test_web_payment_webhook_processing()
    {
        // Create a pending payment
        $payment = Payment::create([
            'reference' => 'TEST_WEB_12345',
            'amount' => 10.00,
            'currency' => 'GHS',
            'status' => 'pending',
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Test Voter',
            'customer_phone' => '233241234567',
            'metadata' => [
                'nominee_profile_id' => $this->nominee->id,
                'vote_count' => 5,
                'platform' => 'web'
            ]
        ]);

        // Simulate webhook payload
        $webhookPayload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'TEST_WEB_12345',
                'status' => 'success',
                'channel' => 'card',
                'amount' => 1000, // In pesewas
                'currency' => 'GHS',
                'customer' => [
                    'email' => '<EMAIL>',
                    'phone' => '233241234567'
                ],
                'metadata' => [
                    'nominee_profile_id' => $this->nominee->id,
                    'vote_count' => 5
                ]
            ]
        ];

        $response = $this->postJson('/api/payments/webhook', $webhookPayload);

        $response->assertStatus(200);

        // Verify payment was updated
        $payment->refresh();
        $this->assertEquals('success', $payment->status);

        // Verify vote was created
        $this->assertDatabaseHas('votes', [
            'payment_id' => $payment->id,
            'nominee_profile_id' => $this->nominee->id,
            'vote_count' => 5,
        ]);

        // Verify nominee vote count was updated
        $this->nominee->refresh();
        $this->assertEquals(5, $this->nominee->total_votes);
    }

    /** @test */
    public function test_telegram_payment_initiation()
    {
        Http::fake([
            'api.paystack.co/*' => Http::response([
                'status' => true,
                'message' => 'Authorization URL created',
                'data' => [
                    'authorization_url' => 'https://checkout.paystack.com/telegram123',
                    'access_code' => 'telegram_access_code',
                    'reference' => 'TG_' . time(),
                ]
            ], 200)
        ]);

        $telegramService = app(TelegramVotingService::class);
        
        $result = $telegramService->initiateVoting([
            'nominee_slug' => 'test-nominee',
            'vote_count' => 3,
            'user_id' => '12345',
            'chat_id' => '67890',
            'voter_name' => 'Telegram User',
        ]);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('payment_url', $result);
        $this->assertArrayHasKey('reference', $result);

        // Verify payment record
        $this->assertDatabaseHas('payments', [
            'status' => 'pending',
            'customer_name' => 'Telegram User',
        ]);
    }

    /** @test */
    public function test_telegram_payment_webhook_processing()
    {
        // Create a pending Telegram payment
        $payment = Payment::create([
            'reference' => 'TG_TELEGRAM_12345',
            'amount' => 6.00,
            'currency' => 'GHS',
            'status' => 'pending',
            'customer_email' => '<EMAIL>',
            'customer_name' => 'Telegram User',
            'metadata' => [
                'nominee_profile_id' => $this->nominee->id,
                'vote_count' => 3,
                'platform' => 'telegram',
                'telegram_user_id' => '12345',
                'telegram_chat_id' => '67890'
            ]
        ]);

        // Simulate webhook for Telegram payment
        $webhookPayload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'TG_TELEGRAM_12345',
                'status' => 'success',
                'channel' => 'card',
                'amount' => 600, // In pesewas
                'currency' => 'GHS',
                'metadata' => [
                    'platform' => 'telegram',
                    'nominee_profile_id' => $this->nominee->id,
                    'vote_count' => 3
                ]
            ]
        ];

        $response = $this->postJson('/api/payments/webhook', $webhookPayload);

        $response->assertStatus(200);

        // Verify payment and vote creation
        $payment->refresh();
        $this->assertEquals('success', $payment->status);

        $this->assertDatabaseHas('votes', [
            'payment_id' => $payment->id,
            'nominee_profile_id' => $this->nominee->id,
            'vote_count' => 3,
        ]);
    }

    /** @test */
    public function test_ussd_payment_session_creation()
    {
        $ussdPayload = [
            'USERID' => 'TestUser',
            'MSISDN' => '233241234567',
            'USERDATA' => '',
            'MSGTYPE' => true, // First request
            'NETWORK' => 'MTN',
            'SESSIONID' => 'test_session_123'
        ];

        $response = $this->postJson('/api/nalo/ussd/webhook', $ussdPayload);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'USERID',
            'MSISDN',
            'MSG',
            'MSGTYPE'
        ]);

        // Verify USSD session was created
        $this->assertDatabaseHas('ussd_sessions', [
            'session_id' => 'test_session_123',
            'msisdn' => '233241234567',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function test_ussd_mobile_money_payment_flow()
    {
        Http::fake([
            'api.paystack.co/*' => Http::response([
                'status' => true,
                'message' => 'Charge initiated',
                'data' => [
                    'reference' => 'NALO_MOBILE_123',
                    'status' => 'pay_offline',
                    'display_text' => 'A payment approval has been sent to your phone.',
                ]
            ], 200)
        ]);

        // Create a USSD session with nominee and vote count
        $session = UssdSession::create([
            'session_id' => 'test_session_456',
            'msisdn' => '233541234567', // Vodafone number
            'user_id' => 'TestUser',
            'network' => 'VOD',
            'current_screen' => 'confirm_payment',
            'user_data' => json_encode([
                'nominee' => [
                    'id' => $this->nominee->id,
                    'name' => $this->nominee->display_name,
                    'code' => $this->nominee->nominee_code,
                    'category_id' => $this->category->id,
                    'price_per_vote' => $this->category->price_per_vote,
                ],
                'vote_count' => 2,
                'pricing' => [
                    'base_price' => 4.00,
                    'discount' => 0,
                    'final_price' => 4.00,
                ]
            ]),
            'is_active' => true,
        ]);

        // Simulate payment confirmation
        $ussdPayload = [
            'USERID' => 'TestUser',
            'MSISDN' => '233541234567',
            'USERDATA' => '1', // Confirm payment
            'MSGTYPE' => false,
            'SESSIONID' => 'test_session_456'
        ];

        $response = $this->postJson('/api/nalo/ussd/webhook', $ussdPayload);

        $response->assertStatus(200);

        // Verify payment record was created
        $this->assertDatabaseHas('payments', [
            'customer_phone' => '233541234567',
            'status' => 'pending',
            'payment_method' => 'mobile_money',
        ]);
    }

    /** @test */
    public function test_ussd_mobile_money_webhook_processing()
    {
        // Create a pending USSD mobile money payment
        $payment = Payment::create([
            'reference' => 'NALO_USSD_78910',
            'amount' => 4.00,
            'currency' => 'GHS',
            'status' => 'pending',
            'payment_method' => 'mobile_money',
            'customer_email' => '<EMAIL>',
            'customer_name' => 'NALO USSD Voter',
            'customer_phone' => '233241234567',
            'metadata' => [
                'nominee_profile_id' => $this->nominee->id,
                'votes_requested' => 2,
                'platform' => 'voteyourfav',
                'source' => 'nalo_ussd',
                'session_id' => 'ussd_session_789',
                'provider' => 'mtn'
            ]
        ]);

        // Simulate mobile money webhook
        $webhookPayload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'NALO_USSD_78910',
                'status' => 'success',
                'channel' => 'mobile_money',
                'amount' => 400, // In pesewas
                'currency' => 'GHS',
                'metadata' => [
                    'platform' => 'voteyourfav',
                    'nominee_profile_id' => $this->nominee->id,
                    'votes_requested' => 2
                ]
            ]
        ];

        $response = $this->postJson('/api/payments/webhook', $webhookPayload);

        $response->assertStatus(200);

        // Verify payment and vote processing
        $payment->refresh();
        $this->assertEquals('success', $payment->status);

        $this->assertDatabaseHas('votes', [
            'payment_id' => $payment->id,
            'nominee_profile_id' => $this->nominee->id,
            'vote_count' => 2,
            'voter_phone' => '233241234567',
        ]);

        // Verify nominee total votes updated
        $this->nominee->refresh();
        $this->assertEquals(2, $this->nominee->total_votes);
    }

    /** @test */
    public function test_payment_method_detection()
    {
        $testCases = [
            // Web payments
            [
                'data' => ['channel' => 'card'],
                'metadata' => [],
                'reference' => 'WEB_123',
                'expected' => 'web'
            ],
            // Telegram payments
            [
                'data' => ['channel' => 'card'],
                'metadata' => ['platform' => 'telegram'],
                'reference' => 'TG_123',
                'expected' => 'telegram'
            ],
            // USSD/Mobile Money payments
            [
                'data' => ['channel' => 'mobile_money'],
                'metadata' => ['platform' => 'voteyourfav'],
                'reference' => 'NALO_123',
                'expected' => 'ussd'
            ],
        ];

        foreach ($testCases as $case) {
            $payment = Payment::create([
                'reference' => $case['reference'],
                'amount' => 10.00,
                'currency' => 'GHS',
                'status' => 'pending',
                'customer_email' => '<EMAIL>',
                'metadata' => $case['metadata']
            ]);

            $webhookController = new \App\Http\Controllers\PaystackWebhookController(
                app(\App\Services\PaystackUssdService::class)
            );

            $reflection = new \ReflectionClass($webhookController);
            $method = $reflection->getMethod('detectVoteMethod');
            $method->setAccessible(true);

            $result = $method->invoke($webhookController, $case['data'], $case['metadata'], $payment);

            $this->assertEquals($case['expected'], $result, 
                "Failed to detect payment method for case: " . json_encode($case));
        }
    }

    /** @test */
    public function test_concurrent_payment_processing()
    {
        // Test that duplicate webhooks don't create duplicate votes
        $payment = Payment::create([
            'reference' => 'DUPLICATE_TEST_123',
            'amount' => 6.00,
            'currency' => 'GHS',
            'status' => 'pending',
            'customer_email' => '<EMAIL>',
            'metadata' => [
                'nominee_profile_id' => $this->nominee->id,
                'vote_count' => 3,
            ]
        ]);

        $webhookPayload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'DUPLICATE_TEST_123',
                'status' => 'success',
                'channel' => 'card',
                'amount' => 600,
                'metadata' => [
                    'nominee_profile_id' => $this->nominee->id,
                    'vote_count' => 3
                ]
            ]
        ];

        // Send same webhook twice
        $response1 = $this->postJson('/api/payments/webhook', $webhookPayload);
        $response2 = $this->postJson('/api/payments/webhook', $webhookPayload);

        $response1->assertStatus(200);
        $response2->assertStatus(200); // Should handle gracefully

        // Verify only one vote was created
        $votes = Vote::where('payment_id', $payment->id)->get();
        $this->assertCount(1, $votes);

        // Verify nominee vote count is correct
        $this->nominee->refresh();
        $this->assertEquals(3, $this->nominee->total_votes);
    }

    /** @test */
    public function test_invalid_nominee_payment_handling()
    {
        // Test payment with non-existent nominee
        $payment = Payment::create([
            'reference' => 'INVALID_NOMINEE_123',
            'amount' => 6.00,
            'currency' => 'GHS',
            'status' => 'pending',
            'customer_email' => '<EMAIL>',
            'metadata' => [
                'nominee_profile_id' => 99999, // Non-existent
                'vote_count' => 3,
            ]
        ]);

        $webhookPayload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'INVALID_NOMINEE_123',
                'status' => 'success',
                'metadata' => [
                    'nominee_profile_id' => 99999,
                    'vote_count' => 3
                ]
            ]
        ];

        $response = $this->postJson('/api/payments/webhook', $webhookPayload);

        // Should still update payment status but not create vote
        $payment->refresh();
        $this->assertEquals('success', $payment->status);

        // No vote should be created
        $this->assertDatabaseMissing('votes', [
            'payment_id' => $payment->id,
        ]);
    }

    /** @test */
    public function test_paystack_service_error_handling()
    {
        Http::fake([
            'api.paystack.co/*' => Http::response([
                'status' => false,
                'message' => 'Invalid API key',
            ], 401)
        ]);

        $paystackService = app(PaystackService::class);

        $result = $paystackService->initializePayment([
            'email' => '<EMAIL>',
            'amount' => 10.00,
            'reference' => 'TEST_ERROR_123',
        ]);

        $this->assertFalse($result['status']);
        $this->assertStringContainsString('Invalid API key', $result['message']);
    }
} 