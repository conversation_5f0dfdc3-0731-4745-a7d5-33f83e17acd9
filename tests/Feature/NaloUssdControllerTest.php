<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use App\Models\UssdSession;
use App\Services\PaystackService;
use Illuminate\Support\Facades\Http;

class NaloUssdControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $webhookUrl;
    protected $paystackServiceMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->webhookUrl = route('nalo.ussd.webhook');

        // Mock the PaystackService
        $this->paystackServiceMock = Mockery::mock(PaystackService::class);
        $this->app->instance(PaystackService::class, $this->paystackServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    private function createUssdRequestData(string $msisdn, string $sessionId, ?string $userData, bool $isFirstRequest): array
    {
        return [
            'USERID' => 'VoteYFav',
            'MSISDN' => $msisdn,
            'USERDATA' => $userData,
            'MSGTYPE' => $isFirstRequest,
            'NETWORK' => 'MTN',
            'SESSIONID' => $sessionId,
        ];
    }

    public function test_initial_request_shows_main_menu()
    {
        $sessionId = 'session_' . Str::random(10);
        $msisdn = '233257853094';

        $data = $this->createUssdRequestData($msisdn, $sessionId, '', true);

        $response = $this->postJson($this->webhookUrl, $data);

        $response->assertOk();
        $response->assertJson([
            'MSGTYPE' => true,
        ]);
        $this->assertStringContainsString('Welcome to VoteYourFav!', $response->json('MSG'));
        $this->assertStringContainsString('1. Vote with Nominee Code', $response->json('MSG'));
    }

    public function test_full_ussd_flow_with_valid_nominee_code_and_successful_payment()
    {
        // 1. Setup
        $organization = Organization::factory()->create(['is_active' => true]);
        $category = AwardCategory::factory()->create([
            'organization_id' => $organization->id,
            'is_active' => true,
            'price_per_vote' => 1.50,
            'discount_percentage' => 10,
            'discount_min_votes' => 10,
        ]);
        $nominee = NomineeProfile::factory()->create([
            'is_approved' => true,
            'nominee_code' => 'TEST01',
        ]);
        $nominee->awardCategories()->attach($category->id);

        $sessionId = 'session_' . Str::random(10);
        $msisdn = '233540123456'; // MTN number

        // 2. Initial Request
        $data = $this->createUssdRequestData($msisdn, $sessionId, '', true);
        $response = $this->postJson($this->webhookUrl, $data);
        $response->assertOk();
        $this->assertStringContainsString('Welcome to VoteYourFav', $response->json('MSG'));

        // 3. User chooses to vote with code
        $data = $this->createUssdRequestData($msisdn, $sessionId, '1', false);
        $response = $this->postJson($this->webhookUrl, $data);
        $response->assertOk();
        $this->assertStringContainsString('Enter nominee code', $response->json('MSG'));

        // 4. User enters valid nominee code
        $data = $this->createUssdRequestData($msisdn, $sessionId, 'TEST01', false);
        $response = $this->postJson($this->webhookUrl, $data);
        $response->assertOk();
        $this->assertStringContainsString("Nominee: {$nominee->display_name}", $response->json('MSG'));
        $this->assertStringContainsString("Category: {$category->name}", $response->json('MSG'));
        $this->assertStringContainsString("Enter votes", $response->json('MSG'));

        // 5. User enters vote count (with discount)
        $voteCount = 20;
        $data = $this->createUssdRequestData($msisdn, $sessionId, (string)$voteCount, false);
        $response = $this->postJson($this->webhookUrl, $data);
        $response->assertOk();
        $this->assertStringContainsString("Confirm your vote", $response->json('MSG'));
        $this->assertStringContainsString("Votes: {$voteCount}", $response->json('MSG'));
        $this->assertStringContainsString("Final total: GHS 27", $response->json('MSG')); // 20 * 1.5 = 30. 10% discount = 3. Final = 27

        // 6. User confirms payment
        $paystackRef = 'test_paystack_ref_' . Str::random();
        $this->paystackServiceMock
            ->shouldReceive('charge')
            ->once()
            ->andReturn([
                'status' => true,
                'message' => 'Charge created',
                'data' => [
                    'reference' => $paystackRef,
                    'status' => 'pay_offline',
                    'display_text' => 'Please complete the authorization process',
                ],
            ]);
        
        // Mock payment verification before initiating payment
        Http::fake([
            "https://api.paystack.co/transaction/verify/{$paystackRef}" => Http::response([
                'status' => true,
                'data' => [
                    'status' => 'success',
                    'amount' => 2700, // in pesewas
                    'reference' => $paystackRef,
                    'customer' => ['email' => "nalo_ussd_{$msisdn}@voteyourfav.com"],
                    'metadata' => []
                ]
            ]),
        ]);

        $data = $this->createUssdRequestData($msisdn, $sessionId, '1', false);
        $response = $this->postJson($this->webhookUrl, $data);
        $response->assertOk();
        $this->assertStringContainsString('Please complete the authorization process', $response->json('MSG'));
        
        // 7. User confirms they have paid
        $data = $this->createUssdRequestData($msisdn, $sessionId, '1', false);
        $response = $this->postJson($this->webhookUrl, $data);

        $response->assertOk();
        $response->assertJson(['MSGTYPE' => false]);
        $this->assertStringContainsString('✓ Payment successful!', $response->json('MSG'));
        $this->assertStringContainsString("Vote(s) cast for: {$nominee->display_name}", $response->json('MSG'));

        // 8. Verify database state
        $payment = \App\Models\Payment::first();
        $this->assertDatabaseHas('payments', [
            'reference' => $payment->reference,
            'status' => 'success',
            'amount' => 27,
        ]);

        $this->assertDatabaseHas('votes', [
            'nominee_profile_id' => $nominee->id,
            'vote_count' => $voteCount,
        ]);

        $nominee->refresh();
        $this->assertEquals($voteCount, $nominee->vote_count);
    }


    public function test_invalid_nominee_code_returns_error()
    {
        $sessionId = 'session_' . Str::random(10);
        $msisdn = '233551234567';

        // Navigate to nominee code entry
        $this->postJson($this->webhookUrl, $this->createUssdRequestData($msisdn, $sessionId, '', true));
        $this->postJson($this->webhookUrl, $this->createUssdRequestData($msisdn, $sessionId, '1', false));

        // Enter invalid code
        $data = $this->createUssdRequestData($msisdn, $sessionId, 'INVALIDCODE', false);
        $response = $this->postJson($this->webhookUrl, $data);

        $response->assertOk();
        $this->assertStringContainsString('Invalid nominee code: INVALIDCODE', $response->json('MSG'));
        $this->assertDatabaseMissing('ussd_sessions', ['user_data->nominee']);
    }
}
