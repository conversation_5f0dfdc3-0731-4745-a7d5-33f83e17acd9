# Admin Visibility and Image Validation Fixes

## Summary
Fixed two main issues:
1. <PERSON><PERSON> (Super admin and Organization Admins) unable to see short codes and profile images of nominees on the members page
2. Profile image upload validation limited to 2MB instead of 5MB on nomination forms

## Changes Made

### 1. Admin User Controller Updates

**File: `app/Http/Controllers/Admin/UserController.php`**

#### Index Method (Line ~15)
- Added nominee profiles data loading to the users list query
- Added specific field selection to include `nominee_code` and `profile_image`

```php
// Before
$query = User::with(['organization'])

// After  
$query = User::with(['organization', 'nomineeProfiles:id,user_id,display_name,nominee_code,profile_image'])
```

#### Show Method (Line ~110)
- Enhanced nominee profiles loading with explicit field selection
- Added proper loading of profile images and nominee codes for detailed user view

```php
// Added explicit field selection for nominee profiles
'nomineeProfiles' => function($query) {
    $query->select([
        'id', 'user_id', 'display_name', 'bio', 'profile_image', 'nominee_code', 
        'slug', 'total_votes', 'is_approved', 'is_suspended', 'why_vote_for_me',
        'social_links', 'achievements', 'created_at', 'updated_at'
    ]);
},
```

### 2. Admin UI Updates

**File: `resources/js/pages/admin/users/index.tsx`**

#### User Interface Enhancement
- Added nominee profiles type definition to User interface
- Added display of nominee codes for nominee users in the users list

#### Visual Changes
- Added short code display below email for nominee users
- Short codes displayed with blue background and monospace font for better visibility

**File: `resources/js/pages/admin/users/show.tsx`**

#### Enhanced Nominee Profile Display
- Added profile image display (12x12 rounded image)
- Added prominent short code display with blue background
- Improved layout with image and details side by side
- Added fallback user icon when no profile image is available

### 3. Image Upload Validation Updates

**File: `app/Http/Controllers/Auth/NominationController.php`**

#### Both Validation Methods Updated
- Updated `storeForOrganization` method validation
- Updated `store` method validation
- Changed from 2MB (2048KB) to 5MB (5120KB) limit

```php
// Before
'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',

// After
'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120',
```

### 4. Frontend Image Validation

**File: `resources/js/pages/auth/nomination-application.tsx`**

#### Client-side Validation Added
- Added file size check before upload (5MB limit)
- Added user-friendly error message when file exceeds limit
- Updated help text to mention 5MB limit

**File: `resources/js/pages/auth/organization-nomination-application.tsx`**

#### Same Validation Applied
- Added identical client-side validation
- Consistent 5MB limit enforcement across all nomination forms

**File: `resources/js/pages/nominee/nominee-application.tsx`**

#### Additional Form Coverage
- Applied same validation to nominee application form
- Ensures consistent experience across all forms

## Technical Details

### Backend Changes
- **Profile Image Validation**: Increased from 2MB to 5MB (2048KB → 5120KB)
- **Data Loading**: Enhanced with explicit field selection for better performance
- **Relationship Loading**: Proper eager loading of nominee profiles with required fields

### Frontend Changes
- **Client-side Validation**: File size check before server submission
- **UI Enhancements**: Visual display of profile images and short codes
- **Type Safety**: Updated TypeScript interfaces to include nominee profile data
- **User Experience**: Clear error messages and visual feedback

### Files Modified
1. `app/Http/Controllers/Admin/UserController.php` - Enhanced data loading
2. `app/Http/Controllers/Auth/NominationController.php` - Updated validation limits  
3. `resources/js/pages/admin/users/index.tsx` - Added short code display
4. `resources/js/pages/admin/users/show.tsx` - Enhanced profile display
5. `resources/js/pages/auth/nomination-application.tsx` - Added validation
6. `resources/js/pages/auth/organization-nomination-application.tsx` - Added validation
7. `resources/js/pages/nominee/nominee-application.tsx` - Added validation

## Testing Recommendations

1. **Admin Views**: Verify that admins can now see nominee codes and profile images
2. **Image Upload**: Test that 5MB images upload successfully
3. **Validation**: Test that >5MB images are rejected with proper error messages
4. **User Experience**: Ensure UI displays correctly across different screen sizes
5. **Performance**: Verify that the enhanced data loading doesn't impact page load times significantly

## Notes

- All changes maintain backward compatibility
- Enhanced data loading includes only necessary fields to optimize performance
- Client-side validation provides immediate feedback before server processing
- UI enhancements follow existing design patterns and styling