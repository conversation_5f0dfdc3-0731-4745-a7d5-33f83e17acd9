<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Organization;
use App\Models\AwardCategory;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Telegram Bot routes (no CSRF protection needed)
Route::post('/telegram/webhook', [App\Http\Controllers\TelegramBotController::class, 'webhook'])
    ->name('telegram.webhook');
Route::get('/telegram/set-webhook', [App\Http\Controllers\TelegramBotController::class, 'setWebhook'])
    ->name('telegram.set-webhook');
Route::get('/telegram/bot-info', [App\Http\Controllers\TelegramBotController::class, 'getBotInfo'])
    ->name('telegram.bot-info');
// Test route for debugging
Route::get('/telegram/test', [App\Http\Controllers\TelegramBotController::class, 'testWebhook'])
    ->name('telegram.test');

// USSD API routes (no CSRF protection needed)
Route::post('/ussd/webhook', [App\Http\Controllers\UssdController::class, 'webhook'])
    ->name('ussd.webhook');

// Paystack webhook routes (no CSRF protection needed)
Route::post('/ussd/payment-callback', [App\Http\Controllers\PaystackWebhookController::class, 'handle'])
    ->name('paystack.webhook');

// Unified Paystack webhook for all payment types (Web, Telegram, USSD)
Route::post('/payments/webhook', [App\Http\Controllers\PaystackWebhookController::class, 'handle'])
    ->name('payments.webhook');

// Test route for USSD
Route::get('/ussd/test', function () {
    return response()->json([
        'status' => 'USSD endpoint is working',
        'timestamp' => now(),
        'endpoint' => '/api/ussd/webhook'
    ]);
})->name('ussd.test');



// NALO USSD Integration Routes
Route::prefix('nalo')->name('nalo.')->group(function () {
    Route::get('/health', [App\Http\Controllers\NaloUssdController::class, 'healthCheck']);
    Route::post('/ussd/webhook', [App\Http\Controllers\NaloUssdController::class, 'webhook'])
        ->name('ussd.webhook');
});

// Public routes for registration form
Route::get('/organizations', function () {
    return Organization::where('is_active', true)->select('id', 'name')->get();
});

Route::get('/categories', function () {
    return AwardCategory::where('is_active', true)
        ->select('id', 'name', 'organization_id')
        ->get();
});