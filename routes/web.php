<?php

use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\Admin\AwardCategoryController as AdminAwardCategoryController;
use App\Http\Controllers\Admin\OrganizationController as AdminOrganizationController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\VotingController;
use App\Http\Controllers\VoteController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\Auth\NominationController;
use App\Models\AwardCategory;
use App\Models\User;
use App\Http\Controllers\Admin\AdminPendingOrganizationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    $popularCategories = AwardCategory::with('organization')
        ->where('is_active', true)
        ->where('is_suspended', false)
        // ->orderByDesc('votes_count') // Or some other popularity metric
        ->latest() // For now, get the newest ones
        ->take(6)
        ->get()
        ->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'organization_name' => $category->organization ? $category->organization->name : 'N/A',
                'description' => $category->description,
                'votes_count' => $category->votes_count, // Assuming this field exists
                'price_per_vote' => $category->price_per_vote,
                'slug' => $category->slug, // Assuming you might want a link later
            ];
        });

    return Inertia::render('welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
        'popularCategories' => $popularCategories,
    ]);
})->name('welcome');

// Dynamic robots.txt and sitemap
Route::get('/robots.txt', [App\Http\Controllers\RobotsController::class, 'index'])
    ->name('robots');
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])
    ->name('sitemap');

// Public results page - no auth required
Route::get('/results', [App\Http\Controllers\ResultsController::class, 'index'])
    ->name('results');

Route::prefix('vote')->name('vote.')->controller(VotingController::class)->group(function () {
    Route::get('/', 'categories')->name('categories');
    Route::get('nominee/', 'voteByCode')->name('nominee.form'); // General voting form
    Route::post('nominee/search', 'searchNominee')->name('nominee.search'); // Search nominee by code
    Route::get('{slug}', 'showCategory')->name('showCategory'); // Categories page
    Route::get('nominee/{slug}', 'show')->name('show'); // Nominee voting page
    Route::post('/{slug}/calculate-price', 'calculatePrice')->name('calculate-price');
    Route::post('/{slug}/initiate-payment', 'initiatePayment')->name('initiate-payment');
});

// Payment related routes
Route::get('/payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');
Route::post('/payment/verify', [PaymentController::class, 'verify'])->name('payment.verify');
Route::get('/payment/status', [PaymentController::class, 'status'])->name('payment.status');
Route::get('/payment/public-key', [PaymentController::class, 'publicKey'])->name('payment.public-key');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        $user = auth()->user();

        // Redirect admin users to admin dashboard
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return redirect()->route('admin.dashboard');
        }

        // Redirect viewer users to viewer dashboard
        if ($user->isViewer()) {
            return redirect()->route('viewer.dashboard');
        }

        if ($user->isNominee()) {
            // Eager load necessary relationships
            $user->load(['nomineeProfile.awardCategories.organization', 'organization']);

            $nomineeProfile = $user->nomineeProfile()->first(); // Get the first profile, assuming one for now


            $awardCategory = $nomineeProfile ? $nomineeProfile->awardCategories()->first() : null;
            $organization = $awardCategory ? $awardCategory->organization : $user->organization;

            return Inertia::render('nominee/dashboard', [
                'auth' => [
                    'user' => $user,
                ],
                'user' => $user->toArray(),
                'nomineeProfile' => $nomineeProfile ? $nomineeProfile->toArray() : null,
                'awardCategory' => $awardCategory ? $awardCategory->toArray() : null,
                'organization' => $organization ? $organization->toArray() : null,
            ]);
        }

        // Default dashboard for other roles or users without nominee profiles (should not happen for organizations)
        return Inertia::render('dashboard', [
            'auth' => [
                'user' => $user,
            ],
        ]);

    })->name('dashboard');


});

// Admin routes are defined in routes/admin.php

// Add these routes for nomination application
Route::middleware('guest')->group(function () {
    Route::get('apply-for-nomination', [App\Http\Controllers\Auth\NominationController::class, 'create'])
        ->name('nomination.create');
    Route::post('apply-for-nomination', [App\Http\Controllers\Auth\NominationController::class, 'store'])
        ->name('nomination.apply');
    
    // Organization-specific nomination application routes
    Route::get('apply/{organization:slug}', [App\Http\Controllers\Auth\NominationController::class, 'createForOrganization'])
        ->name('nomination.create.organization');
    Route::post('apply/{organization:slug}', [App\Http\Controllers\Auth\NominationController::class, 'storeForOrganization'])
        ->name('nomination.apply.organization');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/viewer.php';
