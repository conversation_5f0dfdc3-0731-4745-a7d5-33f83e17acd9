<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Viewer\DashboardController;
use App\Http\Controllers\Viewer\OrganizationController;
use App\Http\Controllers\Viewer\CategoryController;
use App\Http\Controllers\Viewer\UserController;

/*
|--------------------------------------------------------------------------
| Viewer Routes
|--------------------------------------------------------------------------
|
| These routes handle viewer (read-only) access to the system.
| Viewers can view organizations, categories, users, and codes but
| cannot create, update, delete, or access payment information.
|
*/

Route::middleware(['auth', 'verified', 'viewer'])->prefix('viewer')->name('viewer.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Organizations (read-only)
    Route::prefix('organizations')->name('organizations.')->controller(OrganizationController::class)->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('{organization:slug}', 'show')->name('show');
    });

    // Categories (read-only)
    Route::prefix('categories')->name('categories.')->controller(CategoryController::class)->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('{category:slug}', 'show')->name('show');
    });

    // Users (read-only)
    Route::prefix('users')->name('users.')->controller(UserController::class)->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('{user:slug}', 'show')->name('show');
    });
});
