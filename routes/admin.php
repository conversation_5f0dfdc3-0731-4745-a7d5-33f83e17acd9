<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\OrganizationController;
use App\Http\Controllers\Admin\AwardCategoryController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\OrgUserController;
use App\Http\Controllers\Admin\AdminPendingOrganizationController;
use Illuminate\Support\Facades\Route;

// Admin routes (accessible by both admin and super_admin)
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
    
    // Analytics
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');
    
    // Award Categories (scoped to organization for regular admins)
    Route::resource('categories', AwardCategoryController::class);
    Route::post('/categories/{category}/toggle-status', [AwardCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    
    // Category Nominee Management
    Route::get('/categories/{category}/nominees', [AwardCategoryController::class, 'manageNominees'])->name('categories.nominees');
    Route::post('/categories/{category}/nominees', [AwardCategoryController::class, 'assignNominee'])->name('categories.assign-nominee');
    Route::delete('/categories/{category}/nominees/{nominee_profile_id}', [AwardCategoryController::class, 'removeNominee'])->name('categories.remove-nominee');
    
    // Payments
    Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/{payment}', [PaymentController::class, 'show'])->name('payments.show');
    
    // Organization User Management (for organization admins)
    Route::get('/org-users', [OrgUserController::class, 'index'])->name('org-users.index');
    Route::get('/org-users/create', [OrgUserController::class, 'create'])->name('org-users.create');
    Route::post('/org-users', [OrgUserController::class, 'store'])->name('org-users.store');
    Route::get('/org-users/{user}', [OrgUserController::class, 'show'])->name('org-users.show');
    Route::get('/org-users/{user}/edit', [OrgUserController::class, 'edit'])->name('org-users.edit');
    Route::put('/org-users/{user}', [OrgUserController::class, 'update'])->name('org-users.update');
    Route::delete('/org-users/{user}', [OrgUserController::class, 'destroy'])->name('org-users.destroy');
    Route::post('/org-users/{user}/toggle-status', [OrgUserController::class, 'toggleStatus'])->name('org-users.toggle-status');
    
    // User Approval Management
    Route::get('/org-users/pending/approvals', [OrgUserController::class, 'pendingApprovals'])->name('org-users.pending-approvals');
    Route::post('/org-users/{user}/approve', [OrgUserController::class, 'approve'])->name('org-users.approve');
    Route::post('/org-users/{user}/reject', [OrgUserController::class, 'reject'])->name('org-users.reject');
    
});

// Super Admin only routes
Route::middleware(['auth', 'super_admin'])->prefix('admin')->name('admin.')->group(function () {
    
    // User Management
    Route::resource('users', UserController::class);
    Route::post('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
    
    // User Approval Management (Super Admin)
    Route::get('/users/pending/approvals', [UserController::class, 'pendingApprovals'])->name('users.pending-approvals');
    Route::post('/users/{user}/approve', [UserController::class, 'approve'])->name('users.approve');
    Route::post('/users/{user}/reject', [UserController::class, 'reject'])->name('users.reject');
    
    // Organization Approval Management (Super Admin)
    Route::get('/pending-organizations', [AdminPendingOrganizationController::class, 'index'])->name('pending-organizations.index');
    Route::post('/pending-organizations/{user}/approve', [AdminPendingOrganizationController::class, 'approve'])->name('pending-organizations.approve');
    Route::post('/pending-organizations/{user}/reject', [AdminPendingOrganizationController::class, 'reject'])->name('pending-organizations.reject');
    
    // Organization Management
    Route::resource('organizations', OrganizationController::class);
    Route::post('/organizations/{organization}/toggle-status', [OrganizationController::class, 'toggleStatus'])->name('organizations.toggle-status');
    
    // Settings
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [SettingsController::class, 'update'])->name('settings.update');
    Route::get('/settings/backup', [SettingsController::class, 'backup'])->name('settings.backup');
    Route::get('/settings/logs', [SettingsController::class, 'logs'])->name('settings.logs');
    
});