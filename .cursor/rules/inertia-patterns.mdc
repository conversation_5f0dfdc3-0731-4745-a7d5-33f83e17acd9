---
description:
globs:
alwaysApply: false
---
# Inertia.js Patterns & Conventions

## Overview
voteyourfav uses Inertia.js to bridge Laravel and React, providing SPA-like experience without API complexity.

## Page Components
Inertia pages are React components that receive props from Laravel controllers.

### Basic Page Structure
```tsx
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';

export default function PageName({ prop1, prop2 }) {
    return (
        <AppLayout>
            <Head title="Page Title" />
            {/* Page content */}
        </AppLayout>
    );
}
```

## Routing
- Laravel routes in [routes/web.php](mdc:routes/web.php) return Inertia responses
- Use `Inertia::render('component-name', $props)` in controllers
- Route names are used for navigation and redirects

## Navigation
- Use `@inertiajs/react` Link component for client-side navigation
- Preserve scroll position and form state automatically
- Support for partial reloads and lazy loading

## Forms
- Form submissions use Inertia's form helpers
- Automatic CSRF protection
- Validation errors passed as props
- Support for file uploads

## Layouts
Layout components wrap pages and provide consistent structure:
- `resources/js/layouts/app/` - Main application layouts
- `resources/js/layouts/auth/` - Authentication layouts
- `resources/js/layouts/settings/` - Settings layouts

## Shared Data
- Global data available to all pages via `usePage()` hook
- User authentication state, flash messages, etc.
- Configured in Laravel's `HandleInertiaRequests` middleware

## TypeScript Integration
- Type definitions in `resources/js/types/`
- Props interfaces for type safety
- Ziggy integration for route helpers

## SSR Support
- Server-side rendering configured in [vite.config.ts](mdc:vite.config.ts)
- Use `npm run build:ssr` for SSR builds
- Start SSR server with `php artisan inertia:start-ssr`
