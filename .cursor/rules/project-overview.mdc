---
description:
globs:
alwaysApply: false
---
# voteyourfav Project Overview

voteyourfav is a modern Laravel + React web application built with Inertia.js for seamless SPA-like experience.

## Technology Stack
- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 19 with TypeScript
- **Bridge**: Inertia.js
- **Styling**: Tailwind CSS 4.0
- **UI Components**: Radix UI primitives
- **Build Tool**: Vite 6.0
- **Testing**: Pest (PHP)

## Key Configuration Files
- [composer.json](mdc:composer.json) - PHP dependencies and Laravel configuration
- [package.json](mdc:package.json) - Node.js dependencies and build scripts
- [vite.config.ts](mdc:vite.config.ts) - Vite build configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [components.json](mdc:components.json) - UI components configuration

## Project Structure
- `app/` - <PERSON><PERSON> backend code
- `resources/js/` - React frontend code
- `routes/` - Application routes
- `database/` - Migrations and seeders
- `tests/` - Test files

## Development Commands
- `composer dev` - Start development servers (Laravel + Vite + Queue)
- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `php artisan test` - Run PHP tests
