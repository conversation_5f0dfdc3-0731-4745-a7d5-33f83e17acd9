---
description:
globs:
alwaysApply: false
---
# React Frontend Structure

## Pages
Pages are React components that represent full application views, located in `resources/js/pages/`.

- [resources/js/pages/welcome.tsx](mdc:resources/js/pages/welcome.tsx) - Landing page
- [resources/js/pages/dashboard.tsx](mdc:resources/js/pages/dashboard.tsx) - Main dashboard
- `resources/js/pages/auth/` - Authentication pages (login, register, etc.)
- `resources/js/pages/settings/` - Settings pages

## Components
Reusable React components are in `resources/js/components/`.

### Core App Components
- [resources/js/components/app-shell.tsx](mdc:resources/js/components/app-shell.tsx) - Main app shell
- [resources/js/components/app-header.tsx](mdc:resources/js/components/app-header.tsx) - Application header
- [resources/js/components/app-sidebar.tsx](mdc:resources/js/components/app-sidebar.tsx) - Navigation sidebar
- [resources/js/components/app-logo.tsx](mdc:resources/js/components/app-logo.tsx) - Application logo

### Navigation Components
- [resources/js/components/nav-main.tsx](mdc:resources/js/components/nav-main.tsx) - Main navigation
- [resources/js/components/nav-user.tsx](mdc:resources/js/components/nav-user.tsx) - User navigation
- [resources/js/components/breadcrumbs.tsx](mdc:resources/js/components/breadcrumbs.tsx) - Breadcrumb navigation

### UI Components
- `resources/js/components/ui/` - Reusable UI components (buttons, inputs, etc.)
- [resources/js/components/appearance-dropdown.tsx](mdc:resources/js/components/appearance-dropdown.tsx) - Theme switcher

## Layouts
Layout components wrap pages and provide consistent structure.

- `resources/js/layouts/app/` - Main application layouts
- `resources/js/layouts/auth/` - Authentication layouts
- `resources/js/layouts/settings/` - Settings layouts

## Utilities
- `resources/js/lib/` - Utility functions and helpers
- `resources/js/hooks/` - Custom React hooks
- `resources/js/types/` - TypeScript type definitions

## Styling
- Uses Tailwind CSS 4.0 for styling
- Component variants with `class-variance-authority`
- Utility functions in `tailwind-merge` and `clsx`
