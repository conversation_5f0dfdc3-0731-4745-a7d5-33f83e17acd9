---
description:
globs:
alwaysApply: false
---
# UI Components & Design System

## Component Library
voteyourfav uses a modern component library built on Radix UI primitives with Tailwind CSS styling.

## Configuration
- [components.json](mdc:components.json) - Component library configuration
- Tailwind CSS 4.0 for styling
- Radix UI for accessible primitives

## Core UI Components
Located in `resources/js/components/ui/`:

### Form Components
- Button variants with `class-variance-authority`
- Input fields with validation states
- Labels, checkboxes, selects
- Form error handling

### Layout Components
- Dialog/Modal components
- Dropdown menus
- Navigation menus
- Collapsible sections
- Separators

### Display Components
- Avatar components
- Tooltips
- Toggle components
- Typography components

## Custom Components
Application-specific components in `resources/js/components/`:

### App Structure
- [resources/js/components/app-shell.tsx](mdc:resources/js/components/app-shell.tsx) - Main app container
- [resources/js/components/app-header.tsx](mdc:resources/js/components/app-header.tsx) - Application header
- [resources/js/components/app-sidebar.tsx](mdc:resources/js/components/app-sidebar.tsx) - Navigation sidebar

### Navigation
- [resources/js/components/nav-main.tsx](mdc:resources/js/components/nav-main.tsx) - Primary navigation
- [resources/js/components/breadcrumbs.tsx](mdc:resources/js/components/breadcrumbs.tsx) - Breadcrumb navigation

### User Interface
- [resources/js/components/user-menu-content.tsx](mdc:resources/js/components/user-menu-content.tsx) - User dropdown menu
- [resources/js/components/appearance-dropdown.tsx](mdc:resources/js/components/appearance-dropdown.tsx) - Theme switcher

## Styling Conventions
- Use Tailwind utility classes
- Component variants with `cva()` from `class-variance-authority`
- Merge classes with `cn()` utility (tailwind-merge + clsx)
- Dark mode support with `dark:` prefix

## Accessibility
- Radix UI provides ARIA attributes and keyboard navigation
- Focus management and screen reader support
- Color contrast and semantic HTML

## Icons
- Lucide React for consistent iconography
- Custom icon component wrapper
- SVG optimization and tree-shaking
