---
description:
globs:
alwaysApply: false
---
# Development Workflow & Best Practices

## Getting Started
1. Install PHP dependencies: `composer install`
2. Install Node.js dependencies: `npm install`
3. Copy environment file: `cp .env.example .env`
4. Generate application key: `php artisan key:generate`
5. Run migrations: `php artisan migrate`

## Development Commands

### Backend (Laravel)
- `php artisan serve` - Start Laravel development server
- `php artisan migrate` - Run database migrations
- `php artisan migrate:fresh --seed` - Fresh migration with seeders
- `php artisan queue:work` - Start queue worker
- `php artisan test` - Run PHP tests

### Frontend (React/Vite)
- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `npm run build:ssr` - Build with SSR support
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run types` - Type check TypeScript

### Combined Development
- `composer dev` - Start all development servers (<PERSON><PERSON> + Vite + Queue)
- `composer dev:ssr` - Start with SSR support

## Code Quality Tools

### PHP
- [composer.json](mdc:composer.json) includes <PERSON><PERSON> Pint for code formatting
- Pest for testing framework
- PHPUnit configuration in [phpunit.xml](mdc:phpunit.xml)

### JavaScript/TypeScript
- [eslint.config.js](mdc:eslint.config.js) - ESLint configuration
- [.prettierrc](mdc:.prettierrc) - Prettier configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration

## File Conventions
- PHP files use PSR-4 autoloading
- React components use PascalCase naming
- TypeScript files use `.tsx` extension for components
- Utility files use kebab-case naming

## Environment Configuration
- [.env.example](mdc:.env.example) - Example environment variables
- Database configuration in `config/database.php`
- Application configuration in `config/app.php`
