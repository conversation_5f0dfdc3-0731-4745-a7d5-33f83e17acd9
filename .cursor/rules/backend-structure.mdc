---
description:
globs:
alwaysApply: false
---
# Laravel Backend Structure

## Controllers
Controllers are located in `app/Http/Controllers/` and follow Laravel conventions.

### Authentication Controllers
- [app/Http/Controllers/Auth/AuthenticatedSessionController.php](mdc:app/Http/Controllers/Auth/AuthenticatedSessionController.php) - Login/logout
- [app/Http/Controllers/Auth/RegisteredUserController.php](mdc:app/Http/Controllers/Auth/RegisteredUserController.php) - User registration
- [app/Http/Controllers/Auth/NewPasswordController.php](mdc:app/Http/Controllers/Auth/NewPasswordController.php) - Password reset
- [app/Http/Controllers/Auth/VerifyEmailController.php](mdc:app/Http/Controllers/Auth/VerifyEmailController.php) - Email verification

### Settings Controllers
- Controllers in `app/Http/Controllers/Settings/` handle user settings and preferences

## Models
- [app/Models/User.php](mdc:app/Models/User.php) - User model with authentication traits

## Routes
- [routes/web.php](mdc:routes/web.php) - Main web routes
- [routes/auth.php](mdc:routes/auth.php) - Authentication routes
- [routes/settings.php](mdc:routes/settings.php) - Settings routes
- [routes/console.php](mdc:routes/console.php) - Artisan commands

## Database
- `database/migrations/` - Database schema migrations
- `database/seeders/` - Database seeders
- `database/factories/` - Model factories for testing

## Middleware & Requests
- `app/Http/Middleware/` - Custom middleware
- `app/Http/Requests/` - Form request validation classes

## Testing
- `tests/Feature/` - Feature tests
- `tests/Unit/` - Unit tests
- [phpunit.xml](mdc:phpunit.xml) - PHPUnit configuration
