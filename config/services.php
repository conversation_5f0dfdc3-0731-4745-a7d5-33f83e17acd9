<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'paystack' => [
        'public_key' => env('PAYSTACK_PUBLIC_KEY'),
        'secret_key' => env('PAYSTACK_SECRET_KEY'),
        'secret' => env('PAYSTACK_SECRET_KEY'), // For PaystackUssdService
        'webhook_secret' => env('PAYSTACK_WEBHOOK_SECRET', null), // Default to null instead of URL
        'payment_url' => env('PAYSTACK_PAYMENT_URL', 'https://api.paystack.co'),
        'merchant_email' => env('PAYSTACK_MERCHANT_EMAIL'),
    ],

    'telegram-bot-api' => [
        'token' => env('TELEGRAM_BOT_ACCESS_TOKEN'),
    ],



    'nalo' => [
        'user_id' => env('NALO_USER_ID', 'VoteYFav'),
        'webhook_url' => env('APP_URL') . '/api/nalo/ussd/webhook',
        'session_timeout' => env('NALO_SESSION_TIMEOUT', 300), // 5 minutes
        'max_votes_per_session' => env('NALO_MAX_VOTES', 100),
        'enabled' => env('NALO_USSD_ENABLED', false),
    ],

    'mnotify' => [
        'api_key' => env('MNOTIFY_API_KEY'),
        'sender_id' => env('MNOTIFY_SENDER_ID', 'VoteYourFav'),
    ],
];
