import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { 
    BookOpen, 
    Folder, 
    LayoutGrid, 
    Users, 
    Building2, 
    Trophy, 
    CreditCard, 
    BarChart3, 
    Settings, 
    Shield,
    UserCheck,
    Vote,

    FileText,
    Database,
    Activity
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';
    const isSuperAdmin = user?.role === 'super_admin';

    // Main navigation items based on user role
    const getMainNavItems = (): NavItem[] => {
        if (!isAdmin) {
            return [
                {
                    title: 'Dashboard',
                    href: '/dashboard',
                    icon: LayoutGrid,
                },
            ];
        }

        const adminItems: NavItem[] = [
            {
                title: 'Dashboard',
                href: '/admin',
                icon: LayoutGrid,
            },
        ];

        // Organization admin items (for regular admins)
        if (!isSuperAdmin) {
            adminItems.push(
                {
                    title: 'Organization Users',
                    href: '/admin/org-users',
                    icon: Users,
                },
                {
                    title: 'Pending Approvals',
                    href: '/admin/org-users/pending/approvals',
                    icon: UserCheck,
                },
            {
                title: 'Categories',
                href: '/admin/categories',
                icon: Trophy,
            },
            {
                title: 'Payments',
                href: '/admin/payments',
                icon: CreditCard,
                }
            );
        } else {
            // Super admin gets categories and payments too
            adminItems.push(
                {
                    title: 'Categories',
                    href: '/admin/categories',
                    icon: Trophy,
                },
                {
                    title: 'Payments',
                    href: '/admin/payments',
                    icon: CreditCard,
                }
            );
        }

        // Super admin exclusive items
        if (isSuperAdmin) {
            adminItems.push(
                {
                    title: 'User Management',
                    href: '/admin/users',
                    icon: Users,
                },
                {
                    title: 'Pending Approvals',
                    href: '/admin/users/pending/approvals',
                    icon: UserCheck,
                },
                {
                    title: 'Organizations',
                    href: '/admin/organizations',
                    icon: Building2,
                },
                {
                    title: 'System Settings',
                    href: '/admin/settings',
                    icon: Settings,
                }
            );
        }

        return adminItems;
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={isAdmin ? "/admin" : "/dashboard"} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getMainNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
