import { SVGAttributes } from 'react';

export default function CediSign(props: SVGAttributes<SVGElement>) {
    return (
        <svg 
            {...props} 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
        >
            {/* Ghana Cedi symbol - ₵ */}
            <path d="M12 2v20" />
            <path d="M19 9c0-3.9-3.1-7-7-7s-7 3.1-7 7" />
            <path d="M5 15c0 3.9 3.1 7 7 7s7-3.1 7-7" />
            <path d="M8 9h8" />
            <path d="M8 15h8" />
        </svg>
    );
} 