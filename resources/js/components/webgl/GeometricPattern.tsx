import React, { useRef, useEffect } from 'react';

interface GeometricPatternProps {
    className?: string;
    color?: string;
    density?: number;
}

export const GeometricPattern: React.FC<GeometricPatternProps> = ({
    className = '',
    color = '#6366f1',
    density = 20
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationRef = useRef<number>();
    const timeRef = useRef(0);
    const shapesRef = useRef<any[]>([]);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas size
        const resizeCanvas = () => {
            canvas.width = canvas.offsetWidth * window.devicePixelRatio;
            canvas.height = canvas.offsetHeight * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Initialize shapes
        const initShapes = () => {
            shapesRef.current = [];
            for (let i = 0; i < density; i++) {
                shapesRef.current.push({
                    x: Math.random() * canvas.offsetWidth,
                    y: Math.random() * canvas.offsetHeight,
                    size: Math.random() * 30 + 10,
                    rotation: Math.random() * Math.PI * 2,
                    rotationSpeed: (Math.random() - 0.5) * 0.02,
                    opacity: Math.random() * 0.3 + 0.1,
                    type: Math.floor(Math.random() * 3), // 0: triangle, 1: square, 2: circle
                    pulsePhase: Math.random() * Math.PI * 2,
                });
            }
        };

        initShapes();

        // Draw shape functions
        const drawTriangle = (x: number, y: number, size: number, rotation: number) => {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(rotation);
            ctx.beginPath();
            ctx.moveTo(0, -size / 2);
            ctx.lineTo(-size / 2, size / 2);
            ctx.lineTo(size / 2, size / 2);
            ctx.closePath();
            ctx.restore();
        };

        const drawSquare = (x: number, y: number, size: number, rotation: number) => {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(rotation);
            ctx.beginPath();
            ctx.rect(-size / 2, -size / 2, size, size);
            ctx.restore();
        };

        const drawCircle = (x: number, y: number, size: number) => {
            ctx.beginPath();
            ctx.arc(x, y, size / 2, 0, Math.PI * 2);
        };

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);
            
            timeRef.current += 0.01;

            shapesRef.current.forEach(shape => {
                // Update rotation
                shape.rotation += shape.rotationSpeed;

                // Pulse effect
                const pulse = Math.sin(timeRef.current * 2 + shape.pulsePhase) * 0.2 + 1;
                const currentSize = shape.size * pulse;
                const currentOpacity = shape.opacity * pulse;

                // Set style
                ctx.fillStyle = `${color}${Math.floor(currentOpacity * 255).toString(16).padStart(2, '0')}`;
                ctx.strokeStyle = `${color}${Math.floor(currentOpacity * 255 * 0.5).toString(16).padStart(2, '0')}`;
                ctx.lineWidth = 1;

                // Draw shape based on type
                switch (shape.type) {
                    case 0:
                        drawTriangle(shape.x, shape.y, currentSize, shape.rotation);
                        break;
                    case 1:
                        drawSquare(shape.x, shape.y, currentSize, shape.rotation);
                        break;
                    case 2:
                        drawCircle(shape.x, shape.y, currentSize);
                        break;
                }

                ctx.fill();
                ctx.stroke();
            });

            animationRef.current = requestAnimationFrame(animate);
        };

        animate();

        return () => {
            window.removeEventListener('resize', resizeCanvas);
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [color, density]);

    return (
        <canvas
            ref={canvasRef}
            className={`absolute inset-0 pointer-events-none ${className}`}
            style={{ width: '100%', height: '100%' }}
        />
    );
}; 