import React, { useRef, useEffect } from 'react';

interface WaveEffectProps {
    className?: string;
    color?: string;
    intensity?: number;
}

export const WaveEffect: React.FC<WaveEffectProps> = ({
    className = '',
    color = '#6366f1',
    intensity = 0.3
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationRef = useRef<number>();
    const timeRef = useRef(0);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas size
        const resizeCanvas = () => {
            canvas.width = canvas.offsetWidth * window.devicePixelRatio;
            canvas.height = canvas.offsetHeight * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);
            
            const width = canvas.offsetWidth;
            const height = canvas.offsetHeight;
            
            timeRef.current += 0.02;

            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, 0, height);
            gradient.addColorStop(0, `${color}00`);
            gradient.addColorStop(0.5, `${color}${Math.floor(intensity * 255 * 0.3).toString(16).padStart(2, '0')}`);
            gradient.addColorStop(1, `${color}00`);

            // Draw multiple wave layers
            for (let layer = 0; layer < 3; layer++) {
                ctx.beginPath();
                ctx.moveTo(0, height / 2);

                for (let x = 0; x <= width; x += 2) {
                    const y = height / 2 + 
                        Math.sin((x * 0.01) + (timeRef.current * (1 + layer * 0.5))) * (20 + layer * 10) * intensity +
                        Math.sin((x * 0.005) + (timeRef.current * 0.7)) * (10 + layer * 5) * intensity;
                    
                    ctx.lineTo(x, y);
                }

                ctx.lineTo(width, height);
                ctx.lineTo(0, height);
                ctx.closePath();

                ctx.fillStyle = gradient;
                ctx.globalAlpha = 0.1 + (layer * 0.05);
                ctx.fill();
            }

            ctx.globalAlpha = 1;
            animationRef.current = requestAnimationFrame(animate);
        };

        animate();

        return () => {
            window.removeEventListener('resize', resizeCanvas);
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [color, intensity]);

    return (
        <canvas
            ref={canvasRef}
            className={`absolute inset-0 pointer-events-none ${className}`}
            style={{ width: '100%', height: '100%' }}
        />
    );
}; 