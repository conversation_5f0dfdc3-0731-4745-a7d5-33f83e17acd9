import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
// import { Vote } from 'lucide-react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthModernLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <>
            {/* Add Google Fonts */}
            <link rel="preconnect" href="https://fonts.googleapis.com" />
            <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
            <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@300;400;500;600;700;800&family=Instrument+Serif:ital@1&display=swap" rel="stylesheet" />
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
                {/* Background Elements */}
                <div className="absolute inset-0 opacity-40">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>
                <div className="absolute top-0 right-0 w-96 h-96 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-96 h-96 rounded-full blur-3xl"></div>

                {/* Navigation */}
                <nav className="relative z-10 flex items-center justify-between p-4 sm:p-6 lg:px-8">
                    <Link href={route('welcome')} className="flex items-center space-x-2">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center">
                            <AppLogoIcon className="size-full object-contain" />
                        </div>
                        <span className="text-lg sm:text-xl font-bold text-slate-900" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            VoteYourFav
                        </span>
                    </Link>
                </nav>

                {/* Main Content */}
                <div className="relative z-10 flex min-h-[calc(100vh-120px)] items-center justify-center p-4 sm:p-6">
                    <div className="w-full max-w-md sm:max-w-lg lg:max-w-2xl">
                        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 p-4 sm:p-6 lg:p-8 shadow-xl hover:bg-white/70 transition-all duration-300">
                            {/* Logo and Title */}
                            <div className="text-center mb-6 sm:mb-8">
                                <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-2xl mb-4 sm:mb-6">
                                    <AppLogoIcon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                                </div>
                                
                                <h1 className="text-xl sm:text-2xl font-bold text-slate-900 mb-2 px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                    {title}
                                </h1>
                                <p className="text-slate-600 text-sm leading-relaxed px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                    {description}
                                </p>
                            </div>

                            {/* Form Content */}
                            {children}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
} 