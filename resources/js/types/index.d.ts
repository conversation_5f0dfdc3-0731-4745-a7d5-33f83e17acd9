import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';
import type { User as Ziggy<PERSON>ser, NomineeProfile as ZiggyNomineeProfile, AwardCategory as ZiggyAwardCategory, Organization as ZiggyOrganization } from './server';

// Base Laravel model structures from Ziggy/Laravel
export type BaseUser = ZiggyUser;
export type BaseNomineeProfile = ZiggyNomineeProfile;
export type BaseAwardCategory = ZiggyAwardCategory;
export type BaseOrganization = ZiggyOrganization;

export interface Auth {
    user: User | null; // User can be null if not authenticated
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string }; // Keep Ziggy type specific
    sidebarOpen: boolean;
    flash: {
        success: string | null;
        error: string | null;
    };
    [key: string]: unknown; // Allow other shared properties
}

// Extended User type for client-side use
export type User = BaseUser & {
    approval_status?: string; // Optional client-side specific field
    // All properties from BaseUser are implicitly included
};

// Extended NomineeProfileData type
export type NomineeProfileData = BaseNomineeProfile & {
    total_votes?: number;
    is_approved?: boolean;
    // All properties from BaseNomineeProfile are implicitly included
};

// Extended AwardCategoryData type
export type AwardCategoryData = BaseAwardCategory & {
    organization?: OrganizationData; // Embed organization for easier access
    // All properties from BaseAwardCategory are implicitly included
};

// Extended OrganizationData type
export type OrganizationData = BaseOrganization & {
    // Any client-side specific fields for Organization
    // All properties from BaseOrganization are implicitly included
};

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & SharedData;
