// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function ForgotPassword({ status }: { status?: string }) {
    const { data, setData, post, processing, errors } = useForm<Required<{ email: string }>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.email'));
    };

    return (
        <AuthLayout title="Forgot password" description="Enter your email to receive a password reset link">
            <Head title="Forgot password" />

            {status && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl text-center">
                    <div className="text-sm font-medium text-green-700" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                        {status}
                    </div>
                </div>
            )}

            <div className="space-y-6">
                <form onSubmit={submit}>
                    <div className="grid gap-3">
                        <Label htmlFor="email" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Email address
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            name="email"
                            autoComplete="off"
                            value={data.email}
                            autoFocus
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />

                        <InputError message={errors.email} />
                    </div>

                    <div className="my-6 flex items-center justify-start">
                        <Button 
                            className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300" 
                            disabled={processing}
                            style={{ fontFamily: 'Urbanist, sans-serif' }}
                        >
                            {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                            Email password reset link
                        </Button>
                    </div>
                </form>

                <div className="text-center text-sm" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                    <span className="text-slate-600">Or, return to</span>{' '}
                    <TextLink 
                        href={route('login')}
                        className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    >
                        log in
                    </TextLink>
                </div>
            </div>
        </AuthLayout>
    );
}
