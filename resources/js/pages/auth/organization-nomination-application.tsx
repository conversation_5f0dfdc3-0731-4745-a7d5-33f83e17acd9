import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Upload, ExternalLink, ArrowLeft } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AuthLayout from '@/layouts/auth-layout';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Organization {
    id: number;
    name: string;
    slug: string;
    description?: string;
    logo?: string;
    website?: string;
}

interface AwardCategory {
    id: number;
    name: string;
    description?: string;
    organization_id: number;
}

interface OrganizationNominationApplicationProps {
    organization: Organization;
    categories: AwardCategory[];
}

type NominationForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    award_category_id: string;
    profile_image: File | null;
    bio: string;
    achievements: string;
    why_vote_for_me: string;
    social_links: {
        instagram?: string;
        twitter?: string;
        facebook?: string;
    };
    phone: string;
    role: string;
};

export default function OrganizationNominationApplication({ 
    organization, 
    categories 
}: OrganizationNominationApplicationProps) {
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { data, setData, post, processing, errors, reset } = useForm<Required<NominationForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        award_category_id: '',
        profile_image: null,
        bio: '',
        achievements: '',
        why_vote_for_me: '',
        social_links: {
            instagram: '',
            twitter: '',
            facebook: '',
        },
        phone: '',
        role: 'nominee',
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (file) {
            // Check file size (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB. Please choose a smaller image.');
                e.target.value = ''; // Clear the input
                return;
            }
            
            setData('profile_image', file);
            
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        } else {
            setData('profile_image', null);
            setImagePreview(null);
        }
    };

    const handleSocialLinksChange = (platform: keyof typeof data.social_links, value: string) => {
        setData('social_links', {
            ...data.social_links,
            [platform]: value
        });
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('nomination.apply.organization', organization.slug), {
            onFinish: () => reset('password', 'password_confirmation'),
            forceFormData: true,
        });
    };

    return (
        <AuthLayout 
            title={`Apply for ${organization.name}`}
            description={`Submit your nomination application for ${organization.name}`}
        >
            <Head title={`Apply for ${organization.name} - Nomination`} />
            
            <form className="flex flex-col gap-4 sm:gap-6" onSubmit={submit}>
                <div className="grid gap-4 sm:gap-6">
                    {/* Personal Information */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="name" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Full Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="name"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing}
                            placeholder="Your full name as it should appear"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base"
                        />
                        <InputError message={errors.name} className="mt-1" />
                    </div>

                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="email" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Email address <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="<EMAIL>"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="phone" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Phone Number <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="phone"
                            type="tel"
                            required
                            tabIndex={3}
                            value={data.phone}
                            onChange={(e) => setData('phone', e.target.value)}
                            disabled={processing}
                            placeholder="0234XXXXXXX"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base"
                        />
                        <InputError message={errors.phone} />
                    </div>

                    {/* Password Fields */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="password" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Password <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            disabled={processing}
                            placeholder="Create a secure password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base"
                        />
                        <InputError message={errors.password} />
                    </div>

                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="password_confirmation" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Confirm Password <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            required
                            tabIndex={5}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            disabled={processing}
                            placeholder="Confirm your password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base"
                        />
                        <InputError message={errors.password_confirmation} />
                    </div>

                    {/* Category Selection */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="award_category_id" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Award Category <span className="text-red-500">*</span>
                        </Label>
                        <Select
                            value={data.award_category_id}
                            onValueChange={(value) => setData('award_category_id', value)}
                            disabled={processing}
                        >
                            <SelectTrigger className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10 sm:h-12 text-sm sm:text-base">
                                <SelectValue placeholder="Select a category to apply for">
                                    {data.award_category_id && (
                                        <div className="text-left">
                                            {categories.find(cat => cat.id.toString() === data.award_category_id)?.name}
                                        </div>
                                    )}
                                </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                                {categories.map((category) => (
                                    <SelectItem key={category.id} value={category.id.toString()}>
                                        <div className="w-full">
                                            <div className="font-medium text-slate-900 text-sm">{category.name}</div>
                                            {category.description && (
                                                <div className="text-xs text-slate-500 mt-1 line-clamp-2">{category.description}</div>
                                            )}
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <InputError message={errors.award_category_id} />
                    </div>

                    {/* Profile Image */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="profile_image" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Profile Image (Max 3MB)
                        </Label>
                        <div className="flex flex-col gap-3">
                            <input
                                id="profile_image"
                                type="file"
                                accept="image/*"
                                onChange={handleImageChange}
                                disabled={processing}
                                className="hidden"
                            />
                            <Button
                                type="button"
                                variant="outline"
                                className="h-10 sm:h-12 border-2 border-dashed border-white/50 hover:border-blue-300 bg-white/50 hover:bg-white/80 transition-all duration-300 rounded-xl text-sm sm:text-base"
                                onClick={() => document.getElementById('profile_image')?.click()}
                                disabled={processing}
                            >
                                <Upload className="w-4 h-4 mr-2" />
                                <span className="hidden sm:inline">{data.profile_image ? 'Change Image' : 'Upload Profile Image'}</span>
                                <span className="sm:hidden">{data.profile_image ? 'Change' : 'Upload'}</span>
                            </Button>
                            
                            {imagePreview && (
                                <div className="flex justify-center">
                                    <img
                                        src={imagePreview}
                                        alt="Profile preview"
                                        className="w-20 h-20 sm:w-24 sm:h-24 object-cover rounded-lg border-2 border-white/50"
                                    />
                                </div>
                            )}
                        </div>
                        <InputError message={errors.profile_image} />
                    </div>

                    {/* Bio and Achievements */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="bio" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Bio (Optional)
                        </Label>
                        <Textarea
                            id="bio"
                            value={data.bio}
                            onChange={(e) => setData('bio', e.target.value)}
                            disabled={processing}
                            placeholder="Tell us about yourself..."
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[70px] sm:min-h-[80px] text-sm sm:text-base"
                        />
                        <InputError message={errors.bio} />
                    </div>

                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="achievements" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Achievements (Optional)
                        </Label>
                        <Textarea
                            id="achievements"
                            value={data.achievements}
                            onChange={(e) => setData('achievements', e.target.value)}
                            disabled={processing}
                            placeholder="List your key achievements..."
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[70px] sm:min-h-[80px] text-sm sm:text-base"
                        />
                        <InputError message={errors.achievements} />
                    </div>

                    <div className="grid gap-2 sm:gap-3">
                        <Label htmlFor="why_vote_for_me" className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Why should people vote for you? <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                            id="why_vote_for_me"
                            required
                            value={data.why_vote_for_me}
                            onChange={(e) => setData('why_vote_for_me', e.target.value)}
                            disabled={processing}
                            placeholder="Explain why you deserve votes in this category..."
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[90px] sm:min-h-[100px] text-sm sm:text-base"
                            maxLength={1000}
                        />
                        <div className="text-xs sm:text-sm text-slate-500">
                            {data.why_vote_for_me.length}/1000 characters
                        </div>
                        <InputError message={errors.why_vote_for_me} />
                    </div>

                    {/* Social Links */}
                    <div className="grid gap-2 sm:gap-3">
                        <Label className="text-slate-700 font-medium text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Social Media Links (Optional)
                        </Label>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            <div className="space-y-2">
                                <Label htmlFor="instagram" className="text-xs sm:text-sm text-slate-600">Instagram</Label>
                                <Input
                                    id="instagram"
                                    type="url"
                                    value={data.social_links.instagram}
                                    onChange={(e) => handleSocialLinksChange('instagram', e.target.value)}
                                    disabled={processing}
                                    placeholder="https://instagram.com/username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-9 sm:h-10 text-sm"
                                />
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="twitter" className="text-xs sm:text-sm text-slate-600">Twitter</Label>
                                <Input
                                    id="twitter"
                                    type="url"
                                    value={data.social_links.twitter}
                                    onChange={(e) => handleSocialLinksChange('twitter', e.target.value)}
                                    disabled={processing}
                                    placeholder="https://twitter.com/username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-9 sm:h-10 text-sm"
                                />
                            </div>
                            
                            <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                                <Label htmlFor="facebook" className="text-xs sm:text-sm text-slate-600">Facebook</Label>
                                <Input
                                    id="facebook"
                                    type="url"
                                    value={data.social_links.facebook}
                                    onChange={(e) => handleSocialLinksChange('facebook', e.target.value)}
                                    disabled={processing}
                                    placeholder="https://facebook.com/username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-9 sm:h-10 text-sm"
                                />
                            </div>
                        </div>
                        
                        {/* Social media link errors are handled by individual field validation */}
                    </div>

                    {/* Submit Button */}
                    <Button
                        type="submit"
                        disabled={processing}
                        className="w-full h-11 sm:h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm sm:text-base"
                        style={{ fontFamily: 'Urbanist, sans-serif' }}
                    >
                        {processing ? (
                            <>
                                <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                                <span className="hidden sm:inline">Submitting Application...</span>
                                <span className="sm:hidden">Submitting...</span>
                            </>
                        ) : (
                            <>
                                <span className="hidden sm:inline">{`Submit Application for ${organization.name}`}</span>
                                <span className="sm:hidden">Submit Application</span>
                            </>
                        )}
                    </Button>

                    {/* Login Link */}
                    <div className="text-center">
                        <p className="text-slate-600 text-sm sm:text-base" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Already have an account?{' '}
                            <TextLink href={route('login')} className="font-medium">
                                Sign in here
                            </TextLink>
                        </p>
                    </div>
                </div>
            </form>
        </AuthLayout>
    );
} 