import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { LoaderCircle } from 'lucide-react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

interface OrganizationRegistrationForm {
    organization_name: string;
    email: string;
    phone_number: string;
    password: string;
    password_confirmation: string;
}

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<OrganizationRegistrationForm>>({
        organization_name: '',
        email: '',
        phone_number: '',
        password: '',
        password_confirmation: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    return (
        <AuthLayout title="Organization Registration" description="Register your organization to get started">
            <Head title="Organization Registration" />
            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-3">
                        <Label htmlFor="organization_name" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Organization Name
                        </Label>
                        <Input
                            id="organization_name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="organization-name"
                            value={data.organization_name}
                            onChange={(e) => setData('organization_name', e.target.value)}
                            disabled={processing}
                            placeholder="Your organization's full name"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.organization_name} className="mt-2" />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="email" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Email address
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="<EMAIL>"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="phone_number" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Phone Number
                        </Label>
                        <Input
                            id="phone_number"
                            type="tel"
                            required
                            tabIndex={3}
                            autoComplete="tel"
                            value={data.phone_number}
                            onChange={(e) => setData('phone_number', e.target.value)}
                            disabled={processing}
                            placeholder="0201234567"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.phone_number} />
                        <p className="text-xs text-slate-500 mt-1">
                        </p>
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Password
                        </Label>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            disabled={processing}
                            placeholder="Enter password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.password} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password_confirmation" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Confirm Password
                        </Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            required
                            tabIndex={5}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            disabled={processing}
                            placeholder="Confirm password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.password_confirmation} />
                    </div>

                    <Button 
                        type="submit" 
                        className="mt-4 w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300" 
                        tabIndex={6} 
                        disabled={processing}
                        style={{ fontFamily: 'Urbanist, sans-serif' }}
                    >
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                        Register Organization
                    </Button>
                </div>

                <div className="text-center text-sm" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                    <span className="text-slate-600">Already have an account?</span>{' '}
                    <TextLink 
                        href={route('login')} 
                        tabIndex={7}
                        className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    >
                        Log in
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
} 