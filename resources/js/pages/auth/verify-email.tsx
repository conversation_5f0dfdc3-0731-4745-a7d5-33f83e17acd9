// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('verification.send'));
    };

    return (
        <AuthLayout title="Verify email" description="Please verify your email address by clicking on the link we just emailed to you.">
            <Head title="Email verification" />

            {status === 'verification-link-sent' && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl text-center">
                    <div className="text-sm font-medium text-green-700" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                        A new verification link has been sent to the email address you provided during registration.
                    </div>
                </div>
            )}

            <form onSubmit={submit} className="space-y-6 text-center">
                <Button 
                    disabled={processing} 
                    className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300"
                    style={{ fontFamily: 'Urbanist, sans-serif' }}
                >
                    {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                    Resend verification email
                </Button>

                <TextLink 
                    href={route('logout')} 
                    method="post" 
                    className="mx-auto block text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    style={{ fontFamily: 'Urbanist, sans-serif' }}
                >
                    Log out
                </TextLink>
            </form>
        </AuthLayout>
    );
}
