import { Head } from '@inertiajs/react';
import { <PERSON><PERSON>p, Arrow<PERSON>own, Trophy, Users, TrendingUp, Calendar, Crown, Medal, Award, BarChart3, <PERSON><PERSON><PERSON>, Activity, Sparkles, Star, Zap } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, AreaChart, Area, BarChart, Bar, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ie<PERSON>hart, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { useState, useEffect } from 'react';
import AppLogoIcon from '@/components/app-logo-icon';

interface Nominee {
    id: number;
    display_name: string;
    slug: string;
    bio: string;
    total_votes: number;
    vote_percentage: number;
}

interface Category {
    id: number;
    name: string;
    description: string;
    slug: string;
    price_per_vote: number;
    voting_start_date: string;
    voting_end_date: string;
    is_voting_active: boolean;
    nominees: Nominee[];
    total_votes: number;
    voting_trends: Array<{
        date: string;
        votes: number;
    }>;
}

interface Organization {
    id: number;
    name: string;
    description: string;
    slug: string;
    categories: Category[];
    total_votes: number;
}

interface OverallStats {
    total_votes: number;
    total_nominees: number;
    total_categories: number;
    total_organizations: number;
}

interface RecentActivity {
    id: number;
    nominee_name: string;
    nominee_slug: string;
    category_name: string;
    vote_count: number;
    voter_name: string;
    created_at: string;
}

interface ResultsProps {
    organizations: Organization[];
    overall_stats: OverallStats;
    top_categories: Category[];
    recent_activity: RecentActivity[];
    page_title: string;
}

const COLORS = [
    '#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', 
    '#ef4444', '#84cc16', '#f97316', '#ec4899', '#14b8a6'
];

const PODIUM_COLORS = {
    1: { bg: 'bg-gradient-to-br from-yellow-400 to-yellow-600', text: 'text-yellow-900', icon: Crown },
    2: { bg: 'bg-gradient-to-br from-gray-300 to-gray-500', text: 'text-gray-900', icon: Medal },
    3: { bg: 'bg-gradient-to-br from-amber-600 to-amber-800', text: 'text-amber-100', icon: Award }
};

export default function Results({ organizations, overall_stats, top_categories, recent_activity, page_title }: ResultsProps) {
    const [selectedOrg, setSelectedOrg] = useState<number | null>(null);
    const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
    const [animationKey, setAnimationKey] = useState(0);

    useEffect(() => {
        // Trigger animations on mount
        setAnimationKey(prev => prev + 1);
    }, [selectedOrg, selectedCategory]);

    const formatNumber = (num: number) => {
        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
        if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
        return num.toString();
    };



    const Podium = ({ nominees, category }: { nominees: Nominee[]; category: Category }) => {
        const topThree = nominees.slice(0, 3);
        const heights = [120, 160, 100]; // 2nd, 1st, 3rd place heights
        const order = [1, 0, 2]; // Display order: 2nd, 1st, 3rd

        return (
            <div className="flex items-end justify-center space-x-4 mb-8 relative">
                {/* Podium Background Effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-blue-100/30 to-transparent rounded-3xl"></div>
                
                {order.map((index, displayIndex) => {
                    const nominee = topThree[index];
                    if (!nominee) return null;

                    const position = index + 1;
                    const config = PODIUM_COLORS[position as keyof typeof PODIUM_COLORS];
                    const IconComponent = config.icon;
                    const height = heights[displayIndex];

                    return (
                        <div key={nominee.id} className="relative group">
                            {/* Nominee Card */}
                            <div 
                                className="relative z-10 bg-white rounded-2xl shadow-xl border border-white/50 p-4 mb-2 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                                style={{ 
                                    animation: `slideUp 0.8s ease-out ${displayIndex * 0.2}s both`,
                                    width: '140px',
                                }}
                            >
                                {/* Position Badge */}
                                <div className={`absolute -top-3 -right-3 w-10 h-10 rounded-full ${config.bg} ${config.text} flex items-center justify-center shadow-lg border-2 border-white`}>
                                    <IconComponent className="w-5 h-5" />
                                </div>

                                {/* Avatar */}
                                <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    {nominee.display_name.charAt(0)}
                                </div>

                                {/* Name */}
                                <h3 className="font-bold text-sm text-center text-slate-900 mb-2 leading-tight">
                                    {nominee.display_name}
                                </h3>

                                {/* Stats */}
                                <div className="text-center space-y-1">
                                    <div className="text-2xl font-bold text-indigo-600">
                                        {formatNumber(nominee.total_votes)}
                                    </div>
                                    <div className="text-xs text-slate-600">votes</div>
                                    <div className="text-sm font-semibold text-emerald-600">
                                        {nominee.vote_percentage}%
                                    </div>
                                </div>

                                {/* Progress Bar */}
                                <div className="mt-3 bg-slate-200 rounded-full h-2 overflow-hidden">
                                    <div 
                                        className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-1000 ease-out"
                                        style={{ 
                                            width: `${nominee.vote_percentage}%`,
                                            animation: `expandWidth 1.5s ease-out ${displayIndex * 0.3}s both`
                                        }}
                                    ></div>
                                </div>
                            </div>

                            {/* Podium Base */}
                            <div 
                                className={`${config.bg} rounded-t-2xl shadow-lg relative overflow-hidden`}
                                style={{ 
                                    height: `${height}px`,
                                    animation: `growHeight 1s ease-out ${displayIndex * 0.2 + 0.5}s both`
                                }}
                            >
                                {/* Sparkle Effect */}
                                <div className="absolute inset-0 opacity-20">
                                    {[...Array(6)].map((_, i) => (
                                        <Sparkles 
                                            key={i} 
                                            className="absolute w-4 h-4 text-white animate-pulse" 
                                            style={{
                                                top: `${20 + (i * 15)}%`,
                                                left: `${10 + (i * 20)}%`,
                                                animationDelay: `${i * 0.5}s`
                                            }}
                                        />
                                    ))}
                                </div>

                                {/* Position Number */}
                                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <div className={`w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm ${config.text} flex items-center justify-center font-bold text-2xl shadow-lg`}>
                                        {position}
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        );
    };

    const CategoryCard = ({ category, orgName }: { category: Category; orgName: string }) => {
        const topNominee = category.nominees[0];
        const isActive = category.is_voting_active;

        return (
            <div 
                className="bg-white rounded-3xl shadow-xl border border-white/50 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer group"
                onClick={() => setSelectedCategory(category)}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 text-white relative overflow-hidden">
                    <div className="absolute inset-0 bg-black/10"></div>
                    <div className="relative z-10">
                        <div className="flex items-center justify-between mb-3">
                            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                                isActive 
                                    ? 'bg-green-500/20 text-green-100 border border-green-400/30' 
                                    : 'bg-orange-500/20 text-orange-100 border border-orange-400/30'
                            }`}>
                                {isActive ? 'Active' : 'Ended'}
                            </div>
                            <Trophy className="w-6 h-6 text-yellow-300" />
                        </div>
                        <h3 className="font-bold text-lg mb-2 group-hover:text-yellow-300 transition-colors">
                            {category.name}
                        </h3>
                        <p className="text-sm opacity-90">{orgName}</p>
                    </div>
                </div>

                {/* Stats Grid */}
                <div className="p-6">
                    <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-600 mb-1">
                                {formatNumber(category.total_votes)}
                            </div>
                            <div className="text-sm text-slate-600">Total Votes</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-emerald-600 mb-1">
                                {category.nominees.length}
                            </div>
                            <div className="text-sm text-slate-600">Nominees</div>
                        </div>
                    </div>

                    {/* Top Nominee */}
                    {topNominee && (
                        <div className="bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-4 border border-indigo-100">
                            <div className="flex items-center space-x-3">
                                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-lg">
                                    <Crown className="w-6 h-6" />
                                </div>
                                <div className="flex-1">
                                    <div className="font-semibold text-slate-900 mb-1">
                                        {topNominee.display_name}
                                    </div>
                                    <div className="text-sm text-slate-600">
                                        Leading with {formatNumber(topNominee.total_votes)} votes ({topNominee.vote_percentage}%)
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}


                </div>
            </div>
        );
    };

    return (
        <>
            <Head>
                <title>{page_title}</title>
                <meta name="description" content="Live voting results and statistics for all categories and nominees. Real-time data visualization and rankings." />
                <meta name="mobile-web-app-capable" content="yes" />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
                {/* Background Elements */}
                <div className="absolute inset-0 opacity-30">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>
                <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-purple-400/20 to-transparent rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-indigo-400/20 to-transparent rounded-full blur-3xl"></div>

                {/* Navigation */}
                <nav className="relative z-10 flex items-center justify-between p-4 sm:p-6 lg:px-8">
                    <div className="flex items-center space-x-2">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center">
                            <AppLogoIcon className="size-full object-contain" />
                        </div>
                        <span className="text-lg sm:text-xl font-bold text-slate-900" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            VoteYourFav
                        </span>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2 lg:space-x-4">
                        <a href="/" className="w-full sm:w-auto px-4 py-2 text-center text-slate-600 hover:text-slate-900 font-medium rounded-full hover:bg-white/50 transition-all duration-300">
                            Home
                        </a>
                        <a href="/vote" className="w-full sm:w-auto inline-flex items-center justify-center px-4 sm:px-6 py-2.5 bg-white/80 backdrop-blur-sm text-slate-700 border border-white/50 rounded-full font-medium hover:bg-white hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-0.5 transition-all duration-300">
                            <span className="sm:mr-2">Vote Now</span>
                            <Trophy className="w-4 h-4 ml-2 sm:ml-0" />
                        </a>
                    </div>
                </nav>

                {/* Hero Section */}
                <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-20">
                    <div className="text-center mb-16">
                        <div className="inline-flex items-center px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full text-sm font-medium text-indigo-700 border border-indigo-200/50 mb-6 hover:bg-white/80 transition-all duration-300">
                            <Activity className="w-4 h-4 mr-2" />
                            Live Results Dashboard
                        </div>
                        
                        <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold text-slate-900 mb-6 leading-tight" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Voting{' '}
                            <span 
                                className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600"
                                style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                            >
                                Results
                            </span>
                        </h1>
                        
                        <p className="text-lg md:text-xl text-slate-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                            Real-time voting statistics, beautiful visualizations, and comprehensive results for all categories and nominees.
                        </p>
                    </div>

                    {/* Overall Stats */}
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 mb-16" key={animationKey}>
                        {[
                            { label: 'Total Votes', value: formatNumber(overall_stats.total_votes), icon: TrendingUp, color: 'from-blue-500 to-blue-600' },
                            { label: 'Nominees', value: overall_stats.total_nominees, icon: Users, color: 'from-emerald-500 to-emerald-600' },
                            { label: 'Categories', value: overall_stats.total_categories, icon: BarChart3, color: 'from-purple-500 to-purple-600' },
                            { label: 'Organizations', value: overall_stats.total_organizations, icon: Award, color: 'from-orange-500 to-orange-600' },
                        ].map((stat, index) => (
                            <div 
                                key={stat.label}
                                className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/50 hover:bg-white/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-500 group"
                                style={{ animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both` }}
                            >
                                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-gradient-to-r ${stat.color} flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                                    <stat.icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                                </div>
                                <div className="text-xl sm:text-2xl font-bold text-slate-900 mb-1">
                                    {stat.value}
                                </div>
                                <div className="text-xs sm:text-sm text-slate-600">
                                    {stat.label}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Organization Tabs */}
                    <div className="mb-12">
                        <div className="flex flex-wrap gap-2 justify-center mb-8">
                            <button
                                onClick={() => setSelectedOrg(null)}
                                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                                    selectedOrg === null
                                        ? 'bg-indigo-600 text-white shadow-lg hover:bg-indigo-700'
                                        : 'bg-white/60 backdrop-blur-sm text-slate-700 hover:bg-white/80 border border-white/50'
                                }`}
                            >
                                All Organizations
                            </button>
                            {organizations.map((org) => (
                                <button
                                    key={org.id}
                                    onClick={() => setSelectedOrg(org.id)}
                                    className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                                        selectedOrg === org.id
                                            ? 'bg-indigo-600 text-white shadow-lg hover:bg-indigo-700'
                                            : 'bg-white/60 backdrop-blur-sm text-slate-700 hover:bg-white/80 border border-white/50'
                                    }`}
                                >
                                    {org.name}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Categories Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                        {organizations
                            .filter(org => selectedOrg === null || org.id === selectedOrg)
                            .flatMap(org => 
                                org.categories.map(category => (
                                    <CategoryCard 
                                        key={category.id} 
                                        category={category} 
                                        orgName={org.name}
                                    />
                                ))
                            )}
                    </div>

                    {/* Category Detail Modal */}
                    {selectedCategory && (
                        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={() => setSelectedCategory(null)}>
                            <div className="bg-white rounded-3xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
                                {/* Modal Header */}
                                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-8 text-white relative overflow-hidden">
                                    <div className="absolute inset-0 bg-black/10"></div>
                                    <div className="relative z-10 flex items-center justify-between">
                                        <div>
                                            <h2 className="text-3xl font-bold mb-2">{selectedCategory.name}</h2>
                                            <p className="text-indigo-100">{selectedCategory.description}</p>
                                            <div className="flex items-center space-x-4 mt-4 text-sm">
                                                <span className="bg-white/20 px-3 py-1 rounded-full">
                                                    {formatNumber(selectedCategory.total_votes)} votes
                                                </span>
                                                <span className="bg-white/20 px-3 py-1 rounded-full">
                                                    {selectedCategory.nominees.length} nominees
                                                </span>
                                            </div>
                                        </div>
                                        <button 
                                            onClick={() => setSelectedCategory(null)}
                                            className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center hover:bg-white/30 transition-colors"
                                        >
                                            <span className="text-2xl">×</span>
                                        </button>
                                    </div>
                                </div>

                                <div className="p-8">
                                    {/* Podium */}
                                    {selectedCategory.nominees.length > 0 && (
                                        <div className="mb-12">
                                            <h3 className="text-2xl font-bold text-slate-900 mb-8 text-center">🏆 Top Performers</h3>
                                            <Podium nominees={selectedCategory.nominees} category={selectedCategory} />
                                        </div>
                                    )}

                                    {/* Voting Trends Chart */}
                                    {selectedCategory.voting_trends && selectedCategory.voting_trends.length > 0 && (
                                        <div className="mb-12">
                                            <h3 className="text-2xl font-bold text-slate-900 mb-6">📈 Voting Trends (Last 30 Days)</h3>
                                            <div className="bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-indigo-100">
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <AreaChart data={selectedCategory.voting_trends}>
                                                        <defs>
                                                            <linearGradient id="votesGradient" x1="0" y1="0" x2="0" y2="1">
                                                                <stop offset="5%" stopColor="#6366f1" stopOpacity={0.3}/>
                                                                <stop offset="95%" stopColor="#6366f1" stopOpacity={0}/>
                                                            </linearGradient>
                                                        </defs>
                                                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                                                        <XAxis 
                                                            dataKey="date" 
                                                            stroke="#64748b"
                                                            tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                                        />
                                                        <YAxis stroke="#64748b" />
                                                        <Tooltip 
                                                            contentStyle={{ 
                                                                backgroundColor: 'white', 
                                                                borderRadius: '12px', 
                                                                border: '1px solid #e2e8f0',
                                                                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                                                            }}
                                                            labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', { 
                                                                weekday: 'long', 
                                                                year: 'numeric', 
                                                                month: 'long', 
                                                                day: 'numeric' 
                                                            })}
                                                        />
                                                        <Area 
                                                            type="monotone" 
                                                            dataKey="votes" 
                                                            stroke="#6366f1" 
                                                            strokeWidth={3}
                                                            fill="url(#votesGradient)" 
                                                        />
                                                    </AreaChart>
                                                </ResponsiveContainer>
                                            </div>
                                        </div>
                                    )}

                                    {/* All Nominees List */}
                                    <div>
                                        <h3 className="text-2xl font-bold text-slate-900 mb-6">📊 All Nominees</h3>
                                        <div className="space-y-4">
                                            {selectedCategory.nominees.map((nominee, index) => (
                                                <div 
                                                    key={nominee.id}
                                                    className="bg-white rounded-2xl p-6 border border-slate-200 shadow-sm hover:shadow-lg transition-all duration-300"
                                                    style={{ animation: `fadeInUp 0.5s ease-out ${index * 0.1}s both` }}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-4">
                                                            <div className="relative">
                                                                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                                                    {nominee.display_name.charAt(0)}
                                                                </div>
                                                                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-slate-900 text-white flex items-center justify-center text-sm font-bold shadow-lg">
                                                                    {index + 1}
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <h4 className="font-bold text-lg text-slate-900">{nominee.display_name}</h4>
                                                                <p className="text-slate-600 text-sm">{nominee.bio}</p>
                                                            </div>
                                                        </div>
                                                        <div className="text-right">
                                                            <div className="text-2xl font-bold text-indigo-600 mb-1">
                                                                {formatNumber(nominee.total_votes)}
                                                            </div>
                                                            <div className="text-sm text-slate-600 mb-2">votes</div>
                                                            <div className="text-lg font-semibold text-emerald-600">
                                                                {nominee.vote_percentage}%
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Progress Bar */}
                                                    <div className="mt-4 bg-slate-200 rounded-full h-3 overflow-hidden">
                                                        <div 
                                                            className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-1000 ease-out"
                                                            style={{ 
                                                                width: `${nominee.vote_percentage}%`,
                                                                animation: `expandWidth 1.5s ease-out ${index * 0.1}s both`
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                </div>
            </div>

            <style>{`
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes slideUp {
                    from {
                        opacity: 0;
                        transform: translateY(50px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes expandWidth {
                    from {
                        width: 0%;
                    }
                    to {
                        width: var(--target-width);
                    }
                }

                @keyframes growHeight {
                    from {
                        height: 0px;
                    }
                    to {
                        height: var(--target-height);
                    }
                }
            `}</style>
        </>
    );
} 