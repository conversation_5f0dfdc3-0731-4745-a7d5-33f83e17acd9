import React from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Users, 
  Award, 
  TrendingUp, 
  Mail, 
  Phone, 
  Globe, 
  UserCheck,
  Trophy
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { type BreadcrumbItem } from '@/types';

interface Organization {
  id: number;
  name: string;
  slug: string;
  description: string;
  contact_email: string;
  contact_phone?: string;
  website?: string;
  logo?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  users: any[];
  awardCategories: any[];
}

interface Stats {
  total_users: number;
  total_admins: number;
  total_nominees: number;
  total_categories: number;
  active_categories: number;
  total_nominees_profiles: number;
  total_votes: number;
  total_revenue: number;
}

interface ShowOrganizationProps {
  organization: Organization;
  stats: Stats;
}

export default function ShowOrganization({ organization, stats }: ShowOrganizationProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Organizations', href: '/viewer/organizations' },
    { title: organization.name, href: `/viewer/organizations/${organization.slug}` },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${organization.name} - Organization Details`} />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {organization.logo && (
              <img
                src={`/storage/${organization.logo}`}
                alt={organization.name}
                className="h-16 w-16 rounded-xl object-cover border-2 border-blue-100"
              />
            )}
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <span className="text-gray-800">{organization.name}</span>
                <Badge 
                  variant={organization.is_active ? 'default' : 'secondary'}
                  className={organization.is_active 
                    ? 'bg-green-500 hover:bg-green-600 text-white border-0' 
                    : 'bg-gray-400 hover:bg-gray-500 text-white border-0'
                  }
                >
                  {organization.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </h1>
              <p className="text-gray-600 mt-1">Organization Details</p>
            </div>
          </div>
        </div>

        {/* Description */}
        {organization.description && (
          <Card>
            <CardHeader>
              <CardTitle>About {organization.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">{organization.description}</p>
            </CardContent>
          </Card>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Users</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.total_users}</p>
                  <p className="text-xs text-gray-500 mt-1">{stats.total_admins} admins, {stats.total_nominees} nominees</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Categories</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.total_categories}</p>
                  <p className="text-xs text-gray-500 mt-1">{stats.active_categories} active</p>
                </div>
                <div className="p-3 bg-green-100 rounded-lg">
                  <Award className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Votes</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.total_votes.toLocaleString()}</p>
                  <p className="text-xs text-gray-500 mt-1">{stats.total_nominees_profiles} nominee profiles</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Trophy className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
                  <p className="text-3xl font-bold text-gray-900 flex items-center gap-1">
                    <CediSign className="h-5 w-5" />
                    {parseFloat(stats.total_revenue.toString()).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Organization Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Organization Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Name</span>
                <span className="text-sm font-semibold text-gray-900">{organization.name}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Slug</span>
                <span className="text-sm font-mono text-gray-700 bg-gray-100 px-2 py-1 rounded">
                  {organization.slug}
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <Badge 
                  variant={organization.is_active ? 'default' : 'secondary'}
                  className={organization.is_active 
                    ? 'bg-green-500 hover:bg-green-600 text-white border-0' 
                    : 'bg-gray-400 hover:bg-gray-500 text-white border-0'
                  }
                >
                  {organization.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Created</span>
                <span className="text-sm text-gray-700">
                  {new Date(organization.created_at).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium text-gray-600">Last Updated</span>
                <span className="text-sm text-gray-700">
                  {new Date(organization.updated_at).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Email</p>
                    <p className="text-sm text-gray-900">{organization.contact_email}</p>
                  </div>
                </div>
                
                {organization.contact_phone && (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Phone</p>
                      <p className="text-sm text-gray-900">{organization.contact_phone}</p>
                    </div>
                  </div>
                )}
                
                {organization.website && (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Website</p>
                      <a 
                        href={organization.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {organization.website}
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Categories (if any) */}
        {organization.awardCategories && organization.awardCategories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-amber-500" />
                Award Categories ({organization.awardCategories.length})
              </CardTitle>
              <CardDescription>
                Categories managed by this organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {organization.awardCategories.slice(0, 6).map((category: any) => (
                  <div key={category.id} className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900">{category.name}</h4>
                      <Badge 
                        variant={category.is_active ? 'default' : 'secondary'}
                        className={category.is_active 
                          ? 'bg-green-500 hover:bg-green-600 text-white border-0 text-xs' 
                          : 'bg-gray-400 hover:bg-gray-500 text-white border-0 text-xs'
                        }
                      >
                        {category.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    {category.description && (
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{category.description}</p>
                    )}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Created {new Date(category.created_at).toLocaleDateString()}</span>
                      <span className="flex items-center gap-1">
                        <CediSign className="h-3 w-3" />
                        {parseFloat(category.price_per_vote).toFixed(2)}/vote
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              {organization.awardCategories.length > 6 && (
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-500">
                    And {organization.awardCategories.length - 6} more categories...
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
