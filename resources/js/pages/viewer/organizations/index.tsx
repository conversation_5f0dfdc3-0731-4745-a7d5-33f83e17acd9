import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
    Building2, 
    Eye,
    Search,
    CheckCircle,
    XCircle,
    Users,
    Trophy
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface Organization {
    id: number;
    name: string;
    slug: string;
    description: string;
    is_active: boolean;
    award_categories_count: number;
    users_count: number;
    created_at: string;
}

interface Stats {
    total: number;
    active: number;
    inactive: number;
}

interface OrganizationsIndexProps {
    organizations: {
        data: Organization[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    stats: Stats;
    filters: {
        search?: string;
        status?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Organizations', href: '/viewer/organizations' },
];

export default function OrganizationsIndex({ organizations, stats, filters }: OrganizationsIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/viewer/organizations', { ...filters, search: searchTerm }, { preserveState: true });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Organizations - Viewer" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Organizations
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                View all organizations and their details
                            </p>
                        </div>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800 w-fit">
                            <Eye className="h-3 w-3 mr-1" />
                            Read-only View
                        </Badge>
                    </div>

                    {/* Stats */}
                    <div className="grid gap-6 md:grid-cols-3">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-blue-100 rounded-xl">
                                        <Building2 className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Organizations</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-green-100 rounded-xl">
                                        <CheckCircle className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Organizations</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-red-100 rounded-xl">
                                        <XCircle className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Inactive Organizations</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.inactive}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Search */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex items-center space-x-4">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search organizations..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Organizations List */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Organizations List</CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">Organization</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Categories</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Users</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Created</th>
                                            <th className="text-right p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-100">
                                        {organizations.data.map((org) => (
                                            <tr key={org.id} className="hover:bg-gray-50/50 transition-colors">
                                                <td className="p-4">
                                                    <div>
                                                        <h4 className="font-medium text-gray-900">{org.name}</h4>
                                                        <p className="text-sm text-gray-600">{org.description}</p>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <Badge 
                                                        variant={org.is_active ? 'default' : 'secondary'}
                                                        className={org.is_active 
                                                            ? 'bg-green-100 text-green-800 border-green-200' 
                                                            : 'bg-red-100 text-red-800 border-red-200'
                                                        }
                                                    >
                                                        {org.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Trophy className="h-3 w-3" />
                                                        {org.award_categories_count}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Users className="h-3 w-3" />
                                                        {org.users_count}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <span className="text-sm text-gray-600">{org.created_at}</span>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex justify-end">
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/viewer/organizations/${org.slug}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {organizations.total === 0 && (
                                <div className="text-center py-12">
                                    <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No organizations found</h3>
                                    <p className="text-gray-600">No organizations match your search criteria.</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
