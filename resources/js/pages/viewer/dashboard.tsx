import { Head } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { 
    Building2, 
    Trophy, 
    Users, 
    Eye,
    CheckCircle,
    XCircle,
    Clock,
    UserCheck,
    Shield,
    Activity,
    BarChart3,
    TrendingUp
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface Stats {
    total_organizations: number;
    active_organizations: number;
    total_categories: number;
    active_categories: number;
    total_users: number;
    total_admins: number;
    total_nominees: number;
    total_viewers: number;
    approved_nominees: number;
    pending_nominees: number;
}

interface Organization {
    id: number;
    name: string;
    slug: string;
    is_active: boolean;
    categories_count: number;
    users_count: number;
    created_at: string;
}

interface Category {
    id: number;
    name: string;
    slug: string;
    organization_name: string;
    is_active: boolean;
    is_suspended: boolean;
    voting_status: string;
    created_at: string;
}

interface ViewerDashboardProps {
    stats: Stats;
    recent_organizations: Organization[];
    recent_categories: Category[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
];

export default function ViewerDashboard({ stats, recent_organizations, recent_categories }: ViewerDashboardProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Viewer Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Viewer Dashboard
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Read-only access to system information and statistics
                            </p>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                <Eye className="h-3 w-3 mr-1" />
                                Viewer Access
                            </Badge>
                        </div>
                    </div>

                    {/* Stats Overview */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-blue-100 rounded-xl">
                                        <Building2 className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Organizations</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_organizations}</p>
                                        <p className="text-xs text-green-600">{stats.active_organizations} active</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-purple-100 rounded-xl">
                                        <Trophy className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Categories</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_categories}</p>
                                        <p className="text-xs text-green-600">{stats.active_categories} active</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-green-100 rounded-xl">
                                        <Users className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_users}</p>
                                        <div className="flex space-x-2 text-xs">
                                            <span className="text-blue-600">{stats.total_admins} admins</span>
                                            <span className="text-orange-600">{stats.total_nominees} nominees</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-yellow-100 rounded-xl">
                                        <UserCheck className="h-6 w-6 text-yellow-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Nominees</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.approved_nominees}</p>
                                        <p className="text-xs text-orange-600">{stats.pending_nominees} pending</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <div className="grid gap-6 md:grid-cols-3">
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <Building2 className="h-5 w-5 text-blue-600" />
                                    Organizations
                                </CardTitle>
                                <CardDescription>View all organizations and their details</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button asChild className="w-full" variant="outline">
                                    <Link href="/viewer/organizations">
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Organizations
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <Trophy className="h-5 w-5 text-purple-600" />
                                    Categories
                                </CardTitle>
                                <CardDescription>Browse award categories and nominees</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button asChild className="w-full" variant="outline">
                                    <Link href="/viewer/categories">
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Categories
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg flex items-center gap-2">
                                    <Users className="h-5 w-5 text-green-600" />
                                    Users
                                </CardTitle>
                                <CardDescription>View users and their nominee codes</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button asChild className="w-full" variant="outline">
                                    <Link href="/viewer/users">
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Users
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Recent Data */}
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Recent Organizations */}
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    <span className="flex items-center gap-2">
                                        <Building2 className="h-5 w-5 text-blue-600" />
                                        Recent Organizations
                                    </span>
                                    <Button asChild variant="ghost" size="sm">
                                        <Link href="/viewer/organizations">View All</Link>
                                    </Button>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recent_organizations.map((org) => (
                                    <div key={org.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <h4 className="font-medium text-gray-900">{org.name}</h4>
                                            <p className="text-sm text-gray-600">
                                                {org.categories_count} categories • {org.users_count} users
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {org.is_active ? (
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            ) : (
                                                <XCircle className="h-4 w-4 text-red-600" />
                                            )}
                                            <Button asChild variant="ghost" size="sm">
                                                <Link href={`/viewer/organizations/${org.slug}`}>
                                                    <Eye className="h-3 w-3" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Recent Categories */}
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    <span className="flex items-center gap-2">
                                        <Trophy className="h-5 w-5 text-purple-600" />
                                        Recent Categories
                                    </span>
                                    <Button asChild variant="ghost" size="sm">
                                        <Link href="/viewer/categories">View All</Link>
                                    </Button>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {recent_categories.map((category) => (
                                    <div key={category.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <h4 className="font-medium text-gray-900">{category.name}</h4>
                                            <p className="text-sm text-gray-600">{category.organization_name}</p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge 
                                                variant={category.voting_status === 'Active' ? 'default' : 'secondary'}
                                                className="text-xs"
                                            >
                                                {category.voting_status}
                                            </Badge>
                                            <Button asChild variant="ghost" size="sm">
                                                <Link href={`/viewer/categories/${category.slug}`}>
                                                    <Eye className="h-3 w-3" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
