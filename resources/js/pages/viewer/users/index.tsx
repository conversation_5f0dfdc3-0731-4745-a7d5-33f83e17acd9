import React from 'react';
import { <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  Search, 
  Eye,
  User,
  Building2,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

interface UserData {
  id: number;
  name: string;
  email: string;
  slug: string;
  role: string;
  is_suspended: boolean;
  created_at: string;
  organization?: {
    id: number;
    name: string;
    slug: string;
  };
  nominee_profiles?: any[];
}

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

interface UsersIndexProps {
  users: {
    data: UserData[];
    pagination?: PaginationData;
  };
  filters: {
    search?: string;
    role?: string;
    organization?: string;
  };
}

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'destructive';
    case 'admin':
      return 'default';
    case 'nominee':
      return 'secondary';
    case 'viewer':
      return 'outline';
    default:
      return 'outline';
  }
};

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin';
    case 'admin':
      return 'Admin';
    case 'nominee':
      return 'Nominee';
    case 'viewer':
      return 'Viewer';
    default:
      return role;
  }
};

export default function UsersIndex({ users, filters }: UsersIndexProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Users', href: '/viewer/users' },
  ];

  const pagination = users.pagination;

  const getPageNumbers = () => {
    if (!pagination) return [];
    
    const { current_page, last_page } = pagination;
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, current_page - delta); i <= Math.min(last_page - 1, current_page + delta); i++) {
      range.push(i);
    }

    if (current_page - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (current_page + delta < last_page - 1) {
      rangeWithDots.push('...', last_page);
    } else if (last_page > 1) {
      rangeWithDots.push(last_page);
    }

    return rangeWithDots;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Users - Viewer" />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Users</h1>
            <p className="text-gray-600 mt-1">View and browse system users</p>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search users by name or email..."
                  defaultValue={filters.search}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  All Users
                </CardTitle>
                <CardDescription>
                  {pagination ? `Showing ${pagination.from}-${pagination.to} of ${pagination.total} users` : `${users.data.length} users`}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {users.data.length > 0 ? (
                users.data.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage 
                          src={user.nominee_profiles?.[0]?.profile_image ? `/storage/${user.nominee_profiles[0].profile_image}` : undefined} 
                          alt={user.name} 
                        />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {user.name.split(' ').map(name => name[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="font-semibold text-gray-900">{user.name}</h3>
                          <Badge variant={getRoleBadgeVariant(user.role)}>
                            {getRoleLabel(user.role)}
                          </Badge>
                          {user.is_suspended && (
                            <Badge variant="destructive" className="text-xs">
                              Suspended
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{user.email}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          {user.organization && (
                            <span className="flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {user.organization.name}
                            </span>
                          )}
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Joined {new Date(user.created_at).toLocaleDateString()}
                          </span>
                          {user.nominee_profiles && user.nominee_profiles.length > 0 && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {user.nominee_profiles.length} profile(s)
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                      >
                        <a href={`/viewer/users/${user.slug}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </a>
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                  <p className="text-gray-600">No users match your current filters.</p>
                </div>
              )}
            </div>

            {/* Pagination */}
            {pagination && pagination.last_page > 1 && (
              <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
                <div className="flex items-center text-sm text-gray-700">
                  <span>
                    Showing <span className="font-medium">{pagination.from}</span> to{' '}
                    <span className="font-medium">{pagination.to}</span> of{' '}
                    <span className="font-medium">{pagination.total}</span> results
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Previous page */}
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pagination.current_page === 1}
                    asChild={pagination.current_page !== 1}
                  >
                    {pagination.current_page === 1 ? (
                      <>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </>
                    ) : (
                      <a href={`/viewer/users?page=${pagination.current_page - 1}${filters.search ? `&search=${filters.search}` : ''}`}>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </a>
                    )}
                  </Button>

                  {/* Page numbers */}
                  <div className="hidden sm:flex items-center space-x-1">
                    {getPageNumbers().map((page, index) => (
                      <React.Fragment key={index}>
                        {page === '...' ? (
                          <span className="px-3 py-2 text-gray-500">...</span>
                        ) : (
                          <Button
                            variant={pagination.current_page === page ? 'default' : 'outline'}
                            size="sm"
                            className="min-w-[40px]"
                            asChild={pagination.current_page !== page}
                          >
                            {pagination.current_page === page ? (
                              <span>{page}</span>
                            ) : (
                              <a href={`/viewer/users?page=${page}${filters.search ? `&search=${filters.search}` : ''}`}>
                                {page}
                              </a>
                            )}
                          </Button>
                        )}
                      </React.Fragment>
                    ))}
                  </div>

                  {/* Next page */}
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pagination.current_page === pagination.last_page}
                    asChild={pagination.current_page !== pagination.last_page}
                  >
                    {pagination.current_page === pagination.last_page ? (
                      <>
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </>
                    ) : (
                      <a href={`/viewer/users?page=${pagination.current_page + 1}${filters.search ? `&search=${filters.search}` : ''}`}>
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
