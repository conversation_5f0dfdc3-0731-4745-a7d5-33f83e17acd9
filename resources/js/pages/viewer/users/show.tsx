import React from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  User, 
  Mail, 
  Phone, 
  Building2, 
  Calendar,
  Trophy,
  Award,
  Clock,
  AlertCircle
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { type BreadcrumbItem } from '@/types';

interface UserData {
  id: number;
  name: string;
  email: string;
  slug: string;
  phone?: string;
  bio?: string;
  role: string;
  is_suspended: boolean;
  suspension_reason?: string;
  suspension_notice?: string;
  created_at: string;
  updated_at: string;
  organization?: {
    id: number;
    name: string;
    slug: string;
  };
  nominee_profiles?: any[];
  managed_categories?: any[];
}

interface Stats {
  total_votes_received: number;
  total_revenue_generated: number;
  account_created: string;
  last_login: string;
}

interface ShowUserProps {
  user: UserData;
  stats: Stats;
}

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'destructive';
    case 'admin':
      return 'default';
    case 'nominee':
      return 'secondary';
    case 'viewer':
      return 'outline';
    default:
      return 'outline';
  }
};

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin';
    case 'admin':
      return 'Admin';
    case 'nominee':
      return 'Nominee';
    case 'viewer':
      return 'Viewer';
    default:
      return role;
  }
};

export default function ShowUser({ user, stats }: ShowUserProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Users', href: '/viewer/users' },
    { title: user.name, href: `/viewer/users/${user.slug}` },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${user.name} - User Details`} />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage 
                src={user.nominee_profiles?.[0]?.profile_image ? `/storage/${user.nominee_profiles[0].profile_image}` : undefined} 
                alt={user.name} 
              />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-lg">
                {user.name.split(' ').map(name => name[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <span className="text-gray-800">{user.name}</span>
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleLabel(user.role)}
                </Badge>
                {user.is_suspended && (
                  <Badge variant="destructive">
                    Suspended
                  </Badge>
                )}
              </h1>
              <p className="text-gray-600 mt-1">{user.email}</p>
            </div>
          </div>
        </div>

        {/* Suspension Alert */}
        {user.is_suspended && (user.suspension_reason || user.suspension_notice) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Account Suspended:</strong> {user.suspension_reason || user.suspension_notice}
            </AlertDescription>
          </Alert>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Votes Received</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.total_votes_received.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-lg">
                  <Trophy className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Revenue Generated</p>
                  <p className="text-3xl font-bold text-gray-900 flex items-center gap-1">
                    <CediSign className="h-5 w-5" />
                    {parseFloat(stats.total_revenue_generated.toString()).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Award className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Account Created</p>
                  <p className="text-lg font-bold text-gray-900">{new Date(stats.account_created).toLocaleDateString()}</p>
                  <p className="text-xs text-gray-500">{new Date(stats.account_created).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Last Login</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats.last_login ? new Date(stats.last_login).toLocaleDateString() : 'Never'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {stats.last_login ? new Date(stats.last_login).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) : 'No login recorded'}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-lg">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Full Name</span>
                <span className="text-sm font-semibold text-gray-900">{user.name}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Email</span>
                <span className="text-sm text-gray-700">{user.email}</span>
              </div>
              {user.phone && (
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Phone</span>
                  <span className="text-sm text-gray-700">{user.phone}</span>
                </div>
              )}
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Role</span>
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleLabel(user.role)}
                </Badge>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <Badge variant={user.is_suspended ? 'destructive' : 'default'}>
                  {user.is_suspended ? 'Suspended' : 'Active'}
                </Badge>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium text-gray-600">Joined</span>
                <span className="text-sm text-gray-700">
                  {new Date(user.created_at).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Organization & Role Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {user.organization ? (
                <>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm font-medium text-gray-600">Organization</span>
                    <span className="text-sm font-semibold text-gray-900">{user.organization.name}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm font-medium text-gray-600">Organization Slug</span>
                    <span className="text-sm font-mono text-gray-700 bg-gray-100 px-2 py-1 rounded">
                      {user.organization.slug}
                    </span>
                  </div>
                </>
              ) : (
                <div className="py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-500">No organization assigned</span>
                </div>
              )}
              
              {user.nominee_profiles && user.nominee_profiles.length > 0 && (
                <div className="py-2 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600 block mb-2">Nominee Profiles</span>
                  <span className="text-sm text-gray-700">
                    {user.nominee_profiles.length} profile(s) created
                  </span>
                </div>
              )}

              {user.managed_categories && user.managed_categories.length > 0 && (
                <div className="py-2">
                  <span className="text-sm font-medium text-gray-600 block mb-2">Managed Categories</span>
                  <span className="text-sm text-gray-700">
                    Managing {user.managed_categories.length} categor{user.managed_categories.length === 1 ? 'y' : 'ies'}
                  </span>
                </div>
              )}

              {user.bio && (
                <div className="py-2">
                  <span className="text-sm font-medium text-gray-600 block mb-2">Bio</span>
                  <p className="text-sm text-gray-700 leading-relaxed">{user.bio}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Nominee Profiles */}
        {user.nominee_profiles && user.nominee_profiles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-amber-500" />
                Nominee Profiles ({user.nominee_profiles.length})
              </CardTitle>
              <CardDescription>
                Profiles created by this user for award nominations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {user.nominee_profiles.map((profile: any) => (
                  <div key={profile.id} className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                    <div className="flex items-center space-x-3 mb-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage 
                          src={profile.profile_image ? `/storage/${profile.profile_image}` : undefined} 
                          alt={profile.display_name} 
                        />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {profile.display_name.split(' ').map((name: string) => name[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-semibold text-gray-900">{profile.display_name}</h4>
                        <p className="text-sm text-gray-600">{profile.award_category?.name || 'No category'}</p>
                      </div>
                    </div>
                    {profile.bio && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{profile.bio}</p>
                    )}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Created {new Date(profile.created_at).toLocaleDateString()}</span>
                      <span>{profile.votes_count || 0} votes</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Managed Categories */}
        {user.managed_categories && user.managed_categories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-blue-500" />
                Managed Categories ({user.managed_categories.length})
              </CardTitle>
              <CardDescription>
                Award categories managed by this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {user.managed_categories.map((category: any) => (
                  <div key={category.id} className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900">{category.name}</h4>
                      <Badge 
                        variant={category.is_active ? 'default' : 'secondary'}
                        className={category.is_active 
                          ? 'bg-green-500 hover:bg-green-600 text-white border-0 text-xs' 
                          : 'bg-gray-400 hover:bg-gray-500 text-white border-0 text-xs'
                        }
                      >
                        {category.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    {category.description && (
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{category.description}</p>
                    )}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Created {new Date(category.created_at).toLocaleDateString()}</span>
                      <span className="flex items-center gap-1">
                        <CediSign className="h-3 w-3" />
                        {parseFloat(category.price_per_vote).toFixed(2)}/vote
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
