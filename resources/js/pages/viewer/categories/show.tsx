import React from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Award, 
  Users, 
  Vote, 
  Calendar, 
  Building2, 
  TrendingUp,
  BarChart3
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { type BreadcrumbItem } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface TopNominee {
  id: number;
  display_name: string;
  votes_count: number;
  profile_image?: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  price_per_vote: string | number;
  discount_percentage: string | number;
  discount_min_votes: number;
  voting_start_date?: string;
  voting_end_date?: string;
  is_active: boolean;
  is_suspended: boolean;
  suspension_reason?: string;
  created_at: string;
  organization: Organization;
  voting_status: string;
}

interface Stats {
  total_nominees: number;
  total_votes: number;
  total_revenue: number;
  avg_votes_per_nominee: number;
}

interface ShowCategoryProps {
  category: Category;
  stats: Stats;
  topNominees: TopNominee[];
}

export default function ShowCategory({ category, stats, topNominees }: ShowCategoryProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Categories', href: '/viewer/categories' },
    { title: category.name, href: `/viewer/categories/${category.slug}` },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Not Started':
        return 'bg-blue-100 text-blue-800';
      case 'Ended':
        return 'bg-gray-100 text-gray-800';
      case 'Suspended':
        return 'bg-red-100 text-red-800';
      case 'Inactive':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${category.name} - Category Details`} />
      
      <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900 mb-2">
              {category.name}
            </h1>
            <div className="flex flex-wrap items-center gap-3">
              <Badge 
                className={`${getStatusColor(category.voting_status)} border-0 text-sm font-medium`}
              >
                {category.voting_status}
              </Badge>
              {category.is_suspended && (
                <Badge variant="destructive" className="text-sm font-medium">
                  Suspended
                </Badge>
              )}
              <span className="text-sm text-gray-500 flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                {category.organization.name}
              </span>
            </div>
          </div>
        </div>

        {/* Description */}
        {category.description && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">{category.description}</p>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Nominees</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.total_nominees}</p>
                </div>
                <div className="p-2 sm:p-3 bg-blue-100 rounded-lg">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Votes</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.total_votes.toLocaleString()}</p>
                </div>
                <div className="p-2 sm:p-3 bg-green-100 rounded-lg">
                  <Vote className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 flex items-center gap-1">
                    <CediSign className="h-4 w-4 sm:h-5 sm:w-5" />
                    {parseFloat(stats.total_revenue.toString()).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <div className="p-2 sm:p-3 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Avg Votes/Nominee</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.avg_votes_per_nominee}</p>
                </div>
                <div className="p-2 sm:p-3 bg-orange-100 rounded-lg">
                  <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Category Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Voting Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Price per Vote</span>
                <span className="text-sm font-semibold text-gray-900 flex items-center gap-1">
                  <CediSign className="h-3 w-3" />
                  {parseFloat(category.price_per_vote.toString()).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Discount Percentage</span>
                <span className="text-sm font-semibold text-gray-900">
                  {category.discount_percentage}%
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Min Votes for Discount</span>
                <span className="text-sm font-semibold text-gray-900">
                  {category.discount_min_votes}
                </span>
              </div>
              {category.voting_start_date && (
                <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">Voting Start</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {new Date(category.voting_start_date).toLocaleDateString()}
                  </span>
                </div>
              )}
              {category.voting_end_date && (
                <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <span className="text-sm font-medium text-gray-600">Voting End</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {new Date(category.voting_end_date).toLocaleDateString()}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Category Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Organization</span>
                <span className="text-sm font-semibold text-gray-900">
                  {category.organization.name}
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <Badge 
                  variant={category.is_active ? 'default' : 'secondary'}
                  className={category.is_active 
                    ? 'bg-green-500 hover:bg-green-600 text-white border-0' 
                    : 'bg-gray-400 hover:bg-gray-500 text-white border-0'
                  }
                >
                  {category.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600">Created</span>
                <span className="text-sm font-semibold text-gray-900">
                  {new Date(category.created_at).toLocaleDateString()}
                </span>
              </div>
              {category.is_suspended && category.suspension_reason && (
                <div className="py-2">
                  <span className="text-sm font-medium text-gray-600 block mb-1">Suspension Reason</span>
                  <span className="text-sm text-red-600 bg-red-50 px-2 py-1 rounded">
                    {category.suspension_reason}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Top Nominees */}
        {topNominees && topNominees.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Award className="h-5 w-5 text-amber-500" />
                Top Performing Nominees
              </CardTitle>
              <CardDescription>
                Leading nominees by vote count in this category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topNominees.map((nominee, index) => (
                  <div key={nominee.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-bold">
                        {index + 1}
                      </div>
                      <Avatar className="h-10 w-10">
                        <AvatarImage 
                          src={nominee.profile_image ? `/storage/${nominee.profile_image}` : undefined} 
                          alt={nominee.display_name} 
                        />
                        <AvatarFallback>
                          {nominee.display_name.split(' ').map(name => name[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold text-gray-900">{nominee.display_name}</p>
                        <p className="text-sm text-gray-500">Nominee</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">{nominee.votes_count.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">votes</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
