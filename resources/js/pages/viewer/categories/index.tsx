import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Trophy, 
    Eye,
    Search,
    CheckCircle,
    XCircle,
    Users,
    Building2,
    Activity
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    organization_name: string;
    is_active: boolean;
    is_suspended: boolean;
    voting_status: string;
    nominee_profiles_count: number;
    price_per_vote: number;
    voting_start_date?: string;
    voting_end_date?: string;
    created_at: string;
}

interface Organization {
    id: number;
    name: string;
}

interface Stats {
    total: number;
    active: number;
    inactive: number;
}

interface CategoriesIndexProps {
    categories: {
        data: Category[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    organizations: Organization[];
    stats: Stats;
    filters: {
        search?: string;
        status?: string;
        organization_id?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Viewer Dashboard', href: '/viewer' },
    { title: 'Categories', href: '/viewer/categories' },
];

export default function CategoriesIndex({ categories, organizations, stats, filters }: CategoriesIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || 'all');
    const [selectedOrg, setSelectedOrg] = useState(filters.organization_id || 'all');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/viewer/categories', { 
            search: searchTerm, 
            status: selectedStatus === 'all' ? undefined : selectedStatus,
            organization_id: selectedOrg === 'all' ? undefined : selectedOrg
        }, { preserveState: true });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get('/viewer/categories', { 
            ...filters, 
            [key]: value === 'all' ? undefined : value 
        }, { preserveState: true });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Categories - Viewer" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Award Categories
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                View all award categories and their nominees
                            </p>
                        </div>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800 w-fit">
                            <Eye className="h-3 w-3 mr-1" />
                            Read-only View
                        </Badge>
                    </div>

                    {/* Stats */}
                    <div className="grid gap-6 md:grid-cols-3">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-purple-100 rounded-xl">
                                        <Trophy className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Categories</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-green-100 rounded-xl">
                                        <Activity className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Categories</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-red-100 rounded-xl">
                                        <XCircle className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Inactive Categories</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.inactive}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Search and Filters */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="space-y-4">
                                <div className="flex flex-col md:flex-row gap-4">
                                    <div className="flex-1 relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            type="text"
                                            placeholder="Search categories..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                    <Select value={selectedStatus} onValueChange={(value) => {
                                        setSelectedStatus(value);
                                        handleFilterChange('status', value);
                                    }}>
                                        <SelectTrigger className="w-full md:w-48">
                                            <SelectValue placeholder="Filter by status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Select value={selectedOrg} onValueChange={(value) => {
                                        setSelectedOrg(value);
                                        handleFilterChange('organization_id', value);
                                    }}>
                                        <SelectTrigger className="w-full md:w-48">
                                            <SelectValue placeholder="Filter by organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Organizations</SelectItem>
                                            {organizations.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <Button type="submit" variant="outline">
                                        Search
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Categories List */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Categories List</CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">Category</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Organization</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Voting</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Nominees</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Price/Vote</th>
                                            <th className="text-right p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-100">
                                        {categories.data.map((category) => (
                                            <tr key={category.id} className="hover:bg-gray-50/50 transition-colors">
                                                <td className="p-4">
                                                    <div>
                                                        <h4 className="font-medium text-gray-900">{category.name}</h4>
                                                        <p className="text-sm text-gray-600 line-clamp-2">{category.description}</p>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Building2 className="h-3 w-3" />
                                                        {category.organization_name}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <Badge 
                                                        variant={category.is_active && !category.is_suspended ? 'default' : 'secondary'}
                                                        className={category.is_active && !category.is_suspended
                                                            ? 'bg-green-100 text-green-800 border-green-200' 
                                                            : 'bg-red-100 text-red-800 border-red-200'
                                                        }
                                                    >
                                                        {category.is_active && !category.is_suspended ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </td>
                                                <td className="p-4">
                                                    <Badge 
                                                        variant={category.voting_status === 'Active' ? 'default' : 'secondary'}
                                                        className={category.voting_status === 'Active'
                                                            ? 'bg-blue-100 text-blue-800 border-blue-200'
                                                            : 'bg-gray-100 text-gray-800 border-gray-200'
                                                        }
                                                    >
                                                        {category.voting_status}
                                                    </Badge>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Users className="h-3 w-3" />
                                                        {category.nominee_profiles_count}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <span className="text-sm font-medium text-gray-900">
                                                        ¢{category.price_per_vote.toFixed(2)}
                                                    </span>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex justify-end">
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/viewer/categories/${category.slug}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {categories.total === 0 && (
                                <div className="text-center py-12">
                                    <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
                                    <p className="text-gray-600">No categories match your search criteria.</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
