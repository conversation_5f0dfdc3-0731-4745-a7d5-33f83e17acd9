import { Head, <PERSON> } from '@inertiajs/react';
import AuthLayout from '@/layouts/auth-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, LogOut } from 'lucide-react';

export default function PendingApproval() {
    return (
        <AuthLayout title="Registration Pending Approval" description="Your organization registration is being reviewed.">
            <Head title="Pending Approval" />

            <Card className="border-0 shadow-sm w-full max-w-lg mx-auto">
                <CardHeader className="text-center p-6 sm:p-8">
                     <Clock className="h-12 w-12 sm:h-16 sm:w-16 text-yellow-500 mx-auto mb-4" />
                    <CardTitle className="text-xl sm:text-2xl font-bold">Review in Progress</CardTitle>
                    <CardDescription className="text-sm sm:text-base mt-2">
                        Thank you for registering your organization with VoteYourFav.
                        Your registration is currently under review by our administrators.
                    </CardDescription>
                </CardHeader>
                <CardContent className="text-center text-gray-600 p-6 sm:p-8 pt-0">
                    <div className="space-y-4 text-sm sm:text-base">
                        <p>We will notify you via email once your registration has been approved or if we require further information.</p>
                        <p className="font-medium text-gray-700">Thank you for your patience.</p>
                    </div>
                    
                    <div className="mt-8">
                        <Button 
                            variant="outline" 
                            asChild 
                            className="w-full sm:w-auto"
                        >
                            <Link
                                href={route('logout')}
                                method="post"
                                as="button"
                            >
                                <LogOut className="h-4 w-4 mr-2" />
                                Logout
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </AuthLayout>
    );
} 