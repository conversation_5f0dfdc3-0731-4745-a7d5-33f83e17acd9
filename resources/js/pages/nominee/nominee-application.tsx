import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Upload } from 'lucide-react';
import { FormE<PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AuthLayout from '@/layouts/auth-layout';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from 'axios';

interface Organization {
    id: number;
    name: string;
}

interface AwardCategory {
    id: number;
    name: string;
    organization_id: number;
}

type NominationForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    organization_id: string;
    award_category_id: string;
    profile_image: File | null;
    bio: string;
    achievements: string;
    why_vote_for_me: string;
    social_links: {
        instagram?: string;
        twitter?: string;
        facebook?: string;
    };
    phone: string;
    role: string;
};

export default function NominationApplication() {
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [categories, setCategories] = useState<AwardCategory[]>([]);
    const [filteredCategories, setFilteredCategories] = useState<AwardCategory[]>([]);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { data, setData, post, processing, errors, reset } = useForm<Required<NominationForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        organization_id: '',
        award_category_id: '',
        profile_image: null,
        bio: '',
        achievements: '',
        why_vote_for_me: '',
        social_links: {
            instagram: '',
            twitter: '',
            facebook: '',
        },
        phone: '',
        role: 'nominee', // Default role is nominee
    });

    // Fetch organizations when component mounts
    useEffect(() => {
        axios.get('/api/organizations')
            .then(response => {
                setOrganizations(response.data);
            })
            .catch(error => {
                // Error handling - organizations may not load properly
            });
    }, []);

    // Fetch categories when component mounts
    useEffect(() => {
        axios.get('/api/categories')
            .then(response => {
                setCategories(response.data);
            })
            .catch(error => {
                // Error handling - categories may not load properly
            });
    }, []);

    // Filter categories based on selected organization
    useEffect(() => {
        if (data.organization_id) {
            const orgId = parseInt(data.organization_id);
            const filtered = categories.filter(category => category.organization_id === orgId);
            setFilteredCategories(filtered);
            
            // Reset category selection if the current selection is not in the filtered list
            if (!filtered.find(cat => cat.id.toString() === data.award_category_id)) {
                setData('award_category_id', '');
            }
        } else {
            setFilteredCategories([]);
            setData('award_category_id', '');
        }
    }, [data.organization_id, categories]);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (file) {
            // Check file size (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB. Please choose a smaller image.');
                e.target.value = ''; // Clear the input
                return;
            }
            
            setData('profile_image', file);
            
            // Create preview URL
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        } else {
            setData('profile_image', null);
            setImagePreview(null);
        }
    };

    const handleSocialLinksChange = (platform: keyof typeof data.social_links, value: string) => {
        setData('social_links', {
            ...data.social_links,
            [platform]: value
        });
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
            forceFormData: true,
        });
    };

    return (
        <AuthLayout title="Nomination Application" description="Submit your details to apply as a nominee">
            <Head title="Apply for Nomination" />
            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-3">
                        <Label htmlFor="name" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Full Name
                        </Label>
                        <Input
                            id="name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="name"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing}
                            placeholder="Your full name as it should appear"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.name} className="mt-2" />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="email" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Email address
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="<EMAIL>"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="phone" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Phone Number
                        </Label>
                        <Input
                            id="phone"
                            type="tel"
                            required
                            tabIndex={3}
                            value={data.phone}
                            onChange={(e) => setData('phone', e.target.value)}
                            disabled={processing}
                            placeholder="+233 XX XXX XXXX"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.phone} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="organization_id" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Award Organization
                        </Label>
                        <Select 
                            value={data.organization_id} 
                            onValueChange={(value) => setData('organization_id', value)}
                            disabled={processing}
                        >
                            <SelectTrigger className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12">
                                <SelectValue placeholder="Select award organization" />
                            </SelectTrigger>
                            <SelectContent>
                                {organizations.map((org) => (
                                    <SelectItem key={org.id} value={org.id.toString()}>
                                        {org.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <InputError message={errors.organization_id} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="award_category_id" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Award Category
                        </Label>
                        <Select 
                            value={data.award_category_id} 
                            onValueChange={(value) => setData('award_category_id', value)}
                            disabled={processing || filteredCategories.length === 0}
                        >
                            <SelectTrigger className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12">
                                <SelectValue placeholder={filteredCategories.length === 0 ? "Select an organization first" : "Select category"} />
                            </SelectTrigger>
                            <SelectContent>
                                {filteredCategories.map((category) => (
                                    <SelectItem key={category.id} value={category.id.toString()}>
                                        {category.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <InputError message={errors.award_category_id} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="profile_image" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Profile Image (Required)
                        </Label>
                        <div className="flex items-center gap-4">
                            <div className="flex-1">
                                <label htmlFor="profile_image" className="cursor-pointer flex items-center justify-center border-2 border-dashed border-slate-300 bg-white/50 hover:bg-white/80 transition-all duration-300 rounded-xl h-12 px-4">
                                    <Upload className="w-4 h-4 mr-2 text-slate-500" />
                                    <span className="text-slate-600">{data.profile_image ? 'Change image' : 'Upload image'}</span>
                                </label>
                                <input
                                    id="profile_image"
                                    type="file"
                                    accept="image/*"
                                    required
                                    className="hidden"
                                    onChange={handleImageChange}
                                    disabled={processing}
                                />
                            </div>
                            {imagePreview && (
                                <div className="w-12 h-12 rounded-full overflow-hidden border border-slate-200">
                                    <img src={imagePreview} alt="Preview" className="w-full h-full object-cover" />
                                </div>
                            )}
                        </div>
                        <p className="text-xs text-slate-500">Upload a professional photo of yourself (max 5MB). This will be displayed on your nominee profile.</p>
                        <InputError message={errors.profile_image} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="bio" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Biography
                        </Label>
                        <Textarea
                            id="bio"
                            value={data.bio}
                            onChange={(e) => setData('bio', e.target.value)}
                            disabled={processing}
                            placeholder="Tell us about yourself, your background, and experience"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[120px]"
                        />
                        <InputError message={errors.bio} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="achievements" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Achievements
                        </Label>
                        <Textarea
                            id="achievements"
                            value={data.achievements}
                            onChange={(e) => setData('achievements', e.target.value)}
                            disabled={processing}
                            placeholder="List your notable achievements and accolades"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[100px]"
                        />
                        <InputError message={errors.achievements} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="why_vote_for_me" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Why Vote For Me
                        </Label>
                        <Textarea
                            id="why_vote_for_me"
                            required
                            value={data.why_vote_for_me}
                            onChange={(e) => setData('why_vote_for_me', e.target.value)}
                            disabled={processing}
                            placeholder="Explain why people should vote for you in this award category"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl min-h-[100px]"
                        />
                        <InputError message={errors.why_vote_for_me} />
                    </div>

                    <div className="grid gap-3">
                        <Label className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Social Media Links
                        </Label>
                        <div className="grid gap-3">
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-slate-500 w-24">Instagram:</span>
                                <Input
                                    type="text"
                                    value={data.social_links.instagram}
                                    onChange={(e) => handleSocialLinksChange('instagram', e.target.value)}
                                    disabled={processing}
                                    placeholder="@username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10"
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-slate-500 w-24">Twitter:</span>
                                <Input
                                    type="text"
                                    value={data.social_links.twitter}
                                    onChange={(e) => handleSocialLinksChange('twitter', e.target.value)}
                                    disabled={processing}
                                    placeholder="@username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10"
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-slate-500 w-24">Facebook:</span>
                                <Input
                                    type="text"
                                    value={data.social_links.facebook}
                                    onChange={(e) => handleSocialLinksChange('facebook', e.target.value)}
                                    disabled={processing}
                                    placeholder="Profile URL or username"
                                    className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-10"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Password
                        </Label>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            disabled={processing}
                            placeholder="Password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.password} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password_confirmation" className="text-slate-700 font-medium" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Confirm password
                        </Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            required
                            tabIndex={5}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            disabled={processing}
                            placeholder="Confirm password"
                            className="bg-white/50 border-white/50 focus:bg-white/80 focus:border-blue-300 transition-all duration-300 rounded-xl h-12"
                        />
                        <InputError message={errors.password_confirmation} />
                    </div>

                    <Button 
                        type="submit" 
                        className="mt-2 w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300" 
                        tabIndex={6} 
                        disabled={processing}
                        style={{ fontFamily: 'Urbanist, sans-serif' }}
                    >
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                        Submit Nomination Application
                    </Button>
                </div>

                <div className="text-center text-sm" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                    <span className="text-slate-600">Already have an account?</span>{' '}
                    <TextLink 
                        href={route('login')} 
                        tabIndex={7}
                        className="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    >
                        Log in
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
