import AuthenticatedLayout from '@/layouts/auth-layout';
import { Head, router } from '@inertiajs/react';
import { PageProps, User, NomineeProfileData, AwardCategoryData, OrganizationData } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ClipboardCopy, ExternalLink, UserCircle, Award, BarChart3, Check, Eye, TrendingUp } from 'lucide-react';
import { route } from 'ziggy-js';
import { useState } from 'react';
import { Toaster, toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';

interface NomineeDashboardProps extends PageProps {
    user: User;
    nomineeProfile: NomineeProfileData | null;
    awardCategory: AwardCategoryData | null;
    organization: OrganizationData | null;
}

// The `auth` prop here comes from Inertia's shared data, 
// and auth.user is the user object available globally in authenticated pages.
export default function NomineeDashboard({ auth, user: nomineeUserData , nomineeProfile, awardCategory, organization }: NomineeDashboardProps) {
    const [copySuccess, setCopySuccess] = useState(false);

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopySuccess(true);
            toast("Link copied!", {
                description: "Voting link has been copied to your clipboard.",
                duration: 3000,
            });
            setTimeout(() => setCopySuccess(false), 2000);
        });
    };

    const approvalStatusBadge = (status: string | undefined) => {
        if (!status) return <Badge variant="secondary" className="px-3 py-1 text-xs">Unknown</Badge>;
        switch (status.toLowerCase()) {
            case 'pending':
                return <Badge variant="default" className="bg-yellow-500 hover:bg-yellow-500 text-white px-3 py-1 text-xs">Pending Approval</Badge>;
            case 'approved':
                return <Badge variant="default" className="bg-green-500 hover:bg-green-500 text-white px-3 py-1 text-xs">Approved</Badge>;
            case 'rejected':
                return <Badge variant="destructive" className="px-3 py-1 text-xs">Rejected</Badge>;
            default:
                return <Badge variant="secondary" className="px-3 py-1 text-xs">{status}</Badge>;
        }
    };

    const profileApprovalStatusBadge = (isApproved: boolean | undefined) => {
        if (isApproved === undefined) return <Badge variant="secondary" className="px-3 py-1 text-xs">Unknown</Badge>;
        return isApproved ? 
            <Badge variant="default" className="bg-green-500 hover:bg-green-500 text-white px-3 py-1 text-xs flex items-center gap-1">
                <Check className="h-3 w-3" />
                Profile Active
            </Badge> :
            <Badge variant="default" className="bg-yellow-500 hover:bg-yellow-500 text-white px-3 py-1 text-xs">
                Profile Pending Review
            </Badge>;
    };

    const publicVotingLink = (nomineeProfile && nomineeProfile.slug) ? route('vote.show', nomineeProfile.slug) : '#';

    // Use nomineeUserData for displaying user-specific details for this nominee
    // auth.user is still available for layout-level user info if needed by AuthLayout

    return (
        <AppLayout>
            <Head title="Nominee Dashboard">
                <meta name="description" content="Manage your nomination profile, track voting progress, and monitor your performance in VoteYourFav awards. View profile statistics and engagement metrics." />
                
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Nominee Dashboard - VoteYourFav" />
                <meta property="og:description" content="Manage your nomination profile, track voting progress, and monitor your performance in VoteYourFav awards. View profile statistics and engagement metrics." />
                
                <meta property="twitter:card" content="summary" />
                <meta property="twitter:title" content="Nominee Dashboard - VoteYourFav" />
                <meta property="twitter:description" content="Manage your nomination profile, track voting progress, and monitor your performance in VoteYourFav awards. View profile statistics and engagement metrics." />
            </Head>
            <Toaster position="top-right" />

            <div className="py-4">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-4">
                    {/* Top Stats Cards */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Account Status</CardTitle>
                                <UserCircle className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate" title={nomineeUserData.name}>
                                    {nomineeUserData.name || 'N/A'}
                                </div>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2 truncate" title={nomineeUserData.email}>
                                    {nomineeUserData.email || 'No email provided'}
                                </p>
                                <div className="mt-1">
                                    {approvalStatusBadge(nomineeUserData.approval_status)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Profile Status</CardTitle>
                                <Award className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate" title={nomineeProfile?.display_name || ''}>
                                    {nomineeProfile?.display_name || 'Not set up yet'}
                                </div>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2 truncate" title={`${awardCategory?.name || 'No category'} at ${organization?.name || 'No organization'}`}>
                                    <span className="block sm:inline">{awardCategory?.name || 'No category'}</span>
                                    <span className="hidden sm:inline"> at </span>
                                    <span className="block sm:inline">{organization?.name || 'No organization'}</span>
                                </p>
                                <div className="mt-1">
                                    {profileApprovalStatusBadge(nomineeProfile?.is_approved)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Votes</CardTitle>
                                <BarChart3 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-xl font-bold text-gray-900 dark:text-white">
                                    {(nomineeProfile?.total_votes || 0).toLocaleString()}
                                </div>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current vote count for your profile</p>
                                <div className="h-4"></div> {/* Smaller spacer */}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Additional Analytics Cards */}
                    {nomineeProfile && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Profile Views</CardTitle>
                                    <Eye className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                                        {(nomineeProfile?.link_clicks || 0).toLocaleString()}
                                    </div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Total profile page visits</p>
                                </CardContent>
                            </Card>

                            <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Vote Rate</CardTitle>
                                    <TrendingUp className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                                        {nomineeProfile?.link_clicks && nomineeProfile.link_clicks > 0 
                                            ? `${Math.round((nomineeProfile.total_votes || 0) / nomineeProfile.link_clicks * 100)}%`
                                            : '0%'
                                        }
                                    </div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Conversion from views to votes</p>
                                </CardContent>
                            </Card>

                            <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Engagement</CardTitle>
                                    <BarChart3 className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                                        {((nomineeProfile?.total_votes || 0) + (nomineeProfile?.link_clicks || 0)).toLocaleString()}
                                    </div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Total interactions</p>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    {/* Profile Setup Reminder for users without profiles */}
                    {!nomineeProfile && (
                        <Card className="shadow-sm border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10 dark:border-yellow-800">
                            <CardHeader>
                                <CardTitle className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 flex items-center">
                                    <UserCircle className="h-5 w-5 mr-2" />
                                    Complete Your Nominee Profile
                                </CardTitle>
                                <CardDescription className="text-yellow-700 dark:text-yellow-300">
                                    To start receiving votes, you need to complete your nominee profile setup.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button 
                                    variant="default" 
                                    onClick={() => router.visit(route('register'))} 
                                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                                >
                                    Complete Profile Setup
                                </Button>
                            </CardContent>
                        </Card>
                    )}

                    {/* Public Voting Link Card */}
                    {nomineeProfile?.slug && (
                        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                            <CardHeader className="pb-3">
                                <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white">Your Public Voting Link</CardTitle>
                                <CardDescription className="text-sm text-gray-600 dark:text-gray-400">Share this link with your supporters to get votes!</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Input 
                                    type="text" 
                                    value={publicVotingLink} 
                                    readOnly 
                                    className="w-full bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded-md text-sm" 
                                />
                                <div className="flex flex-col sm:flex-row gap-2">
                                    <Button 
                                        variant={copySuccess ? "default" : "outline"} 
                                        size="default" 
                                        onClick={() => copyToClipboard(publicVotingLink)} 
                                        className={`flex-1 sm:flex-none transition-colors ${copySuccess ? "bg-green-500 hover:bg-green-600" : ""}`}
                                    >
                                        {copySuccess ? (
                                            <>
                                                <Check className="h-4 w-4 mr-2" />
                                                Copied!
                                            </>
                                        ) : (
                                            <>
                                                <ClipboardCopy className="h-4 w-4 mr-2" />
                                                Copy Link
                                            </>
                                        )}
                                    </Button>
                                    <a href={publicVotingLink} target="_blank" rel="noopener noreferrer" className="flex-1 sm:flex-none">
                                        <Button variant="outline" size="default" className="w-full">
                                            <ExternalLink className="h-4 w-4 mr-2" />
                                            View Page
                                        </Button>
                                    </a>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Profile Image and Basic Info Card */}
                    {nomineeProfile && (
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <div className="lg:col-span-1">
                                <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <CardHeader>
                                        <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white">Profile Image</CardTitle>
                                    </CardHeader>
                                    <CardContent className="flex flex-col items-center">
                                        <div className="w-full max-w-[200px] mx-auto">
                                            {nomineeProfile.profile_image ? (
                                                <img 
                                                    src={`/storage/${nomineeProfile.profile_image}`} 
                                                    alt="Profile" 
                                                    className="rounded-lg w-full h-auto aspect-square object-cover border border-gray-200 dark:border-gray-700 mb-4" 
                                                />
                                            ) : (
                                                <div className="rounded-lg w-full aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center border border-gray-200 dark:border-gray-700 mb-4">
                                                    <UserCircle className="h-16 w-16 text-gray-400" />
                                                </div>
                                            )}
                                        </div>
                                        <Button 
                                            variant="outline" 
                                            onClick={() => router.visit(route('profile.edit'))} 
                                            className="w-full max-w-[200px] hover:bg-gray-100 dark:hover:bg-gray-700"
                                        >
                                            Update Image
                                        </Button>
                                    </CardContent>
                                </Card>
                            </div>

                            <div className="lg:col-span-2">
                                <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 h-full">
                                    <CardHeader>
                                        <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white">Basic Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-4">
                                        {/* Voting Code - Only show for approved nominees */}
                                        {nomineeProfile.is_approved && nomineeProfile.slug && (
                                            <div className="sm:col-span-2 mb-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                                <h4 className="font-medium text-sm text-blue-700 dark:text-blue-300 flex items-center gap-2 mb-3">
                                                    <Award className="h-4 w-4" />
                                                    Your Voting Code
                                                </h4>
                                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                                    <div className="font-mono text-xl sm:text-2xl font-bold text-blue-900 dark:text-blue-100 bg-white dark:bg-gray-800 px-3 sm:px-4 py-2 rounded-lg border shadow-sm">
                                                        {nomineeProfile.nominee_code.toUpperCase()}
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                                                            Share this code with your supporters
                                                        </p>
                                                        <p className="text-xs text-blue-600 dark:text-blue-400 break-words">
                                                            Use with: <span className="font-mono break-all">/vote {nomineeProfile.nominee_code.toUpperCase()}</span> in Telegram
                                                        </p>
                                                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                            (Search for voteyourfav_bot on telegram)
                                                        </p>
                                                    </div>
                                                    <Button 
                                                        variant="outline" 
                                                        size="sm"
                                                        onClick={() => copyToClipboard(`/vote ${nomineeProfile.nominee_code.toUpperCase()}`)}
                                                        className="text-blue-700 border-blue-300 hover:bg-blue-100 dark:text-blue-300 dark:border-blue-600 dark:hover:bg-blue-900/30 w-full sm:w-auto mt-2 sm:mt-0"
                                                    >
                                                        <ClipboardCopy className="h-4 w-4 mr-1" />
                                                        <span className="sm:hidden">Copy</span>
                                                        <span className="hidden sm:inline">Copy Command</span>
                                                    </Button>
                                                </div>
                                            </div>
                                        )}
                                        
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400">Award Category</h4>
                                            <p className="text-gray-800 dark:text-white font-medium">{awardCategory?.name || 'Not specified'}</p>
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400">Organization</h4>
                                            <p className="text-gray-800 dark:text-white font-medium">{organization?.name || 'Not specified'}</p>
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400">Phone Number</h4>
                                            <p className="text-gray-800 dark:text-white">{nomineeUserData.phone || 'Not provided'}</p>
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400">Email Address</h4>
                                            <p className="text-gray-800 dark:text-white">{nomineeUserData.email || 'Not provided'}</p>
                                        </div>
                                        {nomineeProfile.social_links && typeof nomineeProfile.social_links === 'object' && Object.keys(nomineeProfile.social_links).length > 0 && (
                                            <div className="col-span-2">
                                                <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-2">Social Media</h4>
                                                <div className="space-y-2">
                                                    {Object.entries(nomineeProfile.social_links as Record<string, string>).map(([platform, link]) => 
                                                        link && (
                                                            <div key={platform} className="flex flex-col sm:flex-row sm:items-center text-sm">
                                                                <span className="capitalize font-medium text-gray-700 dark:text-gray-300 sm:w-20 flex-shrink-0">{platform}:</span> 
                                                                <a 
                                                                    href={link.startsWith('http') ? link : `https://${link}`} 
                                                                    target="_blank" 
                                                                    rel="noopener noreferrer" 
                                                                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline break-all mt-1 sm:mt-0 sm:ml-2"
                                                                >
                                                                    {link.replace(/^https?:\/\/(www\.)?/, '')}
                                                                </a>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    )}

                    {/* Nomination Details Card */}
                    <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg font-semibold text-gray-800 dark:text-white">Nomination Details</CardTitle>
                            <CardDescription className="text-sm text-gray-600 dark:text-gray-400">Review your submitted nomination information.</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">Biography</h4>
                                <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                                    <p className="whitespace-pre-wrap text-gray-800 dark:text-white">{nomineeProfile?.bio || 'Not provided'}</p>
                                </div>
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">Achievements</h4>
                                <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                                    <p className="whitespace-pre-wrap text-gray-800 dark:text-white">{nomineeProfile?.achievements || 'Not provided'}</p>
                                </div>
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">Why Vote For Me</h4>
                                <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                                    <p className="whitespace-pre-wrap text-gray-800 dark:text-white">{nomineeProfile?.why_vote_for_me || 'Not provided'}</p>
                                </div>
                            </div>
                            
                            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                                <Button 
                                    variant="default" 
                                    onClick={() => router.visit(route('profile.edit'))} 
                                    className="bg-blue-600 hover:bg-blue-700 text-white h-9"
                                >
                                    Edit Profile Information
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}