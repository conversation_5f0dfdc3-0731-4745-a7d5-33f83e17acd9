import { Head } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, XCircle, AlertCircle, Heart, Trophy, ArrowLeft, Share2, Home, Sparkles } from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';

interface Nominee {
    id: number;
    slug: string;
    display_name: string;
    profile_image?: string;
    total_votes: number;
    category: {
        name: string;
    };
}

interface Props {
    status: 'success' | 'failed' | 'error';
    message: string;
    reference: string | null;
    amount?: number;
    currency?: string;
    vote_count?: number;
    nominee?: Nominee | null;
}

export default function PaymentResult({ status, message, reference, amount, currency, vote_count, nominee }: Props) {
    const getStatusIcon = () => {
        switch (status) {
            case 'success':
                return <CheckCircle className="h-16 w-16 text-green-500" />;
            case 'failed':
                return <XCircle className="h-16 w-16 text-red-500" />;
            case 'error':
                return <AlertCircle className="h-16 w-16 text-yellow-500" />;
            default:
                return <AlertCircle className="h-16 w-16 text-slate-500" />;
        }
    };

    const getStatusColor = () => {
        switch (status) {
            case 'success':
                return 'text-green-600';
            case 'failed':
                return 'text-red-600';
            case 'error':
                return 'text-yellow-600';
            default:
                return 'text-slate-600';
        }
    };

    const getStatusTitle = () => {
        switch (status) {
            case 'success':
                return 'Thank you for voting!';
            case 'failed':
                return 'Payment Failed';
            case 'error':
                return 'Payment Error';
            default:
                return 'Payment Status';
        }
    };

    const handleShare = async () => {
        if (nominee && status === 'success') {
            const shareData = {
                title: `I just voted for ${nominee.display_name}!`,
                text: `I just cast ${vote_count} vote${vote_count !== 1 ? 's' : ''} for ${nominee.display_name} in the ${nominee.category.name} category. Join me in supporting them!`,
                url: `${window.location.origin}/vote/nominee/${nominee.slug}`,
            };

            try {
                if (navigator.share) {
                    await navigator.share(shareData);
                } else {
                    await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
                }
            } catch (error) {
                // Error sharing - fallback handled silently
            }
        }
    };

    return (
        <>
            <Head title={`Payment ${status === 'success' ? 'Successful' : 'Failed'} - VoteYourFav`}>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
                <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@300;400;500;600;700;800&family=Instrument+Serif:ital@1&display=swap" rel="stylesheet" />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                {/* Subtle background pattern */}
                <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>

                {/* Floating gradient orbs - more subtle */}
                <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-400/5 to-transparent rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-indigo-400/5 to-transparent rounded-full blur-3xl"></div>

                {/* Simplified Navigation */}
                <nav className="relative z-10 flex items-center justify-between p-6 lg:px-8">
                    <div className="flex items-center space-x-2">
                        <AppLogoIcon className="h-8 w-8 object-contain" />
                        <span className="text-xl font-bold text-slate-900">VoteYourFav</span>
                    </div>
                </nav>

                <div className="relative z-10 max-w-2xl mx-auto px-6 lg:px-8 py-16">
                    {/* Main Result Card - Cleaner Design */}
                    <Card className="bg-white/95 backdrop-blur-sm border-slate-200/50 shadow-xl overflow-hidden">
                        <CardHeader className="text-center pb-6">
                            {/* Status Icon - Simpler */}
                            <div className="flex justify-center mb-6">
                                    {getStatusIcon()}
                                </div>
                            
                            <CardTitle className={`text-3xl font-bold mb-3 ${getStatusColor()}`}>
                                    {getStatusTitle()}
                                </CardTitle>
                            
                            {/* Success celebration with nominee info */}
                            {status === 'success' && nominee && (
                                <div className="mt-6">
                                    <div className="flex items-center justify-center space-x-4 mb-4">
                                        <Avatar className="h-12 w-12 border-2 border-green-200">
                                            <AvatarImage src={nominee.profile_image} alt={nominee.display_name} />
                                            <AvatarFallback className="bg-gradient-to-br from-green-500 to-emerald-500 text-white">
                                                {nominee.display_name.charAt(0)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="text-left">
                                            <h3 className="font-bold text-slate-900">{nominee.display_name}</h3>
                                            <p className="text-sm text-slate-600">{nominee.category.name}</p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center justify-center space-x-6 text-sm text-slate-600">
                                        <div className="flex items-center space-x-1">
                                            <Heart className="h-4 w-4 text-red-500" />
                                            <span>{vote_count} vote{vote_count !== 1 ? 's' : ''} cast</span>
                                                </div>
                                        <div className="flex items-center space-x-1">
                                            <Trophy className="h-4 w-4 text-yellow-600" />
                                            <span>{nominee.total_votes.toLocaleString()} total</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            {/* Simplified Payment Details */}
                            {reference && amount && currency && (
                                <div className="bg-slate-50 p-4 rounded-xl border border-slate-200/50">
                                    <div className="flex justify-between items-center text-sm">
                                        <span className="text-slate-600">Amount Paid:</span>
                                        <span className="font-bold text-slate-900">{currency} {amount}</span>
                                    </div>
                                    <div className="flex justify-between items-center text-xs text-slate-500 mt-2">
                                        <span>Reference:</span>
                                        <span className="font-mono">{reference}</span>
                                        </div>
                                    </div>
                                )}

                            {/* Action Buttons - Simplified */}
                            <div className="space-y-3">
                                    {nominee && (
                                        <Button 
                                            asChild 
                                        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300"
                                        >
                                            <a href={`/vote/nominee/${nominee.slug}`}>
                                                <ArrowLeft className="h-4 w-4 mr-2" />
                                                Back to {nominee.display_name}
                                            </a>
                                        </Button>
                                    )}
                                    
                                <div className="grid grid-cols-2 gap-3">
                                    <Button 
                                        asChild 
                                        className="bg-white border-slate-200 text-slate-700 hover:bg-slate-50 hover:-translate-y-0.5 transition-all duration-300 rounded-xl font-medium"
                                        variant="outline"
                                    >
                                        <a href="/">
                                            <Home className="h-4 w-4 mr-2" />
                                            welcomepage
                                        </a>
                                    </Button>

                                    {status === 'success' && nominee && (
                                        <Button 
                                            onClick={handleShare}
                                            className="bg-green-600 hover:bg-green-700 text-white font-medium rounded-xl hover:-translate-y-0.5 transition-all duration-300"
                                        >
                                            <Share2 className="h-4 w-4 mr-2" />
                                            Share
                                        </Button>
                                    )}

                                    {status === 'failed' && nominee && (
                                        <Button 
                                            asChild 
                                            className="bg-red-600 hover:bg-red-700 text-white font-medium rounded-xl hover:-translate-y-0.5 transition-all duration-300"
                                        >
                                            <a href={`/vote/nominee/${nominee.slug}`}>
                                                Try Again
                                            </a>
                                        </Button>
                                    )}
                                </div>
                                </div>

                            {/* Simple Thank You Message */}
                                {status === 'success' && (
                                <div className="text-center text-slate-600 text-sm pt-4 border-t border-slate-200/50">
                                    <p>Thank you for your support! You'll receive an email confirmation shortly.</p>
                                    </div>
                                )}

                                {status === 'failed' && (
                                <div className="text-center text-red-600 text-sm pt-4 border-t border-slate-200/50">
                                        <p>If you continue to experience issues, please contact our support team.</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                </div>
            </div>
        </>
    );
} 