import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trophy, Users, Vote, TrendingUp, ExternalLink } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    // Sample data - in a real app, this would come from props
    const sampleNominees = [
        {
            slug: 'kwame-asante-techflow-abc123',
            display_name: 'Kwame Asante - TechFlow',
            category: 'Best Tech Startup',
            total_votes: 342,
            ranking: 1,
        },
        {
            slug: 'ama-osei-edutech-ghana-def456',
            display_name: '<PERSON>a <PERSON> - EduTech Ghana',
            category: 'Best Tech Startup',
            total_votes: 298,
            ranking: 2,
        },
        {
            slug: 'kweku-darko-k-dark-ghi789',
            display_name: 'Kweku Darko (K-Dark)',
            category: 'Artist of the Year',
            total_votes: 456,
            ranking: 1,
        },
        {
            slug: 'akosua-frimpong-healthtracker-jkl012',
            display_name: 'Akosua Frimpong - HealthTracker',
            category: 'Best Mobile App',
            total_votes: 189,
            ranking: 1,
        },
    ];

    const stats = [
        {
            title: 'Total Votes Cast',
            value: '2,847',
            icon: Vote,
            change: '+12%',
            changeType: 'positive' as const,
        },
        {
            title: 'Active Nominees',
            value: '12',
            icon: Users,
            change: '+3',
            changeType: 'positive' as const,
        },
        {
            title: 'Award Categories',
            value: '5',
            icon: Trophy,
            change: 'Stable',
            changeType: 'neutral' as const,
        },
        {
            title: 'Revenue Generated',
            value: 'GHS 5,694',
            icon: TrendingUp,
            change: '+18%',
            changeType: 'positive' as const,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard">
                <meta name="description" content="Access your VoteYourFav dashboard - view voting statistics, featured nominees, and participate in Ghana's premier voting platform for awards and recognition." />
                
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Dashboard - VoteYourFav" />
                <meta property="og:description" content="Access your VoteYourFav dashboard - view voting statistics, featured nominees, and participate in Ghana's premier voting platform for awards and recognition." />
                
                <meta property="twitter:card" content="summary" />
                <meta property="twitter:title" content="Dashboard - VoteYourFav" />
                <meta property="twitter:description" content="Access your VoteYourFav dashboard - view voting statistics, featured nominees, and participate in Ghana's premier voting platform for awards and recognition." />
            </Head>
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Welcome Section */}
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        Welcome to VoteYourFav
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                        Ghana's premier voting platform for awards and recognition
                    </p>
                </div>

                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {stats.map((stat) => (
                        <Card key={stat.title}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    {stat.title}
                                </CardTitle>
                                <stat.icon className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value}</div>
                                <p className={`text-xs ${
                                    stat.changeType === 'positive' 
                                        ? 'text-green-600' 
                                        : stat.changeType === 'negative' 
                                        ? 'text-red-600' 
                                        : 'text-gray-600'
                                }`}>
                                    {stat.change} from last month
                                </p>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Featured Nominees */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Trophy className="h-5 w-5" />
                            <span>Featured Nominees</span>
                        </CardTitle>
                        <CardDescription>
                            Top performing nominees across all categories
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            {sampleNominees.map((nominee) => (
                                <div key={nominee.slug} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="space-y-1">
                                        <h3 className="font-semibold">{nominee.display_name}</h3>
                                        <div className="flex items-center space-x-2">
                                            <Badge variant="secondary">{nominee.category}</Badge>
                                            <Badge variant="outline">Rank #{nominee.ranking}</Badge>
                                        </div>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">
                                            {nominee.total_votes} votes
                                        </p>
                                    </div>
                                    <Button variant="outline" size="sm" asChild>
                                        <a href={`/vote/nominee/${nominee.slug}`} target="_blank" rel="noopener noreferrer">
                                            <ExternalLink className="h-4 w-4 mr-2" />
                                            Vote
                                        </a>
                                    </Button>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>For Voters</CardTitle>
                            <CardDescription>
                                Support your favorite nominees by casting votes
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                Browse through our award categories and vote for the nominees you believe deserve recognition.
                            </p>
                            <div className="space-y-2">
                                <Button className="w-full" asChild>
                                    <a href="/vote/nominee/kwame-asante-techflow-abc123">
                                        Vote for Best Tech Startup
                                    </a>
                                </Button>
                                <Button variant="outline" className="w-full" asChild>
                                    <a href="/vote/nominee/kweku-darko-k-dark-ghi789">
                                        Vote for Artist of the Year
                                    </a>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>For Organizations</CardTitle>
                            <CardDescription>
                                Manage your awards and nominees
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                Create award categories, manage nominees, and track voting progress for your organization.
                            </p>
                            <div className="space-y-2">
                                <Button variant="outline" className="w-full">
                                    Create Award Category
                                </Button>
                                <Button variant="outline" className="w-full">
                                    Manage Nominees
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* System Info */}
                <Card>
                    <CardHeader>
                        <CardTitle>System Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div>
                                <h4 className="font-semibold mb-2">Active Organizations</h4>
                                <ul className="space-y-1 text-sm">
                                    <li>• Ghana Tech Awards</li>
                                    <li>• Ghana Music Awards</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Payment Integration</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-300">
                                    Secure payments powered by Paystack
                                </p>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Technology Stack</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-300">
                                    Laravel + React + Inertia.js
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
