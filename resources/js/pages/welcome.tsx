import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { <PERSON>R<PERSON>, CheckCircle, Star, Users, Trophy, /* Vote, */ Zap, Shield, BarChart3, Smartphone, MessageCircle, CreditCard, Globe, Phone, Wifi } from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';
// import { InteractiveBackground, WaveEffect, GeometricPattern } from '@/components/webgl';
import { route } from 'ziggy-js';

interface PopularCategory {
    id: number;
    name: string | null;
    organization_name: string | null;
    description: string | null;
    votes_count: number | null;
    price_per_vote: string | number | null;
    slug: string | null;
}

interface WelcomeProps {
    popularCategories?: PopularCategory[];
}

const categoryStyles = [
    { icon: "🚀", gradient: "from-purple-500 to-pink-500" },
    { icon: "🎵", gradient: "from-blue-500 to-cyan-500" },
    { icon: "📱", gradient: "from-green-500 to-emerald-500" },
    { icon: "💡", gradient: "from-orange-500 to-red-500" },
    { icon: "⭐", gradient: "from-indigo-500 to-purple-500" },
    { icon: "🤝", gradient: "from-teal-500 to-blue-500" },
];

export default function Welcome() {
    const { auth, popularCategories = [] } = usePage<SharedData & WelcomeProps>().props;

    return (
        <>
            <Head>
                <title>VoteYourFav | Online Voting & Award Management Platform</title>
                <meta name="description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly. Sign up free!" />
                <meta name="google-site-verification" content="HQ_bz1Gx35RDDCLtcqRpNZptlSAO355H0NAm1Unrac0" />
                
                <meta property="og:type" content="website" />
                <meta property="og:url" content="https://voteyourfav.com/" />
                <meta property="og:title" content="VoteYourFav | Online Voting & Award Management Platform" />
                <meta property="og:description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly. Sign up free!" />
                <meta property="og:image" content="/voteyourfav_logo.png" />

                <meta property="twitter:card" content="summary_large_image" />
                <meta property="twitter:url" content="https://voteyourfav.com/" />
                <meta property="twitter:title" content="VoteYourFav | Online Voting & Award Management Platform" />
                <meta property="twitter:description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly. Sign up free!" />
                <meta property="twitter:image" content="/voteyourfav_logo.png" />

                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" />
                <link href="https://fonts.googleapis.com/css2?family=Urbanist:wght@300;400;500;600;700;800&family=Instrument+Serif:ital@1&display=swap" rel="stylesheet" />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
                {/* Background Elements */}
                <div className="absolute inset-0 opacity-40">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>
                <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-400/20 to-transparent rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-indigo-400/20 to-transparent rounded-full blur-3xl"></div>

                {/* Navigation */}
                <nav className="relative z-10 flex items-center justify-between p-4 sm:p-6 lg:px-8">
                    <div className="flex items-center space-x-2">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center">
                            <AppLogoIcon className="size-full object-contain" />
                        </div>
                        <span className="text-lg sm:text-xl font-bold text-slate-900" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            VoteYourFav
                        </span>
                    </div>
                    
                    <div className="flex items-center space-x-3 sm:space-x-4">
                        <Link
                            href="/results"
                            className="px-4 py-2 sm:px-6 sm:py-2.5 text-slate-600 hover:text-slate-900 font-medium rounded-full hover:bg-white/60 hover:shadow-md transition-all duration-300 text-sm sm:text-base border border-transparent hover:border-white/30"
                        >
                            Results
                        </Link>
                        {auth.user ? (
                            <Link
                                href={route('dashboard')}
                                className="inline-flex items-center px-4 py-2 sm:px-6 sm:py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-semibold hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5 transition-all duration-300 text-sm sm:text-base"
                            >
                                <span className="hidden sm:inline">Dashboard</span>
                                <span className="sm:hidden">Go</span>
                                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2" />
                            </Link>
                        ) : (
                            <Link
                                href={route('login')}
                                className="inline-flex items-center px-4 py-2 sm:px-6 sm:py-2.5 bg-white/80 backdrop-blur-sm text-slate-700 rounded-full font-semibold border border-white/50 hover:bg-white hover:shadow-lg hover:shadow-slate-500/10 hover:-translate-y-0.5 transition-all duration-300 text-sm sm:text-base"
                            >
                                Sign In
                                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2" />
                            </Link>
                        )}
                    </div>
                    </nav>

                {/* Hero Section */}
                <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 sm:pt-16 lg:pt-20 pb-20 sm:pb-24 lg:pb-32">
                    {/* <InteractiveBackground className="opacity-60" particleCount={60} color="#6366f1" /> */}
                    <div className="text-center">
                        <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-white/60 backdrop-blur-sm rounded-full text-xs sm:text-sm font-medium text-blue-700 border border-blue-200/50 mb-6 sm:mb-8 hover:bg-white/80 transition-all duration-300">
                            <Phone className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                            Vote via USSD • Web • Telegram
                        </div>
                        
                        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold text-slate-900 mb-4 sm:mb-6 leading-tight px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Make Every{' '}
                            <span 
                                className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"
                                style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                            >
                                Vote
                            </span>{' '}
                            Count
                        </h1>
                        
                        <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-slate-600 mb-8 sm:mb-10 lg:mb-12 max-w-3xl mx-auto leading-relaxed px-4" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Vote from anywhere - your phone, web browser, or Telegram. No smartphone required! 
                            Just dial <a href="tel:*920*5070%23" className="font-bold text-blue-600 hover:text-blue-700 underline cursor-pointer">*920*5070#</a> to get started.
                        </p>
                        
                        <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 mb-12 sm:mb-14 lg:mb-16 px-4">
                            <Link
                                href={route('register')}
                                className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-semibold text-base sm:text-lg hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl hover:shadow-blue-500/25 hover:-translate-y-1 transition-all duration-300"
                            >
                                Start Voting Now
                                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                            </Link>
                            <a 
                                href="tel:*920*5070%23"
                                className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 bg-green-100 text-green-800 rounded-full font-semibold text-base sm:text-lg border border-green-200 hover:bg-green-200 transition-colors duration-300 cursor-pointer"
                            >
                                <Phone className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                                Dial *920*5070# to Vote
                            </a>
                        </div>

                        {/* Stats */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 max-w-lg mx-auto px-4">
                            <div className="text-center">
                                <div className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 sm:mb-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>10K+</div>
                                <div className="text-slate-600 text-sm sm:text-base">Active Voters</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1 sm:mb-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>99.9%</div>
                                <div className="text-slate-600 text-sm sm:text-base">Uptime</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Multiple Ways to Vote Section */}
                <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 sm:pb-24 lg:pb-32">
                    <div className="text-center mb-12 sm:mb-16 lg:mb-20">
                        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 mb-4 sm:mb-6 px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Vote Your{' '}
                            <span 
                                className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"
                                style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                            >
                                Way
                            </span>
                        </h2>
                        <p className="text-base sm:text-lg md:text-xl text-slate-600 max-w-2xl mx-auto px-4">
                            Three different ways to vote - choose what works best for you
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                        {/* USSD Voting */}
                        <div className="group p-8 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300 text-center">
                            <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <Phone className="w-8 h-8 text-white" />
                            </div>
                            <h3 className="text-xl font-semibold text-slate-900 mb-3" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                USSD Voting
                            </h3>
                            <a href="tel:*920*5070%23" className="text-3xl font-bold text-green-600 mb-3 block hover:text-green-700 cursor-pointer">*920*5070#</a>
                            <p className="text-slate-600 leading-relaxed mb-4">
                                Vote from any phone - no smartphone or internet required. Just dial <a href="tel:*920*5070%23" className="font-semibold text-green-600 hover:text-green-700 underline cursor-pointer">*920*5070#</a> and follow the prompts.
                            </p>
                            <div className="flex flex-wrap gap-2 justify-center">
                                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">MTN MoMo</span>
                                <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">Vodafone Cash</span>
                                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">AirtelTigo</span>
                            </div>
                        </div>

                        {/* Web Voting */}
                        <div className="group p-8 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300 text-center">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <Globe className="w-8 h-8 text-white" />
                            </div>
                            <h3 className="text-xl font-semibold text-slate-900 mb-3" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                Web Platform
                            </h3>
                            <div className="text-3xl font-bold text-blue-600 mb-3">🌐</div>
                            <p className="text-slate-600 leading-relaxed mb-4">
                                Full-featured web interface with real-time analytics, organization management, and detailed voting history.
                            </p>
                            <div className="flex flex-wrap gap-2 justify-center">
                                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Real-time Results</span>
                                <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">Analytics</span>
                            </div>
                        </div>

                        {/* Telegram Bot */}
                        <div className="group p-8 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300 text-center">
                            <div className="w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <MessageCircle className="w-8 h-8 text-white" />
                            </div>
                            <h3 className="text-xl font-semibold text-slate-900 mb-3" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                Telegram Bot
                            </h3>
                            <a href="https://t.me/voteyourfav_bot" target="_blank" rel="noopener noreferrer" className="text-3xl font-bold text-cyan-600 mb-3 block hover:text-cyan-700 cursor-pointer">@voteyourfav_bot</a>
                            <p className="text-slate-600 leading-relaxed mb-4">
                                Vote directly from Telegram with our intelligent bot. Search for <a href="https://t.me/voteyourfav_bot" target="_blank" rel="noopener noreferrer" className="font-semibold text-cyan-600 hover:text-cyan-700 underline cursor-pointer">@voteyourfav_bot</a> and start voting instantly.
                            </p>
                            <div className="flex flex-wrap gap-2 justify-center">
                                <span className="px-3 py-1 bg-cyan-100 text-cyan-800 rounded-full text-sm font-medium">Instant</span>
                                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Secure</span>
                            </div>
                        </div>
                    </div>

                    {/* How USSD Works */}
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-3xl p-8 sm:p-12 border border-green-100">
                        <div className="text-center mb-8">
                            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-4" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                How USSD Voting Works
                            </h3>
                            <p className="text-slate-600 text-lg">
                                Vote in 3 simple steps - no app download needed!
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">1</div>
                                <h4 className="font-semibold text-slate-900 mb-2">Dial *920*5070#</h4>
                                <p className="text-slate-600 text-sm">From any phone, dial <a href="tel:*920*5070%23" className="font-semibold text-green-600 hover:text-green-700 underline cursor-pointer">*920*5070#</a> to start voting</p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">2</div>
                                <h4 className="font-semibold text-slate-900 mb-2">Select & Vote</h4>
                                <p className="text-slate-600 text-sm">Choose your category and nominee from the menu</p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">3</div>
                                <h4 className="font-semibold text-slate-900 mb-2">Pay & Confirm</h4>
                                <p className="text-slate-600 text-sm">Pay via Mobile Money - vote recorded instantly</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Features Section */}
                <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 sm:pb-24 lg:pb-32">
                    <div className="text-center mb-12 sm:mb-16 lg:mb-20">
                        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 mb-4 sm:mb-6 px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Why Choose{' '}
                            <span 
                                className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"
                                style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                            >
                                VoteYourFav
                            </span>
                        </h2>
                        <p className="text-base sm:text-lg md:text-xl text-slate-600 max-w-2xl mx-auto px-4">
                            Built for modern organizations that value transparency and efficiency
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {[
                            {
                                icon: Phone,
                                title: "USSD Voting (*920*5070#)",
                                description: "Vote from any phone - no smartphone or internet required. Works on all networks in Ghana."
                            },
                            {
                                icon: MessageCircle,
                                title: "Telegram Bot (@voteyourfav_bot)",
                                description: "Vote directly through Telegram with our intelligent bot. Search for @voteyourfav_bot to get started."
                            },
                            {
                                icon: CreditCard,
                                title: "Local Payment Methods",
                                description: "Mobile Money payments supported - MTN MoMo, Vodafone Cash, and AirtelTigo Money."
                            },
                            {
                                icon: Smartphone,
                                title: "Multi-Platform Access",
                                description: "Vote via USSD, web browser, or Telegram - all synchronized in real-time."
                            },
                            {
                                icon: Shield,
                                title: "Secure & Transparent",
                                description: "End-to-end encryption with real-time vote tracking and comprehensive audit trails."
                            },
                            {
                                icon: BarChart3,
                                title: "Real-time Analytics",
                                description: "Live dashboards with detailed insights, vote counts, and geographical distribution."
                            }
                        ].map((feature, index) => (
                            <div 
                                key={index}
                                className="group p-8 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300"
                            >
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <feature.icon className="w-6 h-6 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-slate-900 mb-3" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                    {feature.title}
                                </h3>
                                <p className="text-slate-600 leading-relaxed">
                                    {feature.description}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* CTA Section */}
                <div className="relative z-10 w-full px-0 pb-20">
                    <div className="relative group">
                        {/* Animated background layers */}
                        <div className="absolute inset-0 bg-gradient-to-r from-slate-700 via-slate-600 to-slate-800 blur-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-700"></div>
                        <div className="absolute inset-0 bg-gradient-to-r from-slate-800 via-slate-700 to-slate-900 blur-3xl opacity-15 animate-pulse"></div>
                        <div className="absolute inset-0 bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 blur-xl opacity-10 group-hover:opacity-20 transition-opacity duration-500"></div>
                        
                        {/* Main CTA container */}
                        <div className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-black overflow-hidden group-hover:shadow-2xl group-hover:shadow-slate-500/25 transition-all duration-500">
                            {/* Animated mesh gradient overlay */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                            
                            {/* Floating geometric shapes */}
                            <div className="absolute top-0 right-0 w-40 h-40 bg-white/5 rounded-full -translate-y-20 translate-x-20 group-hover:scale-110 transition-transform duration-700"></div>
                            <div className="absolute top-1/4 right-1/3 w-24 h-24 bg-slate-400/10 rounded-full group-hover:translate-x-4 group-hover:-translate-y-2 transition-transform duration-500"></div>
                            <div className="absolute bottom-0 left-0 w-32 h-32 bg-slate-300/10 rounded-full translate-y-16 -translate-x-16 group-hover:scale-125 transition-transform duration-700"></div>
                            <div className="absolute bottom-1/3 left-1/4 w-16 h-16 bg-slate-500/10 rounded-full group-hover:-translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                            
                            {/* Animated pattern overlay */}
                            <div className="absolute inset-0 opacity-30" style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                                animation: 'float 6s ease-in-out infinite'
                            }}></div>
                            
                            {/* Content */}
                            <div className="relative px-4 py-8 sm:px-6 sm:py-10 lg:px-12 lg:py-16 text-center text-white">
                                {/* Animated badge */}
                                <div className="inline-flex items-center px-4 py-2 sm:px-6 sm:py-3 bg-white/20 backdrop-blur-sm rounded-full text-xs sm:text-sm font-semibold mb-6 sm:mb-8 border border-white/30 hover:bg-white/30 hover:scale-105 transition-all duration-300 group/badge">
                                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 sm:mr-3 animate-pulse"></div>
                                    <span className="hidden sm:inline">Join 100+ Organizations</span>
                                    <span className="sm:hidden">Join 10K+ Organizations</span>
                                    <div className="ml-2 sm:ml-3 bg-white/20 rounded-full px-2 py-1 text-xs">
                                        LIVE
                                    </div>
                                </div>
                                
                                {/* Main headline with enhanced typography */}
                                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-7xl font-bold mb-6 sm:mb-8 leading-tight relative px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                    <span className="relative z-10">
                                        Ready to Vote from{' '}
                                        <span 
                                            className="relative inline-block"
                                            style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                                        >
                                            <span className="relative z-10">Anywhere?</span>
                                            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-pink-400 text-transparent bg-clip-text opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                                Anywhere?
                                            </div>
                                            {/* Underline animation */}
                                            <div className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-cyan-400 to-pink-400 group-hover:w-full transition-all duration-700 rounded-full"></div>
                                        </span>
                                    </span>
                                </h2>
                                
                                {/* Enhanced description */}
                                <p className="text-base sm:text-lg md:text-xl lg:text-2xl mb-8 sm:mb-10 lg:mb-12 opacity-90 max-w-3xl mx-auto leading-relaxed px-4">
                                    Dial <a href="tel:*920*5070%23" className="font-bold text-white bg-green-600 px-2 py-1 rounded hover:bg-green-700 cursor-pointer">*920*5070#</a> from any phone or vote via web and Telegram.
                                    <span className="block mt-2 text-sm sm:text-base lg:text-lg opacity-75">
                                        No smartphone required - works on all networks in Ghana.
                                    </span>
                                </p>
                                
                                {/* CTA buttons with advanced styling */}
                                <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 mb-8 sm:mb-10 lg:mb-12 px-4">
                                    {/* Primary CTA */}
                                    <Link
                                        href={route('register')}
                                        className="w-full sm:w-auto group/cta relative inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 lg:px-10 lg:py-5 bg-white text-slate-900 rounded-full font-bold text-base sm:text-lg lg:text-xl hover:bg-gray-50 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden sm:min-w-[280px] justify-center"
                                    >
                                        {/* Button background effects */}
                                        <div className="absolute inset-0 bg-gradient-to-r from-white to-gray-50 opacity-0 group-hover/cta:opacity-100 transition-opacity duration-300"></div>
                                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-700"></div>
                                        
                                        {/* Button content */}
                                        <span className="relative z-10 mr-2 sm:mr-3">Get Started Today</span>
                                        <div className="relative z-10 w-5 h-5 sm:w-6 sm:h-6 bg-slate-900 rounded-full flex items-center justify-center group-hover/cta:bg-slate-800 group-hover/cta:scale-110 transition-all duration-300">
                                            <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 text-white group-hover/cta:translate-x-0.5 transition-transform duration-300" />
                                        </div>
                                        
                                        {/* Ripple effect */}
                                        <div className="absolute inset-0 rounded-full bg-slate-900/20 scale-0 group-hover/cta:scale-100 opacity-0 group-hover/cta:opacity-100 transition-all duration-500"></div>
                                    </Link>
                                    
                                    {/* Secondary CTA */}
                                    <Link
                                        href={route('vote.categories')}
                                        className="w-full sm:w-auto group/demo relative inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-white/10 backdrop-blur-sm text-white rounded-full font-semibold text-base sm:text-lg hover:bg-white/20 hover:shadow-lg transition-all duration-300 border border-white/30 hover:border-white/50 justify-center"
                                    >
                                        <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                                        <span>View All Categories</span>
                                    </Link>
                                </div>
                                
                                {/* Trust indicators with enhanced styling */}
                                <div className="flex flex-wrap items-center justify-center gap-8 text-white/80">
                                    {[
                                        { icon: "📱", text: "*920*5070# USSD" },
                                        { icon: "💰", text: "Mobile Money" },
                                        { icon: "⚡", text: "Instant Voting" }
                                    ].map((item, index) => (
                                        <div key={index} className="flex items-center gap-2 group/trust hover:text-white transition-colors duration-300">
                                            <span className="text-lg group-hover/trust:scale-110 transition-transform duration-300">{item.icon}</span>
                                            <span className="text-sm font-medium">{item.text}</span>
                                        </div>
                                    ))}
                                </div>
                                
                                {/* Social proof */}
                                <div className="mt-12 pt-8 border-t border-white/20">
                                    <div className="flex items-center justify-center gap-8 opacity-60 hover:opacity-80 transition-opacity duration-300">
                                        <div className="text-center">
                                            <div className="text-2xl font-bold">Ghana</div>
                                            <div className="text-xs">MTN • Vodafone • AirtelTigo</div>
                                        </div>
                                        <div className="w-px h-8 bg-white/30"></div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold">100+</div>
                                            <div className="text-xs">Organizations</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Add custom CSS for animations */}
                <style>
                    {`
                    @keyframes float {
                        0%, 100% { transform: translateY(0px) rotate(0deg); }
                        50% { transform: translateY(-10px) rotate(1deg); }
                    }
                    `}
                </style>

                {/* Footer */}
                <footer className="relative z-10 border-t border-white/20 bg-white/30 backdrop-blur-sm">
                    <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
                        <div className="flex flex-col md:flex-row items-center justify-between">
                            <div className="flex items-center space-x-2 mb-4 md:mb-0">
                                <div className="w-8 h-8 flex items-center justify-center">
                                    <AppLogoIcon className="size-full object-contain" />
                                </div>
                                <span className="text-xl font-bold text-slate-900" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                    VoteYourFav
                                </span>
                            </div>
                            <div className="text-slate-600">
                                © 2025 VoteTech. All rights reserved.
                            </div>
                        </div>
                </div>
                </footer>
            </div>
        </>
    );
}
