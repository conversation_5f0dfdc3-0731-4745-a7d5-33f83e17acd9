import { Head } from '@inertiajs/react';

import AppearanceTabs from '@/components/appearance-tabs';
import HeadingSmall from '@/components/heading-small';
import { type BreadcrumbItem } from '@/types';

import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Appearance settings',
        href: '/settings/appearance',
    },
];

export default function Appearance() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Appearance Settings">
                <meta name="description" content="Customize your VoteYourFav account appearance settings. Personalize themes, display preferences, and visual elements to enhance your voting experience and interface customization." />
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Appearance Settings - VoteYourFav" />
                <meta property="og:description" content="Personalize your VoteYourFav experience with customizable appearance settings. Adjust themes, visual preferences, and interface elements for optimal user experience." />
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:title" content="Appearance Settings - VoteYourFav" />
                <meta name="twitter:description" content="Customize your VoteYourFav account appearance with personalized themes and display preferences for enhanced user experience." />
            </Head>

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Appearance settings" description="Update your account's appearance settings" />
                    <AppearanceTabs />
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
