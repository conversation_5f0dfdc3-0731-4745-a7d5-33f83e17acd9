import { type BreadcrumbItem, type SharedData, type NomineeProfileData } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, Link, useForm, usePage, router } from '@inertiajs/react';
import React, { FormEventHandler, useState } from 'react';

import DeleteUser from '@/components/delete-user';
import HeadingSmall from '@/components/heading-small';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserCircle, Upload, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

type ProfileForm = {
    name: string;
    email: string;
    // Nominee profile fields
    profile_image?: File | null;
    display_name?: string;
    bio?: string;
    achievements?: string;
    why_vote_for_me?: string;
    social_links?: {
        facebook?: string;
        twitter?: string;
        instagram?: string;
        linkedin?: string;
        youtube?: string;
        tiktok?: string;
    };
}

export default function Profile({ 
    mustVerifyEmail, 
    status, 
    user,
    nomineeProfile
}: { 
    mustVerifyEmail: boolean; 
    status?: string;
    user: any;
    nomineeProfile: NomineeProfileData | null;
}) {
    const { auth } = usePage<SharedData>().props;
    const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
        nomineeProfile?.profile_image ? `/storage/${nomineeProfile.profile_image}` : null
    );

    const { data, setData, post, patch, errors, processing, recentlySuccessful, clearErrors } = useForm<ProfileForm>({
        name: auth.user.name,
        email: auth.user.email,
        profile_image: null,
        display_name: nomineeProfile?.display_name || '',
        bio: nomineeProfile?.bio || '',
        achievements: nomineeProfile?.achievements || '',
        why_vote_for_me: nomineeProfile?.why_vote_for_me || '',
        social_links: {
            facebook: nomineeProfile?.social_links?.facebook || '',
            twitter: nomineeProfile?.social_links?.twitter || '',
            instagram: nomineeProfile?.social_links?.instagram || '',
            linkedin: nomineeProfile?.social_links?.linkedin || '',
            youtube: nomineeProfile?.social_links?.youtube || '',
            tiktok: nomineeProfile?.social_links?.tiktok || '',
        },
    });

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('profile_image', file);
            // Create preview URL
            const reader = new FileReader();
            reader.onload = () => {
                setProfileImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
            clearErrors('profile_image');
        }
    };

    const removeImage = () => {
        setData('profile_image', null);
        setProfileImagePreview(nomineeProfile?.profile_image ? `/storage/${nomineeProfile.profile_image}` : null);
        // Clear the file input
        const fileInput = document.getElementById('profile_image') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        if (isNominee && data.profile_image instanceof File) {
            const formDataWithMethod = {
                ...data,
                _method: 'PATCH'
            };
            
            router.post(route('profile.update'), formDataWithMethod, {
                preserveScroll: true,
                forceFormData: true
            });
        } else {
            // Regular update without files
            router.patch(route('profile.update'), data, {
                preserveScroll: true
            });
        }
    };

    const isNominee = user?.role === 'nominee';

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Profile information" description="Update your name, email, and profile details" />

                    <form onSubmit={submit} className="space-y-6">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg sm:text-xl">Basic Information</CardTitle>
                                <CardDescription>Your account name and email address</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium">Name</Label>
                                    <Input
                                        id="name"
                                        className="w-full"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        required
                                        autoComplete="name"
                                        placeholder="Full name"
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-sm font-medium">Email address</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        className="w-full"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        required
                                        autoComplete="username"
                                        placeholder="Email address"
                                    />
                                    <InputError message={errors.email} />
                                </div>

                                {mustVerifyEmail && auth.user.email_verified_at === null && (
                                    <div>
                                        <p className="text-muted-foreground -mt-4 text-sm">
                                            Your email address is unverified.{' '}
                                            <Link
                                                href={route('verification.send')}
                                                method="post"
                                                as="button"
                                                className="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
                                            >
                                                Click here to resend the verification email.
                                            </Link>
                                        </p>

                                        {status === 'verification-link-sent' && (
                                            <div className="mt-2 text-sm font-medium text-green-600">
                                                A new verification link has been sent to your email address.
                                            </div>
                                        )}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Nominee Profile Information */}
                        {isNominee && (
                            <>
                                {/* Profile Picture */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg sm:text-xl">Profile Picture</CardTitle>
                                        <CardDescription>Upload a profile picture for your public nominee profile</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
                                            <div className="flex-shrink-0">
                                                <Avatar className="h-24 w-24 sm:h-32 sm:w-32">
                                                    {profileImagePreview ? (
                                                        <AvatarImage src={profileImagePreview} alt="Profile preview" />
                                                    ) : (
                                                        <AvatarFallback>
                                                            <UserCircle className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400" />
                                                        </AvatarFallback>
                                                    )}
                                                </Avatar>
                                            </div>
                                            
                                            <div className="flex-1 w-full space-y-3">
                                                <div className="flex flex-col sm:flex-row gap-2">
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        onClick={() => document.getElementById('profile_image')?.click()}
                                                        className="w-full sm:w-auto"
                                                    >
                                                        <Upload className="h-4 w-4 mr-2" />
                                                        Choose Image
                                                    </Button>
                                                    
                                                    {data.profile_image && (
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            onClick={removeImage}
                                                            className="w-full sm:w-auto"
                                                        >
                                                            <X className="h-4 w-4 mr-2" />
                                                            Remove
                                                        </Button>
                                                    )}
                                                </div>
                                                
                                                <input
                                                    id="profile_image"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleFileChange}
                                                    className="hidden"
                                                />
                                                
                                                <p className="text-sm text-muted-foreground">
                                                    Upload a JPG, PNG, or GIF file. Maximum size 5MB.
                                                </p>
                                                
                                                <InputError message={errors.profile_image} />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Nominee Profile Details */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg sm:text-xl">Nominee Profile</CardTitle>
                                        <CardDescription>Information displayed on your public voting page</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="display_name" className="text-sm font-medium">Display Name</Label>
                                            <Input
                                                id="display_name"
                                                value={data.display_name}
                                                onChange={(e) => setData('display_name', e.target.value)}
                                                placeholder="How you want to be displayed to voters"
                                                className="w-full"
                                            />
                                            <InputError message={errors.display_name} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="bio" className="text-sm font-medium">Biography</Label>
                                            <Textarea
                                                id="bio"
                                                value={data.bio}
                                                onChange={(e) => setData('bio', e.target.value)}
                                                placeholder="Tell voters about yourself..."
                                                rows={4}
                                                className="w-full resize-y"
                                            />
                                            <InputError message={errors.bio} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="achievements" className="text-sm font-medium">Achievements</Label>
                                            <Textarea
                                                id="achievements"
                                                value={data.achievements}
                                                onChange={(e) => setData('achievements', e.target.value)}
                                                placeholder="List your notable achievements..."
                                                rows={4}
                                                className="w-full resize-y"
                                            />
                                            <InputError message={errors.achievements} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="why_vote_for_me" className="text-sm font-medium">Why Vote For Me</Label>
                                            <Textarea
                                                id="why_vote_for_me"
                                                value={data.why_vote_for_me}
                                                onChange={(e) => setData('why_vote_for_me', e.target.value)}
                                                placeholder="Convince voters why they should vote for you..."
                                                rows={4}
                                                className="w-full resize-y"
                                            />
                                            <InputError message={errors.why_vote_for_me} />
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Social Media Links */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg sm:text-xl">Social Media Links</CardTitle>
                                        <CardDescription>Connect your social media profiles (optional)</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            {[
                                                { key: 'facebook', label: 'Facebook' },
                                                { key: 'twitter', label: 'Twitter' },
                                                { key: 'instagram', label: 'Instagram' },
                                                { key: 'linkedin', label: 'LinkedIn' },
                                                { key: 'youtube', label: 'YouTube' },
                                                { key: 'tiktok', label: 'TikTok' },
                                            ].map((social) => (
                                                <div key={social.key} className="space-y-2">
                                                    <Label htmlFor={social.key} className="text-sm font-medium">{social.label}</Label>
                                                    <Input
                                                        id={social.key}
                                                        value={data.social_links?.[social.key as keyof typeof data.social_links] || ''}
                                                        onChange={(e) => setData('social_links', {
                                                            ...data.social_links,
                                                            [social.key]: e.target.value
                                                        })}
                                                        placeholder={`Your ${social.label} profile URL`}
                                                        className="w-full"
                                                    />
                                                    <InputError message={errors[`social_links.${social.key}` as keyof typeof errors]} />
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </>
                        )}

                        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                            <Button disabled={processing} className="w-full sm:w-auto">
                                {processing ? 'Saving...' : 'Save Changes'}
                            </Button>

                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-green-600 text-center sm:text-left">Saved successfully!</p>
                            </Transition>
                        </div>
                    </form>
                </div>

                <DeleteUser />
            </SettingsLayout>
        </AppLayout>
    );
}
