# Admin Dashboard Pages

This directory contains all the admin dashboard pages for the voteyourfav application. Each page follows a consistent UI design pattern and includes proper TypeScript interfaces.

## Directory Structure

```
admin/
├── dashboard.tsx                 # Main dashboard
├── analytics/
│   └── index.tsx                # Analytics & reports
├── users/
│   ├── index.tsx                # User listing
│   └── create.tsx               # Create user form
├── organizations/
│   └── index.tsx                # Organization management
├── categories/
│   └── index.tsx                # Award categories
├── payments/
│   └── index.tsx                # Payment transactions
├── settings/
│   └── index.tsx                # System settings
├── index.ts                     # Export file
└── README.md                    # This file
```

## Pages Overview

### 1. Dashboard (`dashboard.tsx`)
- **Route**: `/admin`
- **Description**: Main admin dashboard with overview statistics, quick actions, and system health
- **Features**: 
  - Overview stats cards
  - Quick action buttons
  - Recent activities
  - Top performers
  - System health monitoring

### 2. Analytics (`analytics/index.tsx`)
- **Route**: `/admin/analytics`
- **Description**: Comprehensive analytics and reporting page
- **Features**:
  - Performance metrics
  - Revenue and voting trends
  - Top categories, nominees, and organizations
  - Chart placeholders for data visualization

### 3. Users (`users/index.tsx`)
- **Route**: `/admin/users`
- **Description**: User management interface
- **Features**:
  - User listing with search and filters
  - User status management (active, suspended, pending)
  - Role-based access control
  - Pagination support

### 4. Create User (`users/create.tsx`)
- **Route**: `/admin/users/create`
- **Description**: Form for creating new users
- **Features**:
  - Comprehensive user creation form
  - Role assignment
  - Organization assignment
  - Email verification options

### 5. Organizations (`organizations/index.tsx`)
- **Route**: `/admin/organizations`
- **Description**: Organization management interface
- **Features**:
  - Organization listing and search
  - Status management
  - Performance metrics per organization
  - Admin assignment tracking

### 6. Categories (`categories/index.tsx`)
- **Route**: `/admin/categories`
- **Description**: Award category management
- **Features**:
  - Category listing and management
  - Voting settings configuration
  - Performance tracking
  - Status management

### 7. Payments (`payments/index.tsx`)
- **Route**: `/admin/payments`
- **Description**: Payment transaction monitoring
- **Features**:
  - Payment transaction listing
  - Status tracking (completed, pending, failed, refunded)
  - Revenue analytics
  - Transaction details

### 8. Settings (`settings/index.tsx`)
- **Route**: `/admin/settings`
- **Description**: System configuration and settings
- **Features**:
  - General site settings
  - Feature toggles
  - Payment configuration
  - System health monitoring
  - Backup management

## Design Patterns

All admin pages follow these consistent patterns:

### UI Components
- Uses shadcn/ui components for consistency
- Card-based layout with proper spacing
- Consistent header sections with actions
- Responsive design with mobile support

### Layout Structure
```tsx
<AppLayout breadcrumbs={breadcrumbs}>
  <Head title="Page Title - Admin Dashboard" />
  <div className="min-h-screen bg-gray-50/50">
    <div className="space-y-8 p-6 md:p-8">
      {/* Header Section */}
      {/* Search/Filters */}
      {/* Stats Cards */}
      {/* Main Content */}
      {/* Pagination */}
    </div>
  </div>
</AppLayout>
```

### TypeScript Interfaces
- Proper type definitions for all props
- Consistent naming conventions
- Interface segregation for better maintainability

### State Management
- Uses Inertia.js forms for form handling
- Local state for UI interactions
- Proper error handling and validation

## Navigation

The admin pages are accessible through the quick actions in the main dashboard:

- **Add New User** → `/admin/users/create`
- **New Organization** → `/admin/organizations/create`
- **Create Category** → `/admin/categories/create`
- **View Reports** → `/admin/analytics`
- **System Settings** → `/admin/settings`
- **Database Backup** → `/admin/settings/backup`

## Permissions

Pages respect user roles:
- **Super Admin**: Access to all pages and features
- **Admin**: Limited to organization-specific data and features
- **Nominee**: No access to admin pages

## Future Enhancements

- Add real chart implementations (Chart.js, Recharts, etc.)
- Implement advanced filtering and sorting
- Add bulk actions for user and organization management
- Enhance real-time updates for system health
- Add export functionality for reports 