import React, { useState } from 'react';
import { Head, useForm, usePage, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  UserPlus, 
  UserMinus, 
  Search, 
  Building2, 
  Mail,
  Phone,
  Calendar,
  Filter
} from 'lucide-react';
import { type BreadcrumbItem, type SharedData } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  organization?: Organization;
}

interface NomineeProfile {
  id: number;
  display_name: string;
  profile_image?: string;
  bio?: string;
  nominee_code?: string;
  user: User;
  created_at: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  organization: Organization;
}

interface ManageNomineesProps {
  category: Category;
  assignedNominees: NomineeProfile[];
  availableNominees: NomineeProfile[];
  organizations?: Organization[];
  filters: {
    organization_id?: string;
  };
}

export default function ManageNominees({ 
  category, 
  assignedNominees, 
  availableNominees, 
  organizations,
  filters 
}: ManageNomineesProps) {
  const { auth } = usePage<SharedData>().props;
  const [searchTerm, setSearchTerm] = useState('');
  const [processing, setProcessing] = useState(false);
  
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Categories', href: '/admin/categories' },
    { title: category.name, href: `/admin/categories/${category.slug}` },
    { title: 'Manage Nominees', href: `/admin/categories/${category.slug}/nominees` },
  ];

  const { delete: destroy } = useForm();

  const assignNominee = (nomineeProfileId: number) => {
    if (processing) return;
    
    setProcessing(true);
    router.post(route('admin.categories.assign-nominee', category.slug), {
      nominee_profile_id: nomineeProfileId,
    }, {
      preserveScroll: true,
      onFinish: () => setProcessing(false),
      onSuccess: () => {
        // The page will be refreshed automatically by Inertia
      },
      onError: (errors) => {
        // Assignment error handling
      }
    });
  };

  const removeNominee = (nomineeProfileId: number) => {
    if (confirm('Are you sure you want to remove this nominee from the category?')) {
      destroy(route('admin.categories.remove-nominee', [category.slug, nomineeProfileId]), {
        preserveScroll: true,
      });
    }
  };

  const filteredAvailableNominees = availableNominees.filter(nominee =>
    nominee.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    nominee.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    nominee.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAssignedNominees = assignedNominees.filter(nominee =>
    nominee.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    nominee.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    nominee.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Manage Nominees - ${category.name}`} />
      
      <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Manage Nominees</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Assign and manage nominees for <span className="font-medium">{category.name}</span>
            </p>
          </div>
          <Button variant="outline" asChild className="w-full sm:w-auto">
            <a href={route('admin.categories.show', category.slug)}>
              Back to Category
            </a>
          </Button>
        </div>

        {/* Category Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Building2 className="h-4 w-4 sm:h-5 sm:w-5" />
              Category Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              <div>
                <Label className="text-xs sm:text-sm font-medium text-gray-600">Category</Label>
                <p className="text-sm sm:text-base text-gray-900 truncate">{category.name}</p>
              </div>
              <div>
                <Label className="text-xs sm:text-sm font-medium text-gray-600">Organization</Label>
                <p className="text-sm sm:text-base text-gray-900 truncate">{category.organization.name}</p>
              </div>
              <div>
                <Label className="text-xs sm:text-sm font-medium text-gray-600">Total Assigned</Label>
                <p className="text-sm sm:text-base text-gray-900">{assignedNominees.length} nominees</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Filter className="h-4 w-4 sm:h-5 sm:w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              <div className="sm:col-span-2 lg:col-span-1">
                <Label htmlFor="search" className="text-xs sm:text-sm">Search Nominees</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-sm"
                  />
                </div>
              </div>
              
              {auth.user.role === 'super_admin' && organizations && (
                <div>
                  <Label htmlFor="organization_filter" className="text-xs sm:text-sm">Filter by Organization</Label>
                  <Select 
                    value={filters.organization_id || 'all'} 
                    onValueChange={(value) => {
                      const url = new URL(window.location.href);
                      if (value === 'all') {
                        url.searchParams.delete('organization_id');
                      } else {
                        url.searchParams.set('organization_id', value);
                      }
                      window.location.href = url.toString();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Organizations" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Organizations</SelectItem>
                      {organizations.map((org) => (
                        <SelectItem key={org.id} value={org.id.toString()}>
                          {org.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
          {/* Available Nominees */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <UserPlus className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                Available Nominees ({filteredAvailableNominees.length})
              </CardTitle>
              <CardDescription className="text-sm">
                Nominees that can be assigned to this category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredAvailableNominees.length > 0 ? (
                <div className="space-y-3 sm:space-y-4 max-h-96 overflow-y-auto">
                  {filteredAvailableNominees.map((nominee) => (
                    <div key={nominee.id} className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 p-3 sm:p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 sm:space-x-4">
                        <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                          <AvatarImage src={nominee.profile_image} />
                          <AvatarFallback className="text-xs sm:text-sm">
                            {nominee.display_name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm sm:text-base font-medium truncate">{nominee.display_name}</p>
                          {nominee.nominee_code && (
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 font-medium">
                              <span className="px-2 py-1 bg-blue-100 rounded text-xs font-mono">{nominee.nominee_code}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
                            <Mail className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">{nominee.user.email}</span>
                          </div>
                          {nominee.user.organization && (
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
                              <Building2 className="h-3 w-3 flex-shrink-0" />
                              <span className="truncate">{nominee.user.organization.name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => assignNominee(nominee.id)}
                        disabled={processing}
                        className="w-full sm:w-auto text-xs sm:text-sm"
                      >
                        <UserPlus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Assign
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <Users className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
                  <p className="text-sm sm:text-base text-gray-500">
                    {searchTerm ? 'No nominees found matching your search' : 'No available nominees'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Assigned Nominees */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Assigned Nominees ({filteredAssignedNominees.length})
              </CardTitle>
              <CardDescription className="text-sm">
                Nominees currently assigned to this category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredAssignedNominees.length > 0 ? (
                <div className="space-y-3 sm:space-y-4 max-h-96 overflow-y-auto">
                  {filteredAssignedNominees.map((nominee) => (
                    <div key={nominee.id} className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 p-3 sm:p-4 border rounded-lg bg-blue-50">
                      <div className="flex items-center space-x-3 sm:space-x-4">
                        <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                          <AvatarImage src={nominee.profile_image} />
                          <AvatarFallback className="text-xs sm:text-sm">
                            {nominee.display_name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm sm:text-base font-medium truncate">{nominee.display_name}</p>
                          {nominee.nominee_code && (
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 font-medium">
                              <span className="px-2 py-1 bg-blue-100 rounded text-xs font-mono">{nominee.nominee_code}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
                            <Mail className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">{nominee.user.email}</span>
                          </div>
                          {nominee.user.organization && (
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
                              <Building2 className="h-3 w-3 flex-shrink-0" />
                              <span className="truncate">{nominee.user.organization.name}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
                            <Calendar className="h-3 w-3 flex-shrink-0" />
                            Assigned {new Date(nominee.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeNominee(nominee.id)}
                        disabled={processing}
                        className="w-full sm:w-auto text-xs sm:text-sm"
                      >
                        <UserMinus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <Users className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
                  <p className="text-sm sm:text-base text-gray-500">
                    {searchTerm ? 'No assigned nominees found matching your search' : 'No nominees assigned yet'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
} 