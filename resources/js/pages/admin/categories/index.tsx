import React, { useState } from 'react';
import { Head, router, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Award, 
    Plus,
    Search,
    Filter,
    Users,
    Vote,
  
    Calendar,
  Building2,
  Eye,
  Edit,
  MoreHorizontal,
  TrendingUp,
  Activity,
  Star,
  UserCog
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem, type SharedData } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface Category {
    id: number;
    name: string;
  slug: string;
  description: string;
  price_per_vote: string | number;
  discount_percentage: string | number;
  discount_min_votes: number;
    voting_start_date?: string;
    voting_end_date?: string;
  is_active: boolean;
  is_suspended: boolean;
  created_at: string;
  organization: Organization;
  voting_status: string;
  nominee_profiles_count: number;
  votes_count: number;
}

interface PaginationLink {
  url?: string;
  label: string;
  active: boolean;
}

interface PaginatedCategories {
        data: Category[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
  links: PaginationLink[];
}

interface Filters {
        search?: string;
  organization_id?: string;
        status?: string;
}

interface CategoriesProps {
  categories: PaginatedCategories;
  organizations?: Organization[];
  filters: Filters;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Categories', href: '/admin/categories' },
];

export default function Categories({ categories, organizations, filters }: CategoriesProps) {
  const { auth } = usePage<SharedData>().props;
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [selectedOrg, setSelectedOrg] = useState<string | null>(filters.organization_id || null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(filters.status || null);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
    router.get(route('admin.categories.index'), {
      search: searchTerm,
      organization_id: selectedOrg,
      status: selectedStatus,
    }, {
      preserveState: true,
      replace: true,
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    router.get(route('admin.categories.index'), {
      ...filters,
      [key]: value,
    }, {
      preserveState: true,
      replace: true,
        });
    };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg shadow-green-500/25';
      case 'Not Started':
        return 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/25';
      case 'Ended':
        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white shadow-lg shadow-gray-500/25';
      case 'Suspended':
        return 'bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-lg shadow-red-500/25';
      case 'Inactive':
        return 'bg-gradient-to-r from-yellow-500 to-amber-500 text-white shadow-lg shadow-yellow-500/25';
      default:
        return 'bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-lg shadow-gray-400/25';
    }
  };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Award Categories" />
      
      <div className="p-6">
        {/* Header with gradient background */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 p-8 text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex flex-col sm:flex-row items-center justify-between gap-6 sm:gap-0">
            <div className="text-center sm:text-left">
              <h1 className="text-4xl font-bold tracking-tight mb-2">Award Categories</h1>
              <p className="text-blue-100 text-lg">
                Manage award categories and their settings
              </p>
              <div className="flex items-center gap-6 mt-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-blue-100">{categories.total} Total Categories</span>
                </div>
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-200" />
                  <span className="text-sm text-blue-100">Live Tracking</span>
                </div>
              </div>
            </div>
            <Button 
              asChild 
              className="bg-white text-purple-600 hover:bg-gray-50 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 w-full sm:w-auto"
            >
              <Link href={route('admin.categories.create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Category
              </Link>
            </Button>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
        </div>

        {/* Enhanced Filters */}
        <Card className="border-0 mt-10">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                <Filter className="h-5 w-5 text-white" />
              </div>
              Smart Filters
            </CardTitle>
            <CardDescription className="text-gray-600">
              Find categories quickly with advanced filtering options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row items-stretch gap-4">
                                <div className="flex-1 min-w-0">
                                    <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                    placeholder="Search categories by name, description..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 w-full"
                                        />
                                    </div>
                                </div>
              
              {organizations && (
                <Select value={selectedOrg || "all"} onValueChange={(value) => {
                  const newValue = value === "all" ? "" : value;
                  setSelectedOrg(newValue);
                  handleFilterChange('organization_id', newValue);
                }}>
                  <SelectTrigger className="w-full md:w-56 border-gray-200 focus:border-purple-500">
                    <SelectValue placeholder="All Organizations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-gray-500" />
                        All Organizations
                      </div>
                    </SelectItem>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-blue-500" />
                          {org.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              <Select value={selectedStatus || "all"} onValueChange={(value) => {
                const newValue = value === "all" ? "" : value;
                setSelectedStatus(newValue);
                handleFilterChange('status', newValue);
              }}>
                <SelectTrigger className="w-full md:w-48 border-gray-200 focus:border-purple-500">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      All Status
                    </div>
                  </SelectItem>
                  <SelectItem value="active">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Active
                    </div>
                  </SelectItem>
                  <SelectItem value="inactive">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      Inactive
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              <Button 
                type="submit" 
                className="w-full md:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 hover:shadow-xl transition-all duration-300"
              >
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
                            </form>
                        </CardContent>
                    </Card>

        {/* Categories List */}
        <Card className="border-0 mt-10">
          <CardHeader className="border-0">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
                    <Award className="h-6 w-6 text-white" />
                                </div>
                  Categories ({categories.total})
                </CardTitle>
                <CardDescription className="text-gray-600 mt-1">
                  {categories.total > 0 
                    ? `Showing ${categories.data.length} of ${categories.total} categories`
                    : 'No categories found'
                  }
                </CardDescription>
                                </div>
              {categories.total > 0 && (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <TrendingUp className="h-4 w-4" />
                  <span>Performance tracking active</span>
                                </div>
              )}
                    </div>
                        </CardHeader>
                        <CardContent className="p-0">
            {categories.data.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {categories.data.map((category, index) => (
                  <div key={category.id} className="p-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 group">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-3">
                                                    <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                              {index + 1}
                                                        </div>
                                                        <div>
                              <h3 className="text-xl font-bold text-gray-900 group-hover:text-purple-700 transition-colors">
                                {category.name}
                              </h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge className={`${getStatusColor(category.voting_status)} px-3 py-1 text-xs font-semibold`}>
                                  {category.voting_status}
                                </Badge>
                                {category.is_suspended && (
                                  <Badge className="bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg shadow-red-500/25 px-3 py-1">
                                    <span className="flex items-center gap-1">
                                      <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                      Suspended
                                    </span>
                                  </Badge>
                                                            )}
                                                        </div>
                                                    </div>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-6 line-clamp-2 leading-relaxed">{category.description}</p>
                        
                        {/* Enhanced Stats Grid */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-3 sm:p-4 border border-green-100">
                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3">
                              <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex-shrink-0">
                                <CediSign className="h-4 w-4 text-white" />
                              </div>
                              <div className="min-w-0">
                                <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Price/Vote</p>
                                <p className="text-md sm:text-lg font-bold text-green-700 truncate">GHS {Number(category.price_per_vote).toFixed(2)}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-3 sm:p-4 border border-blue-100">
                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3">
                              <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex-shrink-0">
                                <Users className="h-4 w-4 text-white" />
                              </div>
                              <div className="min-w-0">
                                <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Nominees</p>
                                <p className="text-md sm:text-lg font-bold text-blue-700 truncate">{category.nominee_profiles_count}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-3 sm:p-4 border border-purple-100">
                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3">
                              <div className="p-2 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg flex-shrink-0">
                                <Vote className="h-4 w-4 text-white" />
                              </div>
                              <div className="min-w-0">
                                <p className="text-xs font-medium text-purple-600 uppercase tracking-wide">Total Votes</p>
                                <p className="text-md sm:text-lg font-bold text-purple-700 truncate">{category.votes_count.toLocaleString()}</p>
                              </div>
                                                    </div>
                                                    </div>
                          
                          <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-3 sm:p-4 border border-orange-100">
                            <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3">
                              <div className="p-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-lg flex-shrink-0">
                                <Calendar className="h-4 w-4 text-white" />
                                                    </div>
                              <div className="min-w-0">
                                <p className="text-xs font-medium text-orange-600 uppercase tracking-wide">Created</p>
                                <p className="text-sm font-semibold text-orange-700 truncate">{new Date(category.created_at).toLocaleDateString()}</p>
                                                        </div>
                                                        </div>
                                                        </div>
                                                    </div>

                        {/* Organization and Admin Info */}
                        <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-6 text-sm">
                          <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg w-full xs:w-auto">
                            <Building2 className="h-4 w-4 text-gray-500 flex-shrink-0" />
                            <span className="font-medium text-gray-700 truncate" title={category.organization.name}>{category.organization.name}</span>
                          </div>
                          {category.admin && (
                            <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg w-full xs:w-auto">
                              <Star className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              <span className="font-medium text-blue-700 truncate" title={`Admin: ${category.admin.name}`}>Admin: {category.admin.name}</span>
                            </div>
                          )}
                        </div>
                                                    </div>

                      {/* Enhanced Action Menu */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                            className="group-hover:opacity-100 transition-all duration-300 hover:bg-purple-100 hover:text-purple-700"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem asChild>
                            <Link href={route('admin.categories.show', category.slug)} className="flex items-center gap-2">
                              <Eye className="h-4 w-4 text-blue-500" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={route('admin.categories.edit', category.slug)} className="flex items-center gap-2">
                              <Edit className="h-4 w-4 text-purple-500" />
                              Edit Category
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={route('admin.categories.nominees', category.slug)} className="flex items-center gap-2">
                              <UserCog className="h-4 w-4 text-green-500" />
                              Manage Nominees
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                                                    </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Award className="h-12 w-12 text-purple-500" />
                            </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No categories found</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  {filters.search || filters.organization_id || filters.status
                    ? 'Try adjusting your search filters to find what you\'re looking for'
                    : 'Get started by creating your first award category to begin managing nominations and voting'
                  }
                </p>
                <Button 
                  asChild 
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Link href={route('admin.categories.create')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Category
                                                </Link>
                                            </Button>
              </div>
            )}
                            </CardContent>
                        </Card>

        {/* Enhanced Pagination */}
        {categories.last_page > 1 && (
          <div className="flex items-center justify-between bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="text-sm text-gray-600 font-medium">
              Showing <span className="font-bold text-purple-600">{((categories.current_page - 1) * categories.per_page) + 1}</span> to{' '}
              <span className="font-bold text-purple-600">{Math.min(categories.current_page * categories.per_page, categories.total)}</span> of{' '}
              <span className="font-bold text-purple-600">{categories.total}</span> results
                </div>
            <div className="flex items-center space-x-2">
              {categories.links.map((link, index) => (
                <Button
                  key={index}
                  variant={link.active ? "default" : "outline"}
                  size="sm"
                  disabled={!link.url}
                  onClick={() => link.url && router.visit(link.url)}
                  className={link.active 
                    ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg" 
                    : "hover:bg-purple-50 hover:text-purple-700 hover:border-purple-300"
                  }
                  dangerouslySetInnerHTML={{ __html: link.label }}
                />
              ))}
            </div>
          </div>
        )}
            </div>
        </AppLayout>
    );
}