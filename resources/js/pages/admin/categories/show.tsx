import React from 'react';
import { Head, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Award, 
  Users, 
  Vote, 
 
  Calendar, 
  Building2, 
  User, 
  TrendingUp,
  Edit,
  Trash2,
  BarChart3,
  UserCog
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { type BreadcrumbItem, type SharedData } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface TopNominee {
  id: number;
  display_name: string;
  votes_count: number;
  profile_image?: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  price_per_vote: string | number;
  discount_percentage: string | number;
  discount_min_votes: number;
  voting_start_date?: string;
  voting_end_date?: string;
  is_active: boolean;
  is_suspended: boolean;
  suspension_reason?: string;
  created_at: string;
  organization: Organization;
  voting_status: string;
}

interface Stats {
  total_nominees: number;
  total_votes: number;
  total_revenue: number;
  avg_votes_per_nominee: number;
}

interface ShowCategoryProps {
  category: Category;
  stats: Stats;
  topNominees: TopNominee[];
}

export default function ShowCategory({ category, stats, topNominees }: ShowCategoryProps) {
  const { auth } = usePage<SharedData>().props;
  
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Categories', href: '/admin/categories' },
    { title: category.name, href: `/admin/categories/${category.slug}` },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Not Started':
        return 'bg-blue-100 text-blue-800';
      case 'Ended':
        return 'bg-gray-100 text-gray-800';
      case 'Suspended':
        return 'bg-red-100 text-red-800';
      case 'Inactive':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${category.name} - Category Details`} />
      
      <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{category.name}</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Category details and performance metrics
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <a href={route('admin.categories.edit', category.slug)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </a>
            </Button>
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <a href={route('admin.analytics.index', { category_id: category.id })}>
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </a>
            </Button>
            {auth.user.role === 'super_admin' && (
              <Button variant="outline" asChild className="w-full sm:w-auto">
                <a href={route('admin.categories.nominees', category.slug)}>
                  <UserCog className="h-4 w-4 mr-2" />
                  Manage Nominees
                </a>
              </Button>
            )}
          </div>
        </div>

        {/* Status Alert */}
        {category.is_suspended && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
            <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0">
              <div className="flex-shrink-0 mx-auto sm:mx-0">
                <Trash2 className="h-5 w-5 text-red-400" />
              </div>
              <div className="sm:ml-3 text-center sm:text-left">
                <h3 className="text-sm font-medium text-red-800">
                  Category Suspended
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{category.suspension_reason}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm font-medium text-gray-600">Nominees</p>
                      <p className="text-lg sm:text-2xl font-bold text-gray-900">{stats.total_nominees}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Vote className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm font-medium text-gray-600">Total Votes</p>
                      <p className="text-lg sm:text-2xl font-bold text-gray-900">{stats.total_votes.toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <CediSign className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm font-medium text-gray-600">Revenue</p>
                      <p className="text-lg sm:text-2xl font-bold text-gray-900">GHS {stats.total_revenue.toFixed(2)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm font-medium text-gray-600">Avg Votes</p>
                      <p className="text-lg sm:text-2xl font-bold text-gray-900">{stats.avg_votes_per_nominee}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Nominees */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Top Nominees</CardTitle>
                <CardDescription className="text-sm">
                  Leading nominees in this category by vote count
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topNominees.length > 0 ? (
                  <div className="space-y-3 sm:space-y-4">
                    {topNominees.map((nominee, index) => (
                      <div key={nominee.id} className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 p-3 sm:p-4 border rounded-lg">
                        <div className="flex items-center space-x-3 sm:space-x-4">
                          <div className="flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 bg-gray-100 rounded-full text-xs sm:text-sm font-medium">
                            #{index + 1}
                          </div>
                          <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                            <AvatarImage src={nominee.profile_image} />
                            <AvatarFallback className="text-xs sm:text-sm">
                              {nominee.display_name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm sm:text-base font-medium truncate max-w-[200px] sm:max-w-none">{nominee.display_name}</p>
                          </div>
                        </div>
                        <div className="text-center sm:text-right ml-10 sm:ml-0">
                          <p className="text-sm sm:text-base font-semibold">{nominee.votes_count.toLocaleString()}</p>
                          <p className="text-xs sm:text-sm text-gray-500">votes</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 sm:py-8">
                    <Users className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
                    <p className="text-sm sm:text-base text-gray-500">No nominees yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Category Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <Award className="h-4 w-4 sm:h-5 sm:w-5" />
                  Category Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Status</label>
                  <div className="mt-1">
                    <Badge className={getStatusColor(category.voting_status)}>
                      {category.voting_status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Description</label>
                  <p className="mt-1 text-xs sm:text-sm text-gray-900">{category.description}</p>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Price per Vote</label>
                  <p className="mt-1 text-xs sm:text-sm text-gray-900">
                    GHS {Number(category.price_per_vote).toFixed(2)} 
                    <span className="text-xs text-gray-500 block sm:inline sm:ml-2">
                      (GHS {(Number(category.price_per_vote) * 1.03).toFixed(2)} with 3% processing fee)
                    </span>
                  </p>
                </div>

                {Number(category.discount_percentage) > 0 && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-gray-600">Bulk Discount</label>
                    <p className="mt-1 text-xs sm:text-sm text-gray-900">
                      {Number(category.discount_percentage)}% off for {category.discount_min_votes}+ votes
                      <span className="text-xs text-gray-500 block">
                        (Discount applied before processing fee)
                      </span>
                    </p>
                  </div>
                )}

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Organization</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <Building2 className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 flex-shrink-0" />
                    <span className="text-xs sm:text-sm text-gray-900 truncate">{category.organization.name}</span>
                  </div>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Created</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 flex-shrink-0" />
                    <span className="text-xs sm:text-sm text-gray-900">
                      {new Date(category.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Voting Period */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
                  Voting Period
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Start Date</label>
                  <p className="mt-1 text-xs sm:text-sm text-gray-900">
                    {category.voting_start_date 
                      ? new Date(category.voting_start_date).toLocaleString()
                      : 'No start date set'
                    }
                  </p>
                </div>

                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-600">End Date</label>
                  <p className="mt-1 text-xs sm:text-sm text-gray-900">
                    {category.voting_end_date 
                      ? new Date(category.voting_end_date).toLocaleString()
                      : 'No end date set'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start text-sm" asChild>
                  <a href={route('admin.categories.edit', category.slug)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Category
                  </a>
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm" asChild>
                  <a href={route('admin.analytics.index', { category_id: category.id })}>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Analytics
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
} 