import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, Award, Calendar, Percent } from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { type BreadcrumbItem } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface CreateCategoryProps {
  organizations?: Organization[];
}

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Admin Dashboard', href: '/admin' },
  { title: 'Categories', href: '/admin/categories' },
  { title: 'Create', href: '/admin/categories/create' },
];

export default function CreateCategory({ organizations }: CreateCategoryProps) {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    description: '',
    price_per_vote: '',
    discount_percentage: '',
    discount_min_votes: '',
    organization_id: '',
    voting_start_date: '',
    voting_end_date: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('admin.categories.store'));
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Award Category" />
      
      <div className="p-6 space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Award Category</h1>
            <p className="text-muted-foreground">
              Add a new award category to the system
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Category Details
                </CardTitle>
                <CardDescription>
                  Enter the basic information for the new award category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Category Name *</Label>
                      <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        placeholder="Enter category name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="price_per_vote">Price per Vote (GHS) *</Label>
                      <div className="relative">
                        <CediSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="price_per_vote"
                          type="number"
                          step="0.01"
                          min="0.01"
                          value={data.price_per_vote}
                          onChange={(e) => setData('price_per_vote', e.target.value)}
                          placeholder="2.00"
                          className={`pl-10 ${errors.price_per_vote ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.price_per_vote && (
                        <p className="text-sm text-red-500">{errors.price_per_vote}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Describe the award category and its criteria"
                      rows={4}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  {/* Discount Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="discount_percentage">Discount Percentage (%)</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="discount_percentage"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={data.discount_percentage}
                          onChange={(e) => setData('discount_percentage', e.target.value)}
                          placeholder="10.00"
                          className={`pl-10 ${errors.discount_percentage ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.discount_percentage && (
                        <p className="text-sm text-red-500">{errors.discount_percentage}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="discount_min_votes">Minimum Votes for Discount</Label>
                      <Input
                        id="discount_min_votes"
                        type="number"
                        min="1"
                        value={data.discount_min_votes}
                        onChange={(e) => setData('discount_min_votes', e.target.value)}
                        placeholder="50"
                        className={errors.discount_min_votes ? 'border-red-500' : ''}
                      />
                      {errors.discount_min_votes && (
                        <p className="text-sm text-red-500">{errors.discount_min_votes}</p>
                      )}
                    </div>
                  </div>

                                    {/* Organization */}
                  {organizations && (
                    <div className="space-y-2">
                      <Label htmlFor="organization_id">Organization *</Label>
                      <Select value={data.organization_id} onValueChange={(value) => setData('organization_id', value)}>
                        <SelectTrigger className={errors.organization_id ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select organization" />
                        </SelectTrigger>
                        <SelectContent>
                          {organizations.map((org) => (
                            <SelectItem key={org.id} value={org.id.toString()}>
                              {org.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.organization_id && (
                        <p className="text-sm text-red-500">{errors.organization_id}</p>
                      )}
                    </div>
                  )}

                  {/* Voting Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="voting_start_date">Voting Start Date</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="voting_start_date"
                          type="datetime-local"
                          value={data.voting_start_date}
                          onChange={(e) => setData('voting_start_date', e.target.value)}
                          className={`pl-10 ${errors.voting_start_date ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.voting_start_date && (
                        <p className="text-sm text-red-500">{errors.voting_start_date}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="voting_end_date">Voting End Date</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="voting_end_date"
                          type="datetime-local"
                          value={data.voting_end_date}
                          onChange={(e) => setData('voting_end_date', e.target.value)}
                          className={`pl-10 ${errors.voting_end_date ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.voting_end_date && (
                        <p className="text-sm text-red-500">{errors.voting_end_date}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => window.history.back()}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={processing}>
                      {processing ? 'Creating...' : 'Create Category'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Help */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Category name must be unique within the organization</p>
                  <p>• Price per vote determines the cost for each vote</p>
                  <p>• Discount applies when minimum vote threshold is reached</p>
                  <p>• Voting dates are optional - leave blank for always active</p>
                  <p>• All fields marked with * are required</p>
                </div>
              </CardContent>
            </Card>

            {/* Pricing Preview */}
            {data.price_per_vote && (
              <Card>
                <CardHeader>
                  <CardTitle>Pricing Preview</CardTitle>
                  <CardDescription className="text-xs text-slate-500">
                    Includes 3% processing fee
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>1 vote:</span>
                      <span>GHS {(parseFloat(data.price_per_vote || '0') * 1.03).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>10 votes:</span>
                      <span>GHS {(parseFloat(data.price_per_vote || '0') * 10 * 1.03).toFixed(2)}</span>
                    </div>
                    {data.discount_percentage && data.discount_min_votes && (
                      <div className="pt-2 border-t">
                        <div className="flex justify-between text-green-600">
                          <span>{data.discount_min_votes}+ votes:</span>
                          <span>
                            GHS {(
                              (parseFloat(data.price_per_vote || '0') * parseInt(data.discount_min_votes)) *
                              (1 - parseFloat(data.discount_percentage || '0') / 100) *
                              1.03
                            ).toFixed(2)}
                          </span>
                        </div>
                        <p className="text-xs text-green-600 mt-1">
                          {data.discount_percentage}% discount + 3% processing fee
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
} 