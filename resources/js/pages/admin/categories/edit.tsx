import React, { useState } from 'react';
import { Head, useForm, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { AlertCircle, Award, Calendar, Percent, Trash2, Eye, AlertTriangle } from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import { type BreadcrumbItem } from '@/types';

interface Organization {
  id: number;
  name: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  price_per_vote: string | number;
  discount_percentage: string | number;
  discount_min_votes: number;
  organization_id: number;
  voting_start_date?: string;
  voting_end_date?: string;
  is_active: boolean;
  is_suspended: boolean;
  suspension_reason?: string;
}

interface EditCategoryProps {
  category: Category;
  organizations?: Organization[];
}

interface FlashMessages {
  error?: string;
  success?: string;
}

interface PageProps {
  flash?: FlashMessages;
  [key: string]: any;
}

export default function EditCategory({ category, organizations }: EditCategoryProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const { props } = usePage<PageProps>();
  
  const { data, setData, put, processing, errors } = useForm({
    name: category.name,
    description: category.description,
    price_per_vote: String(category.price_per_vote),
    discount_percentage: category.discount_percentage ? String(category.discount_percentage) : '',
    discount_min_votes: category.discount_min_votes?.toString() || '',
    organization_id: category.organization_id.toString(),
    voting_start_date: category.voting_start_date ? 
      new Date(category.voting_start_date).toISOString().slice(0, 16) : '',
    voting_end_date: category.voting_end_date ? 
      new Date(category.voting_end_date).toISOString().slice(0, 16) : '',
  });

  const { delete: deleteCategory } = useForm();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(route('admin.categories.update', category.slug));
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      const deleteUrl = route('admin.categories.destroy', category.slug);
      setIsDeleting(true);
      
      deleteCategory(deleteUrl, {
        onStart: () => {
          // Delete request initiated
        },
        onSuccess: (page) => {          
          // Check if there's a flash error message in the response
          const flashError = (page.props as PageProps).flash?.error || props.flash?.error;
          
          if (flashError) {
            setErrorMessage(flashError);
            setShowErrorDialog(true);
            setIsDeleting(false);
          } else {
            // Only redirect if there's no error
            router.visit(route('admin.categories.index'));
          }
        },
        onError: (errors) => {
          setIsDeleting(false);
          
          // Handle HTTP errors
          if (typeof errors === 'object' && errors.message) {
            setErrorMessage('Failed to delete category: ' + errors.message);
          } else if (typeof errors === 'string') {
            setErrorMessage('Failed to delete category: ' + errors);
          } else {
            setErrorMessage('Failed to delete category. Please try again.');
          }
          setShowErrorDialog(true);
        },
        onFinish: () => {
          setIsDeleting(false);
        }
      });
    }
  };

  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Categories', href: '/admin/categories' },
    { title: category.name, href: `/admin/categories/${category.slug}` },
    { title: 'Edit', href: `/admin/categories/${category.slug}/edit` },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Edit ${category.name}`} />
      
      <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Edit Category</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Update the award category information
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <a href={route('admin.categories.show', category.slug)}>
                <Eye className="h-4 w-4 mr-2" />
                View Category
              </a>
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete} 
              disabled={isDeleting}
              className="w-full sm:w-auto"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>

        {/* Suspension Alert */}
        {category.is_suspended && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
            <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0">
              <div className="flex-shrink-0 mx-auto sm:mx-0">
                <Trash2 className="h-5 w-5 text-red-400" />
              </div>
              <div className="sm:ml-3 text-center sm:text-left">
                <h3 className="text-sm font-medium text-red-800">
                  Category Suspended
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{category.suspension_reason}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <Award className="h-4 w-4 sm:h-5 sm:w-5" />
                  Category Details
                </CardTitle>
                <CardDescription className="text-sm">
                  Update the award category information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Category Name *</Label>
                      <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        placeholder="Enter category name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="price_per_vote">Price per Vote (GHS) *</Label>
                      <div className="relative">
                        <CediSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="price_per_vote"
                          type="number"
                          step="0.01"
                          min="0.01"
                          value={data.price_per_vote}
                          onChange={(e) => setData('price_per_vote', e.target.value)}
                          placeholder="2.00"
                          className={`pl-10 ${errors.price_per_vote ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.price_per_vote && (
                        <p className="text-sm text-red-500">{errors.price_per_vote}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Describe the award category and its criteria"
                      rows={4}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  {/* Discount Settings */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="discount_percentage">Discount Percentage (%)</Label>
                      <div className="relative">
                        <Percent className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="discount_percentage"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={data.discount_percentage}
                          onChange={(e) => setData('discount_percentage', e.target.value)}
                          placeholder="10.00"
                          className={`pl-10 ${errors.discount_percentage ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.discount_percentage && (
                        <p className="text-sm text-red-500">{errors.discount_percentage}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="discount_min_votes">Minimum Votes for Discount</Label>
                      <Input
                        id="discount_min_votes"
                        type="number"
                        min="1"
                        value={data.discount_min_votes}
                        onChange={(e) => setData('discount_min_votes', e.target.value)}
                        placeholder="50"
                        className={errors.discount_min_votes ? 'border-red-500' : ''}
                      />
                      {errors.discount_min_votes && (
                        <p className="text-sm text-red-500">{errors.discount_min_votes}</p>
                      )}
                    </div>
                  </div>

                                    {/* Organization */}
                  {organizations && (
                    <div className="space-y-2">
                      <Label htmlFor="organization_id">Organization *</Label>
                      <Select value={data.organization_id} onValueChange={(value) => setData('organization_id', value)}>
                        <SelectTrigger className={errors.organization_id ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select organization" />
                        </SelectTrigger>
                        <SelectContent>
                          {organizations.map((org) => (
                            <SelectItem key={org.id} value={org.id.toString()}>
                              {org.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.organization_id && (
                        <p className="text-sm text-red-500">{errors.organization_id}</p>
                      )}
                    </div>
                  )}

                  {/* Voting Dates */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="voting_start_date">Voting Start Date</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="voting_start_date"
                          type="datetime-local"
                          value={data.voting_start_date}
                          onChange={(e) => setData('voting_start_date', e.target.value)}
                          className={`pl-10 ${errors.voting_start_date ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.voting_start_date && (
                        <p className="text-sm text-red-500">{errors.voting_start_date}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="voting_end_date">Voting End Date</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="voting_end_date"
                          type="datetime-local"
                          value={data.voting_end_date}
                          onChange={(e) => setData('voting_end_date', e.target.value)}
                          className={`pl-10 ${errors.voting_end_date ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.voting_end_date && (
                        <p className="text-sm text-red-500">{errors.voting_end_date}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col space-y-3 sm:flex-row sm:justify-end sm:space-x-4 sm:space-y-0">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => window.history.back()}
                      className="w-full sm:w-auto"
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={processing} className="w-full sm:w-auto">
                      {processing ? 'Updating...' : 'Update Category'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Help */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-xs sm:text-sm text-gray-600">
                  <p>• Category name must be unique within the organization</p>
                  <p>• Price changes will affect future votes only</p>
                  <p>• Discount applies when minimum vote threshold is reached</p>
                  <p>• Voting dates are optional - leave blank for always active</p>
                  <p>• All fields marked with * are required</p>
                </div>
              </CardContent>
            </Card>

            {/* Current Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Current Pricing</CardTitle>
                <CardDescription className="text-xs text-slate-500">
                  Final prices include 3% processing fee
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-xs sm:text-sm">
                  <div className="flex justify-between">
                    <span>Current price (final):</span>
                    <span className="font-medium">GHS {(Number(category.price_per_vote) * 1.03).toFixed(2)}</span>
                  </div>
                  {data.price_per_vote && data.price_per_vote !== String(category.price_per_vote) && (
                    <div className="flex justify-between text-blue-600">
                      <span>New price (final):</span>
                      <span className="font-medium">GHS {(parseFloat(data.price_per_vote || '0') * 1.03).toFixed(2)}</span>
                    </div>
                  )}
                  {Number(category.discount_percentage) > 0 && (
                    <div className="pt-2 border-t">
                      <div className="flex justify-between text-green-600">
                        <span>Bulk discount:</span>
                        <span className="font-medium">{Number(category.discount_percentage)}%</span>
                      </div>
                      <p className="text-xs text-green-600 mt-1">
                        For {category.discount_min_votes}+ votes (applied before processing fee)
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Pricing Preview */}
            {data.price_per_vote && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">New Pricing Preview</CardTitle>
                  <CardDescription className="text-xs text-slate-500">
                    Includes 3% processing fee
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs sm:text-sm">
                    <div className="flex justify-between">
                      <span>1 vote:</span>
                      <span className="font-medium">GHS {(parseFloat(data.price_per_vote || '0') * 1.03).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>10 votes:</span>
                      <span className="font-medium">GHS {(parseFloat(data.price_per_vote || '0') * 10 * 1.03).toFixed(2)}</span>
                    </div>
                    {data.discount_percentage && data.discount_min_votes && (
                      <div className="pt-2 border-t">
                        <div className="flex justify-between text-green-600">
                          <span>{data.discount_min_votes}+ votes:</span>
                          <span className="font-medium">
                            GHS {(
                              (parseFloat(data.price_per_vote || '0') * parseInt(data.discount_min_votes)) *
                              (1 - parseFloat(data.discount_percentage || '0') / 100) *
                              1.03
                            ).toFixed(2)}
                          </span>
                        </div>
                        <p className="text-xs text-green-600 mt-1">
                          {data.discount_percentage}% discount + 3% processing fee
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Error Dialog */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Cannot Delete Category
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              {errorMessage}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Tip:</strong> To delete this category, you must first remove all nominees assigned to it. 
              You can manage nominees by clicking "View Category" and then "Manage Nominees".
            </p>
          </div>
          <DialogFooter>
            <Button 
              onClick={() => setShowErrorDialog(false)}
              className="w-full sm:w-auto"
            >
              Close
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                setShowErrorDialog(false);
                router.visit(route('admin.categories.show', category.slug));
              }}
              className="w-full sm:w-auto"
            >
              View Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
} 