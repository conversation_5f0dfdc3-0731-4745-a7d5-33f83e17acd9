import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Bar<PERSON>hart3,
    TrendingUp,
    TrendingDown,
    Users,
    Vote,
  
    Calendar,
    Download,
    Filter,
    RefreshCw,
    ArrowUpRight,
    ArrowDownRight,
    Eye,
    PieChart,
    Activity
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface AnalyticsData {
    overview: {
        total_votes: { value: number; change: number; change_type: 'positive' | 'negative' | 'neutral' };
        total_revenue: { value: string; change: number; change_type: 'positive' | 'negative' | 'neutral' };
        active_users: { value: number; change: number; change_type: 'positive' | 'negative' | 'neutral' };
        conversion_rate: { value: string; change: number; change_type: 'positive' | 'negative' | 'neutral' };
    };
    charts: {
        revenue_trend: Array<{ date: string; revenue: number }>;
        voting_trend: Array<{ date: string; votes: number }>;
        category_performance: Array<{ name: string; votes: number; revenue: number }>;
        user_activity: Array<{ date: string; active_users: number }>;
    };
    top_performers: {
        categories: Array<{ id: number; name: string; votes: number; revenue: string }>;
        nominees: Array<{ id: number; name: string; category: string; votes: number }>;
        organizations: Array<{ id: number; name: string; total_votes: number; total_revenue: string }>;
    };
}

interface AnalyticsProps {
    data: AnalyticsData;
    filters: {
        start_date: string;
        end_date: string;
        category_id?: number;
        organization_id?: number;
    };
    user_role: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Analytics', href: '/admin/analytics' },
];

export default function Analytics({ data, filters, user_role }: AnalyticsProps) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date,
        end_date: filters.end_date,
    });

    const getChangeIcon = (changeType: string) => {
        switch (changeType) {
            case 'positive':
                return <ArrowUpRight className="h-4 w-4 text-green-600" />;
            case 'negative':
                return <ArrowDownRight className="h-4 w-4 text-red-600" />;
            default:
                return <Activity className="h-4 w-4 text-gray-600" />;
        }
    };

    const getChangeColor = (changeType: string) => {
        switch (changeType) {
            case 'positive':
                return 'text-green-600';
            case 'negative':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    return (        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Analytics - Admin Dashboard">
                <meta name="description" content="Comprehensive analytics dashboard for VoteYourFav platform. Monitor voting trends, revenue performance, user engagement metrics, and detailed reporting with real-time data insights for administrative decision-making." />
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Analytics Dashboard - VoteYourFav Admin" />
                <meta property="og:description" content="Access detailed analytics and performance metrics for the VoteYourFav voting platform. Track voting trends, revenue data, user activity, and generate comprehensive reports for data-driven insights." />
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:title" content="Analytics Dashboard - VoteYourFav Admin" />
                <meta name="twitter:description" content="Comprehensive analytics dashboard with voting trends, revenue tracking, and user engagement metrics for VoteYourFav platform administration." />
            </Head>

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Analytics & Reports
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Comprehensive insights and performance metrics
                            </p>
                        </div>
                        {/* <div className="flex flex-wrap items-center gap-3">
                            <Button variant="outline" size="sm" className="h-9">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                        </div> */}
                    </div>

                    {/* Overview Stats */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Votes</CardTitle>
                                <Vote className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {data.overview.total_votes.value.toLocaleString()}
                                </div>
                                <div className={`flex items-center text-xs ${getChangeColor(data.overview.total_votes.change_type)}`}>
                                    {getChangeIcon(data.overview.total_votes.change_type)}
                                    <span className="ml-1">
                                        {Math.abs(data.overview.total_votes.change)}% from last period
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
                                <CediSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {data.overview.total_revenue.value}
                                </div>
                                <div className={`flex items-center text-xs ${getChangeColor(data.overview.total_revenue.change_type)}`}>
                                    {getChangeIcon(data.overview.total_revenue.change_type)}
                                    <span className="ml-1">
                                        {Math.abs(data.overview.total_revenue.change)}% from last period
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Active Users</CardTitle>
                                <Users className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {data.overview.active_users.value.toLocaleString()}
                                </div>
                                <div className={`flex items-center text-xs ${getChangeColor(data.overview.active_users.change_type)}`}>
                                    {getChangeIcon(data.overview.active_users.change_type)}
                                    <span className="ml-1">
                                        {Math.abs(data.overview.active_users.change)}% from last period
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Conversion Rate</CardTitle>
                                <TrendingUp className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {data.overview.conversion_rate.value}
                                </div>
                                <div className={`flex items-center text-xs ${getChangeColor(data.overview.conversion_rate.change_type)}`}>
                                    {getChangeIcon(data.overview.conversion_rate.change_type)}
                                    <span className="ml-1">
                                        {Math.abs(data.overview.conversion_rate.change)}% from last period
                                    </span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>


                
                    {/* Top Performers */}
                    <div className="grid gap-6 lg:grid-cols-3">
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg">Top Categories</CardTitle>
                                <CardDescription>Best performing award categories</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {data.top_performers.categories.map((category, index) => (
                                    <div key={category.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">{category.name}</p>
                                                <p className="text-sm text-gray-500">{category.votes} votes</p>
                                            </div>
                                        </div>
                                        <Badge variant="secondary">{category.revenue}</Badge>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="text-lg">Top Nominees</CardTitle>
                                <CardDescription>Most voted nominees</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {data.top_performers.nominees.map((nominee, index) => (
                                    <div key={nominee.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                <span className="text-sm font-bold text-green-600">#{index + 1}</span>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900">{nominee.name}</p>
                                                <p className="text-sm text-gray-500">{nominee.category}</p>
                                            </div>
                                        </div>
                                        <Badge variant="secondary">{nominee.votes} votes</Badge>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {user_role === 'super_admin' && (
                            <Card className="border-0 shadow-sm">
                                <CardHeader>
                                    <CardTitle className="text-lg">Top Organizations</CardTitle>
                                    <CardDescription>Highest performing organizations</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {data.top_performers.organizations.map((org, index) => (
                                        <div key={org.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                                    <span className="text-sm font-bold text-purple-600">#{index + 1}</span>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">{org.name}</p>
                                                    <p className="text-sm text-gray-500">{org.total_votes} votes</p>
                                                </div>
                                            </div>
                                            <Badge variant="secondary">{org.total_revenue}</Badge>
                                        </div>
                                    ))}
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Quick Actions */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common analytics tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-3">
                                <Button variant="outline" size="sm">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export Full Report
                                </Button>
                                <Button variant="outline" size="sm">
                                    <Calendar className="h-4 w-4 mr-2" />
                                    Schedule Report
                                </Button>
                                <Button variant="outline" size="sm">
                                    <PieChart className="h-4 w-4 mr-2" />
                                    Custom Dashboard
                                </Button>
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin">
                                        <Eye className="h-4 w-4 mr-2" />
                                        Back to Dashboard
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 