import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
    Settings as SettingsIcon,
    Save,
    Database,
    Download,
    Shield,
    Mail,
    Globe,
    CreditCard,
    Bell,
    Users,
    FileText,
    AlertCircle,
    CheckCircle,
    RefreshCw,
    Eye,
    Server
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { FormEventHandler } from 'react';

interface SystemSettings {
    site_name: string;
    site_description: string;
    site_url: string;
    admin_email: string;
    support_email: string;
    maintenance_mode: boolean;
    registration_enabled: boolean;
    email_verification_required: boolean;
    voting_enabled: boolean;
    payment_gateway: string;
    currency: string;
    timezone: string;
    max_votes_per_user: number;
    backup_frequency: string;
    log_retention_days: number;
    [key: string]: any; // Add index signature for FormDataType compatibility
}

interface SystemHealth {
    database_status: 'healthy' | 'warning' | 'error';
    storage_usage: string;
    storage_total: string;
    cache_status: 'healthy' | 'warning' | 'error';
    queue_status: 'healthy' | 'warning' | 'error';
    last_backup: string;
    php_version: string;
    laravel_version: string;
}

interface SettingsProps {
    settings: SystemSettings;
    health?: SystemHealth;
    user_role: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Settings', href: '/admin/settings' },
];

export default function Settings({ settings, health, user_role }: SettingsProps) {
    const { data, setData, post, processing, errors, reset } = useForm<SystemSettings>(settings);

    // Provide fallback values for health data
    const safeHealth: SystemHealth = health || {
        database_status: 'error',
        storage_usage: '0 GB',
        storage_total: '0 GB',
        cache_status: 'error',
        queue_status: 'error',
        last_backup: new Date().toISOString(),
        php_version: 'Unknown',
        laravel_version: 'Unknown'
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post('/admin/settings', {
            onSuccess: () => {
                // Handle success
            },
        });
    };

    const handleBackup = () => {
        window.location.href = '/admin/settings/backup';
    };

    const getHealthIcon = (status: string) => {
        switch (status) {
            case 'healthy':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'warning':
                return <AlertCircle className="h-4 w-4 text-yellow-600" />;
            case 'error':
                return <AlertCircle className="h-4 w-4 text-red-600" />;
            default:
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
        }
    };

    const getHealthColor = (status: string) => {
        switch (status) {
            case 'healthy':
                return 'text-green-600';
            case 'warning':
                return 'text-yellow-600';
            case 'error':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Settings - Admin Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">
                                System Settings
                            </h1>
                            <p className="text-sm sm:text-lg text-muted-foreground">
                                Configure system preferences and monitor health
                            </p>
                        </div>
                        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:gap-3 sm:space-y-0">
                            <Button variant="outline" size="sm" className="h-9 w-full sm:w-auto" onClick={handleBackup}>
                                <Database className="h-4 w-4 mr-2" />
                                Backup Now
                            </Button>
                            <Button variant="outline" size="sm" className="h-9 w-full sm:w-auto" asChild>
                                <Link href="/admin/settings/logs">
                                    <FileText className="h-4 w-4 mr-2" />
                                    View Logs
                                </Link>
                            </Button>
                        </div>
                    </div>

                    <div className="grid gap-6 lg:gap-8 lg:grid-cols-3">
                        {/* Main Settings Form */}
                        <div className="lg:col-span-2 space-y-6 lg:space-y-8">
                            <form onSubmit={submit} className="space-y-8">
                                {/* General Settings */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Globe className="h-5 w-5 text-blue-600" />
                                            General Settings
                                        </CardTitle>
                                        <CardDescription>
                                            Basic site configuration
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid gap-6 sm:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="site_name">Site Name</Label>                                                <Input
                                                    id="site_name"
                                                    value={data.site_name}
                                                    onChange={(e) => setData('site_name', e.target.value)}
                                                    placeholder="Vote Your Fav"
                                                />
                                                {errors.site_name && (
                                                    <p className="text-sm text-red-600">{errors.site_name}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="site_url">Site URL</Label>
                                                <Input
                                                    id="site_url"
                                                    value={data.site_url}
                                                    onChange={(e) => setData('site_url', e.target.value)}
                                                    placeholder="https://voteyourfav.com"
                                                />
                                                {errors.site_url && (
                                                    <p className="text-sm text-red-600">{errors.site_url}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="site_description">Site Description</Label>
                                            <Textarea
                                                id="site_description"
                                                value={data.site_description}
                                                onChange={(e) => setData('site_description', e.target.value)}
                                                placeholder="Award voting platform"
                                                rows={3}
                                            />
                                            {errors.site_description && (
                                                <p className="text-sm text-red-600">{errors.site_description}</p>
                                            )}
                                        </div>

                                        <div className="grid gap-6 sm:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="admin_email">Admin Email</Label>
                                                <div className="relative">
                                                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                                    <Input
                                                        id="admin_email"
                                                        type="email"
                                                        value={data.admin_email}
                                                        onChange={(e) => setData('admin_email', e.target.value)}
                                                        placeholder="<EMAIL>"
                                                        className="pl-10"
                                                    />
                                                </div>
                                                {errors.admin_email && (
                                                    <p className="text-sm text-red-600">{errors.admin_email}</p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="support_email">Support Email</Label>
                                                <div className="relative">
                                                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                                    <Input
                                                        id="support_email"
                                                        type="email"
                                                        value={data.support_email}
                                                        onChange={(e) => setData('support_email', e.target.value)}
                                                        placeholder="<EMAIL>"
                                                        className="pl-10"
                                                    />
                                                </div>
                                                {errors.support_email && (
                                                    <p className="text-sm text-red-600">{errors.support_email}</p>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Feature Settings */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Shield className="h-5 w-5 text-green-600" />
                                            Feature Settings
                                        </CardTitle>
                                        <CardDescription>
                                            Control system features and access
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label>Maintenance Mode</Label>
                                                    <p className="text-sm text-gray-500">
                                                        Temporarily disable site access
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={data.maintenance_mode}
                                                    onCheckedChange={(checked) => setData('maintenance_mode', checked)}
                                                />
                                            </div>

                                            <Separator />

                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label>User Registration</Label>
                                                    <p className="text-sm text-gray-500">
                                                        Allow new user registrations
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={data.registration_enabled}
                                                    onCheckedChange={(checked) => setData('registration_enabled', checked)}
                                                />
                                            </div>

                                            <Separator />

                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label>Email Verification</Label>
                                                    <p className="text-sm text-gray-500">
                                                        Require email verification for new users
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={data.email_verification_required}
                                                    onCheckedChange={(checked) => setData('email_verification_required', checked)}
                                                />
                                            </div>

                                            <Separator />

                                            <div className="flex items-center justify-between">
                                                <div className="space-y-0.5">
                                                    <Label>Voting System</Label>
                                                    <p className="text-sm text-gray-500">
                                                        Enable voting functionality
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={data.voting_enabled}
                                                    onCheckedChange={(checked) => setData('voting_enabled', checked)}
                                                />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Payment Settings */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <CreditCard className="h-5 w-5 text-purple-600" />
                                            Payment Settings
                                        </CardTitle>
                                        <CardDescription>
                                            Configure payment processing
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                            <div className="space-y-2">
                                                <Label htmlFor="payment_gateway">Payment Gateway</Label>
                                                <Input
                                                    id="payment_gateway"
                                                    value={data.payment_gateway}
                                                    onChange={(e) => setData('payment_gateway', e.target.value)}
                                                    placeholder="stripe"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="currency">Currency</Label>
                                                <Input
                                                    id="currency"
                                                    value={data.currency}
                                                    onChange={(e) => setData('currency', e.target.value)}
                                                    placeholder="GHS"
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="max_votes_per_user">Max Votes Per User</Label>
                                                <Input
                                                    id="max_votes_per_user"
                                                    type="number"
                                                    value={data.max_votes_per_user}
                                                    onChange={(e) => setData('max_votes_per_user', parseInt(e.target.value))}
                                                    placeholder="10"
                                                />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Save Button */}
                                <Card className="border-0 shadow-sm">
                                    <CardContent className="p-4 sm:p-6">
                                        <Button type="submit" disabled={processing} className="w-full sm:w-auto">
                                            <Save className="h-4 w-4 mr-2" />
                                            {processing ? 'Saving...' : 'Save Settings'}
                                        </Button>
                                    </CardContent>
                                </Card>
                            </form>
                        </div>

                        {/* System Health Sidebar */}
                        <div className="space-y-6 lg:space-y-8">
                            {/* System Health */}
                            <Card className="border-0 shadow-sm">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Server className="h-5 w-5 text-green-600" />
                                        System Health
                                    </CardTitle>
                                    <CardDescription>
                                        Current system status
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Database</span>
                                            <div className="flex items-center gap-2">
                                                {getHealthIcon(safeHealth.database_status)}
                                                <span className={`text-sm ${getHealthColor(safeHealth.database_status)}`}>
                                                    {safeHealth.database_status}
                                                </span>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Cache</span>
                                            <div className="flex items-center gap-2">
                                                {getHealthIcon(safeHealth.cache_status)}
                                                <span className={`text-sm ${getHealthColor(safeHealth.cache_status)}`}>
                                                    {safeHealth.cache_status}
                                                </span>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Queue</span>
                                            <div className="flex items-center gap-2">
                                                {getHealthIcon(safeHealth.queue_status)}
                                                <span className={`text-sm ${getHealthColor(safeHealth.queue_status)}`}>
                                                    {safeHealth.queue_status}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Storage</span>
                                            <span className="text-sm text-gray-600">
                                                {safeHealth.storage_usage} / {safeHealth.storage_total}
                                            </span>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Last Backup</span>
                                            <span className="text-sm text-gray-600">
                                                {new Date(safeHealth.last_backup).toLocaleDateString()}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* System Info */}
                            <Card className="border-0 shadow-sm">
                                <CardHeader>
                                    <CardTitle>System Information</CardTitle>
                                    <CardDescription>
                                        Version and environment details
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">PHP Version</span>
                                        <span className="text-sm text-gray-600">{safeHealth.php_version}</span>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">Laravel Version</span>
                                        <span className="text-sm text-gray-600">{safeHealth.laravel_version}</span>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">Timezone</span>
                                        <span className="text-sm text-gray-600">{data.timezone}</span>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Quick Actions */}
                            <Card className="border-0 shadow-sm">
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                    <CardDescription>
                                        Common administrative tasks
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <Button variant="outline" size="sm" className="w-full justify-start" onClick={handleBackup}>
                                        <Database className="h-4 w-4 mr-2" />
                                        Create Backup
                                    </Button>
                                    <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                        <Link href="/admin/settings/logs">
                                            <FileText className="h-4 w-4 mr-2" />
                                            View System Logs
                                        </Link>
                                    </Button>
                                    <Button variant="outline" size="sm" className="w-full justify-start">
                                        <RefreshCw className="h-4 w-4 mr-2" />
                                        Clear Cache
                                    </Button>
                                    <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                                        <Link href="/admin">
                                            <Eye className="h-4 w-4 mr-2" />
                                            Back to Dashboard
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 