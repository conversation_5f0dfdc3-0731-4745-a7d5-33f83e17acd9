import { Head, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { format } from 'date-fns';
import { Users, CheckCircle, XCircle, Percent, Phone } from 'lucide-react';
import { useState } from 'react';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';

interface PendingOrganization extends PageProps {
    id: number;
    name: string;
    email: string;
    phone_number?: string;
    created_at: string;
    organization: {
        id: number;
        name: string;
    } | null;
}

interface PendingOrganizationsProps extends PageProps {
    pendingOrganizations: PendingOrganization[];
}

const breadcrumbs = [
    { title: 'Admin Dashboard', href: route('admin.dashboard') },
    { title: 'Pending Organizations', href: route('admin.pending-organizations.index') },
];

export default function PendingOrganizationsIndex({ pendingOrganizations }: PendingOrganizationsProps) {
    const [selectedOrg, setSelectedOrg] = useState<PendingOrganization | null>(null);
    const [approveDialogOpen, setApproveDialogOpen] = useState(false);
    const [rejectDialogOpen, setRejectDialogOpen] = useState(false);

    const { data: approveData, setData: setApproveData, post: postApprove, processing: approveProcessing, errors: approveErrors } = useForm({
        revenue_percentage: '10.00',
    });

    const { data: rejectData, setData: setRejectData, post: postReject, processing: rejectProcessing, errors: rejectErrors } = useForm({
        rejection_reason: '',
    });

    const handleApprove = (org: PendingOrganization) => {
        setSelectedOrg(org);
        setApproveDialogOpen(true);
    };

    const submitApproval = () => {
        if (!selectedOrg) return;

        postApprove(route('admin.pending-organizations.approve', selectedOrg.id), {
            onSuccess: () => {
                toast("Registration Approved", {
                    description: `${selectedOrg.organization?.name || selectedOrg.name} has been approved with ${approveData.revenue_percentage}% revenue rate.`,
                });
                setApproveDialogOpen(false);
                setSelectedOrg(null);
            },
            onError: (errors) => {
                toast("Approval Failed", {
                    description: errors.message || "There was an error approving the registration.",
                });
            },
        });
    };

    const handleReject = (org: PendingOrganization) => {
        setSelectedOrg(org);
        setRejectDialogOpen(true);
    };

    const submitRejection = () => {
        if (!selectedOrg) return;

        postReject(route('admin.pending-organizations.reject', selectedOrg.id), {
            onSuccess: () => {
                toast("Registration Rejected", {
                    description: `${selectedOrg.organization?.name || selectedOrg.name} has been rejected.`,
                });
                setRejectDialogOpen(false);
                setSelectedOrg(null);
            },
            onError: (errors) => {
                toast("Rejection Failed", {
                    description: errors.message || "There was an error rejecting the registration.",
                });
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Pending Organization Approvals" />
            <Toaster />

            <Card className="border-0 shadow-sm">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-600" />
                        Pending Organization Registrations
                    </CardTitle>
                    <CardDescription>
                        Review and manage organization registrations awaiting approval.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Organization Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Phone</TableHead>
                                <TableHead>Registration Date</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {pendingOrganizations.length > 0 ? (
                                pendingOrganizations.map((orgUser) => (
                                    <TableRow key={orgUser.id}>
                                        <TableCell className="font-medium">{orgUser.organization?.name || orgUser.name}</TableCell>
                                        <TableCell>{orgUser.email}</TableCell>
                                        <TableCell>
                                            {orgUser.phone_number ? (
                                                <div className="flex items-center gap-1">
                                                    <Phone className="h-3 w-3 text-green-600" />
                                                    <span className="text-sm">{orgUser.phone_number}</span>
                                                </div>
                                            ) : (
                                                <span className="text-gray-400 text-sm">No phone</span>
                                            )}
                                        </TableCell>
                                        <TableCell>{format(new Date(orgUser.created_at), 'PPP')}</TableCell>
                                        <TableCell className="text-right flex justify-end gap-2">
                                            <Button
                                                variant="default"
                                                size="sm"
                                                onClick={() => handleApprove(orgUser)}
                                                disabled={approveProcessing || rejectProcessing}
                                            >
                                                <CheckCircle className="h-4 w-4 mr-1" />
                                                Approve
                                            </Button>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleReject(orgUser)}
                                                disabled={approveProcessing || rejectProcessing}
                                            >
                                                 <XCircle className="h-4 w-4 mr-1" />
                                                Reject
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={5} className="text-center">No pending organization registrations.</TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* Approval Dialog */}
            <Dialog open={approveDialogOpen} onOpenChange={setApproveDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            Approve Organization
                        </DialogTitle>
                        <DialogDescription>
                            Set the revenue percentage for <strong>{selectedOrg?.organization?.name || selectedOrg?.name}</strong>
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="revenue_percentage" className="flex items-center gap-2">
                                <Percent className="h-4 w-4" />
                                Revenue Percentage
                            </Label>
                            <Input
                                id="revenue_percentage"
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                                value={approveData.revenue_percentage}
                                onChange={(e) => setApproveData('revenue_percentage', e.target.value)}
                                placeholder="10.00"
                                className="text-right"
                            />
                            {approveErrors.revenue_percentage && (
                                <p className="text-sm text-red-500">{approveErrors.revenue_percentage}</p>
                            )}
                            <p className="text-xs text-gray-500">
                                Percentage of voting revenue this organization will pay to the platform
                            </p>
                        </div>
                        {selectedOrg?.phone_number && (
                            <div className="bg-blue-50 p-3 rounded-lg">
                                <p className="text-sm text-blue-800">
                                    <Phone className="h-4 w-4 inline mr-1" />
                                    SMS notification will be sent to: {selectedOrg.phone_number}
                                </p>
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setApproveDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={submitApproval} disabled={approveProcessing}>
                            {approveProcessing ? 'Approving...' : 'Approve Organization'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Rejection Dialog */}
            <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <XCircle className="h-5 w-5 text-red-600" />
                            Reject Organization
                        </DialogTitle>
                        <DialogDescription>
                            Provide a reason for rejecting <strong>{selectedOrg?.organization?.name || selectedOrg?.name}</strong>
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="rejection_reason">Rejection Reason</Label>
                            <Textarea
                                id="rejection_reason"
                                value={rejectData.rejection_reason}
                                onChange={(e) => setRejectData('rejection_reason', e.target.value)}
                                placeholder="Please provide a clear reason for rejection..."
                                rows={4}
                            />
                            {rejectErrors.rejection_reason && (
                                <p className="text-sm text-red-500">{rejectErrors.rejection_reason}</p>
                            )}
                        </div>
                        {selectedOrg?.phone_number && (
                            <div className="bg-red-50 p-3 rounded-lg">
                                <p className="text-sm text-red-800">
                                    <Phone className="h-4 w-4 inline mr-1" />
                                    SMS notification will be sent to: {selectedOrg.phone_number}
                                </p>
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setRejectDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={submitRejection} disabled={rejectProcessing}>
                            {rejectProcessing ? 'Rejecting...' : 'Reject Organization'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
} 