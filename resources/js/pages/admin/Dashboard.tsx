import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';
import {
    Trophy,
    Users,
    Vote,
    TrendingUp,
    Activity,
    Calendar,
    BarChart3,
    PieChart,
    Settings,
    Download,
    Filter,
    RefreshCw,
    Building2,
    CreditCard,
    UserPlus,
    Plus,
    Eye,
    FileText,
    Database,
    Shield,
    Bell,
    Search,
    ArrowRight,
    UserCheck,
    Clock
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface DashboardStats {
    total_votes: {
        value: string;
        change: number;
        change_type: 'positive' | 'negative' | 'neutral';
    };
    total_revenue: {
        value: string;
        change: number;
        change_type: 'positive' | 'negative' | 'neutral';
    };
    gross_revenue?: {
        value: string;
        change: number;
        change_type: 'positive' | 'negative' | 'neutral';
    };
    active_nominees: {
        value: string;
        change: string;
        change_type: 'positive' | 'negative' | 'neutral';
    };
    active_categories: {
        value: string;
        change: string;
        change_type: 'positive' | 'negative' | 'neutral';
    };
    total_organizations?: {
        value: string;
        change: string;
        change_type: 'positive' | 'negative' | 'neutral';
    };
}

interface RecentActivity {
    type: string;
    description: string;
    amount?: string;
    time: string;
    status: string;
}

interface TopCategory {
    id: number;
    name: string;
    votes_count: number;
    total_revenue: string;
    price_per_vote: string;
}

interface TopNominee {
    id: number;
    display_name: string;
    category: string;
    votes_count: number;
    profile_image?: string;
    nominee_code?: string;
    slug: string;
}

interface RevenueData {
    date: string;
    revenue: number;
}

interface VotingTrend {
    date: string;
    votes: number;
}

interface RecentPayment {
    id: number;
    reference: string;
    amount: string;
    status: string;
    nominee: string;
    category: string;
    created_at: string;
}

interface SystemHealth {
    total_users: number;
    active_organizations: number;
    total_categories: number;
    total_nominees: number;
    pending_payments: number;
    failed_payments: number;
    database_size: string;
    storage_usage: string;
}

interface DashboardProps {
    stats: DashboardStats | null;
    recentActivities: RecentActivity[];
    topCategories: TopCategory[];
    topNominees: TopNominee[];
    revenueData: RevenueData[];
    votingTrends: VotingTrend[];
    recentPayments: RecentPayment[];
    systemHealth?: SystemHealth;
    filters: {
        start_date: string;
        end_date: string;
    };
    user_role: string;
    organization?: {
        id: number;
        name: string;
        slug: string;
    };
    is_pending_approval?: boolean;
    approval_status?: string;
    created_at?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin',
    },
];

export default function AdminDashboard({
    stats,
    recentActivities,
    topCategories,
    topNominees,
    revenueData,
    votingTrends,
    recentPayments,
    systemHealth,
    filters,
    user_role,
    organization,
    is_pending_approval,
    approval_status,
    created_at
}: DashboardProps) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date,
        end_date: filters.end_date,
    });

    const getChangeColor = (changeType: string) => {
        switch (changeType) {
            case 'positive':
                return 'text-green-600';
            case 'negative':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const getChangeIcon = (changeType: string) => {
        switch (changeType) {
            case 'positive':
                return '↗';
            case 'negative':
                return '↘';
            default:
                return '→';
        }
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'completed':
                return 'default';
            case 'pending':
                return 'secondary';
            case 'failed':
                return 'destructive';
            default:
                return 'outline';
        }
    };

    // Format date for display
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Quick action items for super admin
    const quickActions = user_role === 'super_admin' ? [
        {
            title: 'Add New User',
            description: 'Create admin or nominee accounts',
            href: '/admin/users/create',
            icon: UserPlus,
            color: 'bg-blue-500',
        },
        {
            title: 'Pending Approvals',
            description: 'Review user registrations',
            href: '/admin/users/pending/approvals',
            icon: Clock,
            color: 'bg-orange-500',
        },
        {
            title: 'New Organization',
            description: 'Register a new organization',
            href: '/admin/organizations/create',
            icon: Building2,
            color: 'bg-green-500',
        },
        {
            title: 'Create Category',
            description: 'Add new award category',
            href: '/admin/categories/create',
            icon: Trophy,
            color: 'bg-yellow-500',
        },
        {
            title: 'View Reports',
            description: 'Generate detailed analytics',
            href: '/admin/analytics',
            icon: FileText,
            color: 'bg-purple-500',
        },
        {
            title: 'System Settings',
            description: 'Configure system preferences',
            href: '/admin/settings',
            icon: Settings,
            color: 'bg-gray-500',
        },
    ] : [
        {
            title: 'Manage Users',
            description: 'Manage organization members',
            href: '/admin/org-users',
            icon: Users,
            color: 'bg-blue-500',
        },
        {
            title: 'Pending Approvals',
            description: 'Review user applications',
            href: '/admin/org-users/pending/approvals',
            icon: UserCheck,
            color: 'bg-orange-500',
        },
        {
            title: 'Create Category',
            description: 'Add new award category',
            href: '/admin/categories/create',
            icon: Trophy,
            color: 'bg-yellow-500',
        },
        {
            title: 'Nomination Link',
            description: 'Your organization\'s nomination application link',
            href: organization?.slug ? `/apply/${organization.slug}` : '/apply-for-nomination',
            icon: UserPlus,
            color: 'bg-indigo-500',
        },
        {
            title: 'View Analytics',
            description: 'Check performance metrics',
            href: '/admin/analytics',
            icon: BarChart3,
            color: 'bg-purple-500',
        },
        {
            title: 'Manage Payments',
            description: 'Review payment transactions',
            href: '/admin/payments',
            icon: CreditCard,
            color: 'bg-green-500',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Admin Dashboard">
                <meta name="description" content="VoteYourFav Admin Dashboard - Monitor voting statistics, manage users and organizations, track revenue, view analytics, and control system settings." />
                
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Admin Dashboard - VoteYourFav" />
                <meta property="og:description" content="VoteYourFav Admin Dashboard - Monitor voting statistics, manage users and organizations, track revenue, view analytics, and control system settings." />
                
                <meta property="twitter:card" content="summary" />
                <meta property="twitter:title" content="Admin Dashboard - VoteYourFav" />
                <meta property="twitter:description" content="VoteYourFav Admin Dashboard - Monitor voting statistics, manage users and organizations, track revenue, view analytics, and control system settings." />
            </Head>

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
                    {/* Pending Approval Notice */}
                    {is_pending_approval && (
                        <Card className="border-orange-200 bg-orange-50">
                            <CardContent className="p-4 sm:p-6">
                                <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:space-x-4 sm:space-y-0">
                                    <div className="p-3 bg-orange-100 rounded-full flex-shrink-0 w-fit mx-auto sm:mx-0">
                                        <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                                    </div>
                                    <div className="flex-1 text-center sm:text-left">
                                        <h3 className="text-base sm:text-lg font-semibold text-orange-900 mb-2">
                                            Account Pending Approval
                                        </h3>
                                        <p className="text-sm sm:text-base text-orange-800 mb-4">
                                            Your organization registration for <strong>{organization?.name}</strong> is currently under review by our administrators. 
                                            Access to admin features is restricted until your account is approved.
                                        </p>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm text-orange-700">
                                            <div className="text-center sm:text-left">
                                                <span className="font-medium">Registration Date:</span><br className="sm:hidden" />
                                                <span className="sm:ml-1">{created_at && formatDate(created_at)}</span>
                                            </div>
                                            <div className="text-center sm:text-left">
                                                <span className="font-medium">Status:</span> 
                                                <Badge variant="secondary" className="ml-1 sm:ml-2 bg-orange-100 text-orange-800 text-xs">
                                                    {approval_status}
                                                </Badge>
                                            </div>
                                        </div>
                                        <div className="mt-4 p-3 sm:p-4 bg-orange-100 rounded-lg">
                                            <p className="text-xs sm:text-sm text-orange-800">
                                                <strong>What happens next?</strong><br />
                                                • Our team will review your organization details<br />
                                                • You'll receive an email notification once approved<br />
                                                • Full admin access will be granted upon approval
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                        <div className="text-center sm:text-left">
                            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                                {is_pending_approval ? 'Welcome to VoteYourFav' : 'Admin Dashboard'}
                            </h1>
                            <p className="text-sm sm:text-base text-gray-600 mt-1">
                                {is_pending_approval 
                                    ? 'Your account is being reviewed. Limited access is available.'
                                    : organization 
                                        ? `Managing ${organization.name}` 
                                        : 'System-wide administration'
                                }
                            </p>
                        </div>
                    </div>

                    {/* Quick Actions Section - Only show if not pending approval */}
                    {!is_pending_approval && (
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">Quick Actions</h2>
                                    <p className="text-gray-600 mt-1">Frequently used admin functions</p>
                                </div>
                                <Button variant="ghost" size="sm" asChild>
                                    <Link href="/admin/analytics">
                                        View All <ArrowRight className="h-4 w-4 ml-1" />
                                    </Link>
                                </Button>
                            </div>
                            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                                {quickActions.map((action, index) => (
                                    <Link 
                                        key={index} 
                                        href={action.href} 
                                        className="group block"
                                    >
                                        <div className="relative overflow-hidden rounded-2xl bg-white border border-gray-100 p-6 shadow-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:border-gray-200">
                                            {/* Decorative background pattern */}
                                            <div className="absolute top-0 right-0 w-20 h-20 opacity-10">
                                                <div className={`w-full h-full rounded-full ${action.color.replace('bg-', 'bg-gradient-to-br from-')} to-transparent`}></div>
                                            </div>
                                            
                                            {/* Icon container */}
                                            <div className="relative mb-4">
                                                <div className={`inline-flex p-3 rounded-2xl ${action.color} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                                                    <action.icon className="h-6 w-6 text-white" />
                                                </div>
                                            </div>
                                            
                                            {/* Content */}
                                            <div className="relative space-y-3">
                                                <h3 className="font-bold text-gray-900 group-hover:text-primary transition-colors duration-200">
                                                    {action.title}
                                                </h3>
                                                <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
                                                    {action.description}
                                                </p>
                                            </div>
                                            
                                            {/* Action indicator */}
                                            <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                                                    <ArrowRight className="h-4 w-4 text-primary" />
                                                </div>
                                            </div>
                                            
                                            {/* Subtle hover glow */}
                                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                        </div>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Disabled Quick Actions for Pending Users */}
                    {is_pending_approval && (
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div className="text-center sm:text-left">
                                    <h2 className="text-xl sm:text-2xl font-bold text-gray-400">Admin Features</h2>
                                    <p className="text-sm sm:text-base text-gray-500 mt-1">Available after account approval</p>
                                </div>
                            </div>
                            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                                {quickActions.map((action, index) => (
                                    <div 
                                        key={index} 
                                        className="relative overflow-hidden rounded-2xl bg-gray-100 border border-gray-200 p-4 sm:p-6 shadow-sm opacity-50 cursor-not-allowed"
                                    >
                                        {/* Icon container */}
                                        <div className="relative mb-3 sm:mb-4">
                                            <div className="inline-flex p-2 sm:p-3 rounded-2xl bg-gray-400 shadow-lg">
                                                <action.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                                            </div>
                                        </div>
                                        
                                        {/* Content */}
                                        <div className="relative space-y-2 sm:space-y-3">
                                            <h3 className="text-sm sm:text-base font-bold text-gray-500">
                                                {action.title}
                                            </h3>
                                            <p className="text-xs sm:text-sm text-gray-400 leading-relaxed line-clamp-2">
                                                {action.description}
                                            </p>
                                        </div>
                                        
                                        {/* Disabled overlay */}
                                        <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-2xl">
                                            <div className="text-center">
                                                <Shield className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400 mx-auto mb-1" />
                                                <p className="text-xs text-gray-500 font-medium">Requires Approval</p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Organization Nomination Link - Only show for organization admins */}
                    {!is_pending_approval && user_role === 'admin' && organization?.slug && (
                        <Card className="border-0 shadow-sm bg-gradient-to-r from-indigo-50 to-blue-50">
                            <CardContent className="p-4 sm:p-6">
                                <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:space-x-4 sm:space-y-0">
                                    <div className="p-3 bg-indigo-100 rounded-full flex-shrink-0 w-fit mx-auto sm:mx-0">
                                        <UserPlus className="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600" />
                                    </div>
                                    <div className="flex-1 text-center sm:text-left">
                                        <h3 className="text-base sm:text-lg font-semibold text-indigo-900 mb-2">
                                            Your Organization's Nomination Link
                                        </h3>
                                        <p className="text-sm sm:text-base text-indigo-800 mb-4">
                                            Share this personalized link with potential nominees to apply directly to your organization. 
                                            The application form will be branded with your organization's information.
                                        </p>
                                        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-3 sm:space-y-0 p-3 bg-white rounded-lg border border-indigo-200">
                                            <code className="flex-1 text-xs sm:text-sm text-gray-700 font-mono break-all sm:break-normal">
                                                {window.location.origin}/apply/{organization.slug}
                                            </code>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(`${window.location.origin}/apply/${organization.slug}`);
                                                    toast.success("Link copied!", {
                                                        description: "Your organization's nomination link has been copied to clipboard.",
                                                    });
                                                }}
                                                className="text-indigo-600 border-indigo-300 hover:bg-indigo-50 w-full sm:w-auto"
                                            >
                                                Copy Link
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                asChild
                                                className="text-indigo-600 border-indigo-300 hover:bg-indigo-50"
                                            >
                                                <Link href={`/apply/${organization.slug}`} target="_blank">
                                                    Preview
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Stats Grid - Only show if not pending approval */}
                    {!is_pending_approval && stats && (
                        <div className="space-y-4">
                            <h2 className="text-xl font-semibold text-gray-900">Overview Statistics</h2>
                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                                <Card className="border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                        <CardTitle className="text-sm font-medium text-gray-600">Total Votes</CardTitle>
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Vote className="h-5 w-5 text-blue-600" />
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="text-3xl font-bold text-gray-900">{stats?.total_votes.value}</div>
                                        <p className={`text-sm mt-2 ${getChangeColor(stats?.total_votes.change_type)}`}>
                                            {getChangeIcon(stats?.total_votes.change_type)} {stats?.total_votes.change}% from last period
                                        </p>
                                    </CardContent>
                                </Card>

                                <Card className="border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                        <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <CediSign className="h-5 w-5 text-green-600" />
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="text-3xl font-bold text-gray-900">{stats?.total_revenue.value}</div>
                                        <p className={`text-sm mt-2 ${getChangeColor(stats?.total_revenue.change_type)}`}>
                                            {getChangeIcon(stats?.total_revenue.change_type)} {stats?.total_revenue.change}% from last period
                                        </p>
                                    </CardContent>
                                </Card>

                                <Card className="border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                        <CardTitle className="text-sm font-medium text-gray-600">Active Nominees</CardTitle>
                                        <div className="p-2 bg-purple-100 rounded-lg">
                                            <Users className="h-5 w-5 text-purple-600" />
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="text-3xl font-bold text-gray-900">{stats?.active_nominees.value}</div>
                                        <p className="text-sm text-muted-foreground mt-2">{stats?.active_nominees.change}</p>
                                    </CardContent>
                                </Card>

                                <Card className="border-0 shadow-sm">
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                        <CardTitle className="text-sm font-medium text-gray-600">Active Categories</CardTitle>
                                        <div className="p-2 bg-yellow-100 rounded-lg">
                                            <Trophy className="h-5 w-5 text-yellow-600" />
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="text-3xl font-bold text-gray-900">{stats?.active_categories.value}</div>
                                        <p className="text-sm text-muted-foreground mt-2">{stats?.active_categories.change}</p>
                                    </CardContent>
                                </Card>

                                {stats?.total_organizations && (
                                    <Card className="border-0 shadow-sm md:col-span-2 lg:col-span-1">
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                            <CardTitle className="text-sm font-medium text-gray-600">Organizations</CardTitle>
                                            <div className="p-2 bg-indigo-100 rounded-lg">
                                                <Building2 className="h-5 w-5 text-indigo-600" />
                                            </div>
                                        </CardHeader>
                                        <CardContent className="pt-0">
                                            <div className="text-3xl font-bold text-gray-900">{stats.total_organizations.value}</div>
                                            <p className="text-sm text-muted-foreground mt-2">{stats.total_organizations.change}</p>
                                        </CardContent>
                                    </Card>
                                )}

                                {stats?.gross_revenue && user_role === 'super_admin' && (
                                    <Card className="border-0 shadow-sm md:col-span-2 lg:col-span-1">
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                                            <CardTitle className="text-sm font-medium text-gray-600">Gross Revenue (100%)</CardTitle>
                                            <div className="p-2 bg-emerald-100 rounded-lg">
                                                <CediSign className="h-5 w-5 text-emerald-600" />
                                            </div>
                                        </CardHeader>
                                        <CardContent className="pt-0">
                                            <div className="text-3xl font-bold text-gray-900">{stats.gross_revenue.value}</div>
                                            <p className={`text-sm mt-2 ${getChangeColor(stats.gross_revenue.change_type)}`}>
                                                {getChangeIcon(stats.gross_revenue.change_type)} {stats.gross_revenue.change}% from last period
                                            </p>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Data Tables */}
                    {!is_pending_approval && (
                    <div className="grid gap-6 lg:grid-cols-7">
                        <Card className="lg:col-span-3 border-0 shadow-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg font-semibold">Top Categories</CardTitle>
                                        <CardDescription>Best performing award categories</CardDescription>
                                    </div>
                                    <Button variant="ghost" size="sm" asChild>
                                        <Link href="/admin/categories">
                                            <Eye className="h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="space-y-4">
                                    {topCategories.map((category, index) => (
                                        <div key={category.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div className="flex items-center space-x-3">
                                                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm">
                                                    {index + 1}
                                                </div>
                                                <div className="space-y-1">
                                                    <p className="font-medium text-gray-900">{category.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {category.votes_count} votes • {category.price_per_vote}/vote
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-semibold text-gray-900">{category.total_revenue}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="lg:col-span-4 border-0 shadow-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg font-semibold">Top Nominees</CardTitle>
                                        <CardDescription>Leading nominees by vote count</CardDescription>
                                    </div>
                                    <Button variant="ghost" size="sm">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="space-y-4">
                                    {topNominees.map((nominee, index) => (
                                        <div key={nominee.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div className="flex items-center space-x-3">
                                                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm">
                                                    {index + 1}
                                                </div>
                                                <div className="flex items-center space-x-3">
                                                    <Avatar className="h-10 w-10">
                                                        <AvatarImage src={nominee.profile_image ? `/storage/${nominee.profile_image}` : undefined} />
                                                        <AvatarFallback className="text-xs">
                                                            {nominee.display_name.split(' ').map(n => n[0]).join('')}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div className="space-y-1">
                                                        <p className="font-medium text-gray-900">{nominee.display_name}</p>
                                                        {nominee.nominee_code && (
                                                            <div className="flex items-center gap-1">
                                                                <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs font-mono">{nominee.nominee_code}</span>
                                                            </div>
                                                        )}
                                                        <p className="text-sm text-muted-foreground">
                                                            {nominee.category} • {nominee.votes_count} votes
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-semibold text-gray-900">{nominee.votes_count} votes</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                    )}

                    {!is_pending_approval && (
                    <div className="grid gap-6 lg:grid-cols-2">
                        <Card className="border-0 shadow-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg font-semibold">Recent Activities</CardTitle>
                                        <CardDescription>Latest voting activities</CardDescription>
                                    </div>
                                    <Button variant="ghost" size="sm">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="space-y-4">
                                    {recentActivities.slice(0, 5).map((activity, index) => (
                                        <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                                            <div className="w-2 h-2 rounded-full bg-primary mt-3"></div>
                                            <div className="flex-1 space-y-2">
                                                <p className="text-sm text-gray-900">{activity.description}</p>
                                                <div className="flex items-center space-x-2">
                                                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                                                    {activity.amount && (
                                                        <Badge variant="outline" className="text-xs">{activity.amount}</Badge>
                                                    )}
                                                    <Badge variant={getStatusBadgeVariant(activity.status)} className="text-xs">
                                                        {activity.status}
                                                    </Badge>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg font-semibold">Recent Payments</CardTitle>
                                        <CardDescription>Latest payment transactions</CardDescription>
                                    </div>
                                    <Button variant="ghost" size="sm" asChild>
                                        <Link href="/admin/payments">
                                            <Eye className="h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="space-y-4">
                                    {recentPayments.slice(0, 5).map((payment) => (
                                        <div key={payment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div className="space-y-1">
                                                <p className="font-medium text-gray-900">{payment.reference}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {payment.nominee} • {payment.category}
                                                </p>
                                            </div>
                                            <div className="text-right space-y-1">
                                                <p className="font-semibold text-gray-900">{payment.amount}</p>
                                                <Badge variant={getStatusBadgeVariant(payment.status)} className="text-xs">
                                                    {payment.status}
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                    )}

                    {/* System Health (Super Admin Only) */}
                    {!is_pending_approval && systemHealth && user_role === 'super_admin' && (
                        <Card className="border-0 shadow-sm">
                            <CardHeader className="pb-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg font-semibold">System Health</CardTitle>
                                        <CardDescription>Overall system status and metrics</CardDescription>
                                    </div>
                                    <Button variant="ghost" size="sm" asChild>
                                        <Link href="/admin/settings">
                                            <Settings className="h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <div className="grid gap-6 md:grid-cols-4">
                                    <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                                        <p className="text-sm font-medium text-gray-600">Total Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{systemHealth.total_users.toLocaleString()}</p>
                                    </div>
                                    <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                                        <p className="text-sm font-medium text-gray-600">Organizations</p>
                                        <p className="text-2xl font-bold text-gray-900">{systemHealth.active_organizations}</p>
                                    </div>
                                    <div className="space-y-2 p-4 bg-yellow-50 rounded-lg">
                                        <p className="text-sm font-medium text-yellow-700">Pending Payments</p>
                                        <p className="text-2xl font-bold text-yellow-800">{systemHealth.pending_payments}</p>
                                    </div>
                                    <div className="space-y-2 p-4 bg-red-50 rounded-lg">
                                        <p className="text-sm font-medium text-red-700">Failed Payments</p>
                                        <p className="text-2xl font-bold text-red-800">{systemHealth.failed_payments}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
            <Toaster position="top-right" />
        </AppLayout>
    );
}