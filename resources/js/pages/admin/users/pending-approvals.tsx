import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    Clock,
    UserCheck,
    UserX,
    Search,
    Mail,
    Building2,
    Calendar,
    ArrowLeft,
    CheckCircle,
    XCircle,
    Users,
    Filter,
    MoreHorizontal,
    Eye,
    RefreshCw
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface UserData {
    id: number;
    slug: string;
    name: string;
    email: string;
    role: 'admin' | 'nominee';
    created_at: string;
    organization?: {
        id: number;
        name: string;
    };
}

interface Stats {
    total_pending: number;
    admin_pending: number;
    nominee_pending: number;
}

interface PendingApprovalsProps {
    users: {
        data: UserData[];
        total: number;
    };
    stats?: Stats;
}

export default function PendingApprovals({ users, stats }: PendingApprovalsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'nominee'>('all');
    const [showApproveDialog, setShowApproveDialog] = useState(false);
    const [showRejectDialog, setShowRejectDialog] = useState(false);
    const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
    const [approvalNotes, setApprovalNotes] = useState('');
    const [rejectionReason, setRejectionReason] = useState('');
    const [revenuePercentage, setRevenuePercentage] = useState('10');
    const [isProcessing, setIsProcessing] = useState(false);    const breadcrumbItems: BreadcrumbItem[] = [
        { title: 'Dashboard', href: route('admin.dashboard') },
        { title: 'Users', href: route('admin.users.index') },
        { title: 'Pending Approvals', href: route('admin.users.pending-approvals') },
    ];

    const filteredUsers = users?.data?.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            user.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesRole = roleFilter === 'all' || user.role === roleFilter;
        return matchesSearch && matchesRole;
    }) || [];    const handleApprove = (user: UserData) => {
        setSelectedUser(user);
        setApprovalNotes('');
        setRevenuePercentage('10'); // Reset to default
        setShowApproveDialog(true);
    };

    const handleReject = (user: UserData) => {
        setSelectedUser(user);
        setRejectionReason('');
        setShowRejectDialog(true);
    };    const confirmApprove = () => {
        if (!selectedUser) return;
        
        // Validate revenue percentage for organization admins
        const isOrganizationAdmin = selectedUser.role === 'admin' && selectedUser.organization;
        if (isOrganizationAdmin) {
            const percentage = parseFloat(revenuePercentage);
            if (isNaN(percentage) || percentage < 0 || percentage > 100) {
                alert('Please enter a valid revenue percentage between 0 and 100.');
                return;
            }
        }
        
        setIsProcessing(true);
        
        // Use different routes based on user type
        // Organization admins (role: admin with organization) need special handling for SMS
        const approvalRoute = isOrganizationAdmin 
            ? route('admin.pending-organizations.approve', selectedUser.slug)
            : route('admin.users.approve', selectedUser.slug);
        
        const requestData = isOrganizationAdmin 
            ? { revenue_percentage: parseFloat(revenuePercentage) }
            : { approval_notes: approvalNotes };
        
        router.post(approvalRoute, requestData, {
            preserveScroll: true,
            onSuccess: () => {
                setShowApproveDialog(false);
                setSelectedUser(null);
                setApprovalNotes('');
                setRevenuePercentage('10');
            },
            onFinish: () => setIsProcessing(false)
        });
    };    const confirmReject = () => {
        if (!selectedUser || !rejectionReason.trim()) return;
        
        setIsProcessing(true);
        
        // Use different routes based on user type
        const isOrganizationAdmin = selectedUser.role === 'admin' && selectedUser.organization;
        const rejectionRoute = isOrganizationAdmin 
            ? route('admin.pending-organizations.reject', selectedUser.slug)
            : route('admin.users.reject', selectedUser.slug);
        
        router.post(rejectionRoute, {
            rejection_reason: rejectionReason
        }, {
            preserveScroll: true,
            onSuccess: () => {
                setShowRejectDialog(false);
                setSelectedUser(null);
                setRejectionReason('');
            },
            onFinish: () => setIsProcessing(false)
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'admin':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            case 'nominee':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title="Pending User Approvals" />
            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
            
            <div className="space-y-6">
                {/* Header Section */}
                <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div>
                        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Pending User Approvals</h1>
                        <p className="text-muted-foreground text-sm sm:text-base">
                            Review and manage user registration requests awaiting approval
                        </p>
                    </div>
                    <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:gap-3 sm:space-y-0">
                        <Button variant="outline" onClick={() => window.location.reload()} className="w-full sm:w-auto">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh
                        </Button>
                        <Button asChild variant="outline" className="w-full sm:w-auto">
                            <Link href={route('admin.users.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Users
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xs sm:text-sm font-medium">Total Pending</CardTitle>
                            <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl sm:text-2xl font-bold">{users?.total || 0}</div>
                            <p className="text-xs text-muted-foreground">Awaiting approval</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xs sm:text-sm font-medium">Admin Requests</CardTitle>
                            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl sm:text-2xl font-bold">
                                {users?.data?.filter(u => u.role === 'admin').length || 0}
                            </div>
                            <p className="text-xs text-muted-foreground">Admin role requests</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xs sm:text-sm font-medium">Nominee Requests</CardTitle>
                            <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl sm:text-2xl font-bold">
                                {users?.data?.filter(u => u.role === 'nominee').length || 0}
                            </div>
                            <p className="text-xs text-muted-foreground">Nominee applications</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xs sm:text-sm font-medium">Avg. Wait Time</CardTitle>
                            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl sm:text-2xl font-bold">2.4</div>
                            <p className="text-xs text-muted-foreground">Days average</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters and Search */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filter & Search</CardTitle>
                        <CardDescription>
                            Use the controls below to filter and search through pending approval requests
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by name or email..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <div className="grid grid-cols-3 gap-2 sm:flex sm:gap-2">
                                <Button
                                    variant={roleFilter === 'all' ? 'default' : 'outline'}
                                    onClick={() => setRoleFilter('all')}
                                    size="sm"
                                    className="text-xs sm:text-sm"
                                >
                                    All Roles
                                </Button>
                                <Button
                                    variant={roleFilter === 'admin' ? 'default' : 'outline'}
                                    onClick={() => setRoleFilter('admin')}
                                    size="sm"
                                    className="text-xs sm:text-sm"
                                >
                                    Admins
                                </Button>
                                <Button
                                    variant={roleFilter === 'nominee' ? 'default' : 'outline'}
                                    onClick={() => setRoleFilter('nominee')}
                                    size="sm"
                                    className="text-xs sm:text-sm"
                                >
                                    Nominees
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Pending Users List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Pending Approval Requests</CardTitle>
                        <CardDescription>
                            {filteredUsers.length} of {users?.total || 0} pending requests
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {filteredUsers.length > 0 ? (
                            <div className="space-y-4">
                                {filteredUsers.map((user, index) => (
                                    <div key={user.id}>
                                        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 p-4 sm:p-6 border rounded-lg hover:bg-muted/50 transition-colors">
                                            <div className="flex items-center space-x-3 sm:space-x-4">
                                                <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                                                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                                        {user.name.charAt(0).toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className="space-y-1 min-w-0 flex-1">
                                                    <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:gap-3 sm:space-y-0">
                                                        <h4 className="font-semibold text-base sm:text-lg truncate">{user.name}</h4>
                                                        <Badge className={getRoleColor(user.role)}>
                                                            {user.role}
                                                        </Badge>
                                                    </div>
                                                    <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0 text-xs sm:text-sm text-muted-foreground">
                                                        <div className="flex items-center gap-1 truncate">
                                                            <Mail className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                            <span className="truncate">{user.email}</span>
                                                        </div>
                                                        {user.organization && (
                                                            <div className="flex items-center gap-1 truncate">
                                                                <Building2 className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                                <span className="truncate">{user.organization.name}</span>
                                                            </div>
                                                        )}
                                                        <div className="flex items-center gap-1">
                                                            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                            <span className="text-nowrap">{formatDate(user.created_at)}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:gap-2 sm:space-y-0">
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    asChild
                                                    className="w-full sm:w-auto"
                                                >
                                                    <Link href={route('admin.users.show', user.slug)}>
                                                        <Eye className="h-4 w-4 sm:mr-2" />
                                                        <span className="sm:inline hidden">View</span>
                                                    </Link>
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 w-full sm:w-auto"
                                                    onClick={() => handleReject(user)}
                                                >
                                                    <XCircle className="h-4 w-4 sm:mr-2" />
                                                    <span className="sm:inline hidden">Reject</span>
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
                                                    onClick={() => handleApprove(user)}
                                                >
                                                    <CheckCircle className="h-4 w-4 sm:mr-2" />
                                                    <span className="sm:inline hidden">Approve</span>
                                                </Button>
                                            </div>
                                        </div>
                                        {index < filteredUsers.length - 1 && <Separator className="my-4" />}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No Pending Approvals</h3>
                                <p className="text-muted-foreground mb-4">
                                    {searchTerm || roleFilter !== 'all' 
                                        ? 'No users match your current filters.' 
                                        : 'All user registration requests have been processed.'}
                                </p>
                                {(searchTerm || roleFilter !== 'all') && (
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setRoleFilter('all');
                                        }}
                                    >
                                        Clear Filters
                                    </Button>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>            </div>
            </div>
            </div>

            {/* Approve Dialog */}
            <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {selectedUser?.role === 'admin' && selectedUser?.organization 
                                ? 'Approve Organization Registration' 
                                : 'Approve User Registration'}
                        </DialogTitle>
                        <DialogDescription>
                            {selectedUser?.role === 'admin' && selectedUser?.organization ? (
                                <>
                                    You are about to approve the organization "{selectedUser.organization.name}" 
                                    and grant admin access to {selectedUser.name}. This will activate the organization 
                                    and send SMS/email notifications.
                                </>
                            ) : (
                                <>
                                    You are about to approve the registration for {selectedUser?.name}. 
                                    This will grant them access to the platform with the {selectedUser?.role} role.
                                </>
                            )}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        {selectedUser?.role === 'admin' && selectedUser?.organization ? (
                            <div className="space-y-2">
                                <Label htmlFor="revenue-percentage">Revenue Percentage *</Label>
                                <Input
                                    id="revenue-percentage"
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.1"
                                    placeholder="Enter revenue percentage (e.g., 10)"
                                    value={revenuePercentage}
                                    onChange={(e) => setRevenuePercentage(e.target.value)}
                                    required
                                />
                                <p className="text-sm text-muted-foreground">
                                    Set the revenue percentage that the platform will take from this organization's transactions.
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-2">
                                <Label htmlFor="approval-notes">Approval Notes (Optional)</Label>
                                <Textarea
                                    id="approval-notes"
                                    placeholder="Add any notes about this approval..."
                                    value={approvalNotes}
                                    onChange={(e) => setApprovalNotes(e.target.value)}
                                    rows={3}
                                />
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setShowApproveDialog(false)}
                            disabled={isProcessing}
                        >
                            Cancel
                        </Button>
                        <Button 
                            onClick={confirmApprove}
                            disabled={isProcessing || (selectedUser?.role === 'admin' && selectedUser?.organization && (!revenuePercentage || isNaN(parseFloat(revenuePercentage)) || parseFloat(revenuePercentage) < 0 || parseFloat(revenuePercentage) > 100))}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            {isProcessing ? (
                                <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Approving...
                                </>
                            ) : (
                                <>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Approve User
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Reject Dialog */}
            <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Reject User Registration</DialogTitle>
                        <DialogDescription>
                            You are about to reject the registration for {selectedUser?.name}. 
                            This action cannot be undone and the user will need to re-register.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="rejection-reason">Rejection Reason *</Label>
                            <Textarea
                                id="rejection-reason"
                                placeholder="Please provide a reason for rejecting this registration..."
                                value={rejectionReason}
                                onChange={(e) => setRejectionReason(e.target.value)}
                                rows={3}
                                required
                            />
                            <p className="text-sm text-muted-foreground">
                                This reason will be sent to the user via email.
                            </p>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setShowRejectDialog(false)}
                            disabled={isProcessing}
                        >
                            Cancel
                        </Button>
                        <Button 
                            onClick={confirmReject}
                            disabled={isProcessing || !rejectionReason.trim()}
                            variant="destructive"
                        >
                            {isProcessing ? (
                                <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Rejecting...
                                </>
                            ) : (
                                <>
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject User
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}