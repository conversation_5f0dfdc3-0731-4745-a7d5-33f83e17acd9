import React, { useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Mail, 
  Phone, 
  Building2, 
  Calendar,
  Edit,
  MoreHorizontal,
  Trophy,
  Award,
  Clock,
  ExternalLink,
  AlertCircle
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { type BreadcrumbItem } from '@/types';

interface UserData {
  id: number;
  name: string;
  email: string;
  slug: string;
  phone?: string;
  bio?: string;
  role: string;
  is_suspended: boolean;
  suspension_reason?: string;
  suspension_notice?: string;
  created_at: string;
  updated_at: string;
  organization?: {
    id: number;
    name: string;
    slug: string;
  };
  nominee_profiles?: any[];
  managed_categories?: any[];
}

interface Stats {
  total_votes_received: number;
  total_revenue_generated: number;
  account_created: string;
  last_login: string;
}

interface ShowUserProps {
  user: UserData;
  stats: Stats;
}

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'destructive';
    case 'admin':
      return 'default';
    case 'nominee':
      return 'secondary';
    default:
      return 'outline';
  }
};

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin';
    case 'admin':
      return 'Admin';
    case 'nominee':
      return 'Nominee';
    default:
      return role;
  }
};

export default function ShowUser({ user, stats }: ShowUserProps) {
  // Safety check
  if (!user || !stats) {
    return (
      <AppLayout>
        <Head title="User Details - Error" />
        <div className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Unable to load user data. Please try refreshing the page.
            </AlertDescription>
          </Alert>
        </div>
      </AppLayout>
    );
  }

  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Users', href: '/admin/users' },
    { title: user.name, href: `/admin/users/${user.slug}` },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${user.name} - User Details`} />
      
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
              <User className="h-8 w-8 text-gray-500" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                {user.name}
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleLabel(user.role)}
                </Badge>
                {user.is_suspended && (
                  <Badge variant="destructive">Suspended</Badge>
                )}
              </h1>
              <p className="text-muted-foreground">
                User Details and Activity
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button asChild>
              <Link href={route('admin.users.edit', user.slug)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Link href={route('admin.users.index')}>
                    Back to Users
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Suspension Notice */}
        {user.is_suspended && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800">Account Suspended</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-red-700">
                  <strong>Reason:</strong> {user.suspension_reason}
                </p>
                {user.suspension_notice && (
                  <p className="text-red-700">
                    <strong>Notice:</strong> {user.suspension_notice}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Votes Received</CardTitle>
              <Trophy className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_votes_received.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                As nominee
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue Generated</CardTitle>
                              <CediSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">GHS {stats.total_revenue_generated.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                From votes received
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Account Age</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.floor((new Date().getTime() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24))} days
              </div>
              <p className="text-xs text-muted-foreground">
                Since {stats.account_created}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Email</h4>
                  <div className="mt-1 flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <a href={`mailto:${user.email}`} className="text-blue-600 hover:underline">
                      {user.email}
                    </a>
                  </div>
                </div>

                {user.phone && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Phone</h4>
                    <div className="mt-1 flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <a href={`tel:${user.phone}`} className="text-blue-600 hover:underline">
                        {user.phone}
                      </a>
                    </div>
                  </div>
                )}
              </div>

              {user.organization && (
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Organization</h4>
                  <div className="mt-1 flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-gray-400" />
                    <Link 
                      href={route('admin.organizations.show', user.organization.slug)}
                      className="text-blue-600 hover:underline"
                    >
                      {user.organization.name}
                    </Link>
                  </div>
                </div>
              )}

              {user.bio && (
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Bio</h4>
                  <p className="mt-1 text-gray-900">{user.bio}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Created</h4>
                  <div className="mt-1 flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{stats.account_created}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Last Login</h4>
                  <div className="mt-1 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{stats.last_login}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activity Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Role</span>
                  <Badge variant={getRoleBadgeVariant(user.role)}>
                    {getRoleLabel(user.role)}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Account Status</span>
                  <Badge variant={user.is_suspended ? 'destructive' : 'default'}>
                    {user.is_suspended ? 'Suspended' : 'Active'}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Nominee Profiles</span>
                  <span className="text-sm text-gray-600">{user.nominee_profiles?.length || 0}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Managed Categories</span>
                  <span className="text-sm text-gray-600">{user.managed_categories?.length || 0}</span>
                </div>

                {stats.total_votes_received > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Avg Revenue per Vote</span>
                    <span className="text-sm text-gray-600">
                      GHS {(stats.total_revenue_generated / stats.total_votes_received).toFixed(2)}
                    </span>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Performance</span>
                  <Badge variant={
                    stats.total_votes_received > 100 ? 'default' :
                    stats.total_votes_received > 50 ? 'secondary' : 'outline'
                  }>
                    {stats.total_votes_received > 100 ? 'High Performer' :
                     stats.total_votes_received > 50 ? 'Active' : 'New User'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        {((user.nominee_profiles?.length || 0) > 0 || (user.managed_categories?.length || 0) > 0) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {(user.nominee_profiles?.length || 0) > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Nominee Profiles</CardTitle>
                  <CardDescription>
                    Categories where this user is nominated with voting links
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {user.nominee_profiles?.slice(0, 5).map((profile: any) => (
                      <div key={profile.id} className="p-3 rounded border bg-gray-50">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-start gap-3 flex-1">
                            {/* Profile Image */}
                            <div className="w-12 h-12 rounded-full overflow-hidden border border-gray-200 flex-shrink-0">
                              {profile.profile_image ? (
                                <img 
                                  src={`/storage/${profile.profile_image}`} 
                                  alt={profile.display_name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                                  <User className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">{profile.display_name}</h4>
                              {/* Short Code Display */}
                              {profile.nominee_code && (
                                <div className="text-sm font-mono bg-blue-50 text-blue-700 px-2 py-1 rounded mt-1 inline-block">
                                  Code: {profile.nominee_code}
                                </div>
                              )}
                              <div className="text-sm text-gray-600 mt-1">
                                <div className="flex items-center gap-1">
                                  <Trophy className="h-3 w-3" />
                                  {profile.total_votes || 0} votes
                                </div>
                                {profile.award_categories && profile.award_categories.length > 0 && (
                                  <div className="mt-1">
                                    <span className="text-xs text-gray-500">Categories: </span>
                                    {profile.award_categories
                                      .filter((category: any) => category && category.name)
                                      .map((category: any, index: number, filteredArray: any[]) => (
                                      <span key={category.id || index} className="text-xs">
                                        {category.name}{index < filteredArray.length - 1 ? ', ' : ''}
                                      </span>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={profile.is_approved ? 'default' : 'secondary'}>
                              {profile.is_approved ? 'Approved' : 'Pending'}
                            </Badge>
                          </div>
                        </div>
                        {profile.is_approved && profile.slug && (
                          <div className="mt-2 pt-2 border-t border-gray-200">
                            <a 
                              href={`/vote/nominee/${profile.slug}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                            >
                              <ExternalLink className="h-3 w-3" />
                              Open Voting Page
                            </a>
                            <div className="text-xs text-gray-500 mt-1">
                              URL: {window.location.origin}/vote/{profile.slug}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                    {(user.nominee_profiles?.length || 0) > 5 && (
                      <p className="text-sm text-gray-500 text-center">
                        And {(user.nominee_profiles?.length || 0) - 5} more...
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {(user.managed_categories?.length || 0) > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Managed Categories</CardTitle>
                  <CardDescription>
                    Categories managed by this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {user.managed_categories?.slice(0, 5).map((category: any) => (
                      <div key={category.id} className="flex justify-between items-center p-2 rounded border">
                        <span className="font-medium">{category.name}</span>
                        <Badge variant={category.is_active ? 'default' : 'secondary'}>
                          {category.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    ))}
                    {(user.managed_categories?.length || 0) > 5 && (
                      <p className="text-sm text-gray-500 text-center">
                        And {(user.managed_categories?.length || 0) - 5} more...
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  );
} 