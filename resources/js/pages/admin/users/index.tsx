import { Head, <PERSON>, router } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Users as UsersIcon,
    UserPlus,
    Search,
    Filter,
    Download,
    RefreshCw,
    MoreHorizontal,
    Edit,
    Trash2,
    UserCheck,
    UserX,
    Mail,
    Phone,
    Calendar,
    Building2,
    Shield,
    Eye,
    Clock,
    ThumbsUp,
    ThumbsDown,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    slug: string;
    role?: 'super_admin' | 'admin' | 'nominee';
    is_suspended: boolean;
    suspension_reason?: string;
    approval_status: 'pending' | 'approved' | 'rejected';
    approved_at?: string;
    rejection_reason?: string;
    organization?: {
        id: number;
        name: string;
    };
    nominee_profiles?: Array<{
        id: number;
        display_name: string;
        nominee_code?: string;
        profile_image?: string;
    }>;
    created_at: string;
    last_login?: string;
    email_verified_at?: string;
    phone?: string;
}

interface Stats {
    total_users: number;
    active_users: number;
    super_admins: number;
    suspended_users: number;
    pending_approval: number;
    rejected_users: number;
}

interface UsersProps {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    filters: {
        search?: string;
        role?: string;
        status?: string;
        organization_id?: number;
    };
    user_role: string;
    organizations?: Array<{ id: number; name: string }>;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Users', href: '/admin/users' },
];

export default function Users({ users, filters, user_role, organizations, stats }: UsersProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const getRoleBadgeVariant = (role: string | undefined) => {
        switch (role) {
            case 'super_admin':
                return 'destructive';
            case 'admin':
                return 'default';
            case 'nominee':
                return 'secondary';
            default:
                return 'outline';
        }
    };

    const getStatusBadgeVariant = (is_suspended: boolean) => {
        return is_suspended ? 'destructive' : 'default';
    };

    const getStatusIcon = (is_suspended: boolean) => {
        return is_suspended ? 
            <XCircle className="h-4 w-4 text-red-600" /> : 
            <CheckCircle className="h-4 w-4 text-green-600" />;
    };

    const getRoleIcon = (role: string | undefined) => {
        return role === 'super_admin' || role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <UsersIcon className="h-4 w-4 text-blue-600" />;
    };

    const getApprovalIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <ThumbsUp className="h-4 w-4 text-green-600" />;
            case 'rejected':
                return <ThumbsDown className="h-4 w-4 text-red-600" />;
            case 'pending':
            default:
                return <Clock className="h-4 w-4 text-orange-600" />;
        }
    };

    const getApprovalBadgeVariant = (status: string) => {
        switch (status) {
            case 'approved':
                return 'default';
            case 'rejected':
                return 'destructive';
            case 'pending':
            default:
                return 'secondary';
        }
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.users.index'), { ...filters, search: searchTerm }, { preserveState: true });
    };

    const handleToggleStatus = (userSlug: string) => {
        router.post(route('admin.users.toggle-status', userSlug), {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Users - Admin Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                User Management
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Manage system users and their permissions
                            </p>
                        </div>
                        {/* <div className="flex flex-wrap items-center gap-3">
                            <Button variant="outline" size="sm" className="h-9">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                            <Button asChild>
                                <Link href={route('admin.users.create')}>
                                    <UserPlus className="h-4 w-4 mr-2" />
                                    Add User
                                </Link>
                            </Button>
                        </div> */}
                    </div>

                    {/* Stats Overview */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-6">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-blue-100 rounded-xl">
                                        <UsersIcon className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-green-100 rounded-xl">
                                        <UserCheck className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.active_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-purple-100 rounded-xl">
                                        <Shield className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Super Admins</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.super_admins}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-red-100 rounded-xl">
                                        <UserX className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Suspended</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.suspended_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-orange-100 rounded-xl">
                                        <Clock className="h-6 w-6 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Pending</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.pending_approval}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gray-100 rounded-xl">
                                        <ThumbsDown className="h-6 w-6 text-gray-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Rejected</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.rejected_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions for Approvals */}
                    {stats.pending_approval > 0 && (
                        <Card className="border-0 shadow-sm bg-orange-50 border-orange-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="p-3 bg-orange-100 rounded-xl">
                                            <AlertCircle className="h-6 w-6 text-orange-600" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-orange-900">
                                                {stats.pending_approval} User{stats.pending_approval !== 1 ? 's' : ''} Awaiting Approval
                                            </h3>
                                            <p className="text-orange-700">Review and approve pending user registrations</p>
                                        </div>
                                    </div>
                                    <Button asChild variant="outline" className="border-orange-300 text-orange-700 hover:bg-orange-100">
                                        <Link href={route('admin.users.pending-approvals')}>
                                            <Clock className="h-4 w-4 mr-2" />
                                            Review Pending
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Search and Filters */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex items-center space-x-4">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search users by name, email, or organization..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Users Table */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Users</CardTitle>
                            <CardDescription>
                                {users.total} user{users.total !== 1 ? 's' : ''} found
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">User</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Organization</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Role</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Approval</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Joined</th>
                                            <th className="text-right p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-100">
                                        {users.data.map((user) => (
                                            <tr key={user.id} className="hover:bg-gray-50/50 transition-colors">
                                                <td className="p-4">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                                            {user.name.charAt(0).toUpperCase()}
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-900">{user.name}</p>
                                                            <p className="text-sm text-gray-600 flex items-center gap-1">
                                                                <Mail className="h-3 w-3" />
                                                                {user.email}
                                                            </p>
                                                            {user.role === 'nominee' && user.nominee_profiles && user.nominee_profiles.length > 0 && (
                                                                <p className="text-xs text-blue-600 font-mono bg-blue-50 px-1 rounded mt-1 inline-block">
                                                                    Code: {user.nominee_profiles[0].nominee_code}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    {user.organization ? (
                                                        <div className="flex items-center gap-2">
                                                            <Building2 className="h-4 w-4 text-gray-500" />
                                                            <span className="text-sm text-gray-900">{user.organization.name}</span>
                                                        </div>
                                                    ) : (
                                                        <span className="text-sm text-gray-500">No organization</span>
                                                    )}
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getRoleIcon(user.role)}
                                                        <Badge 
                                                            variant={getRoleBadgeVariant(user.role)}
                                                            className={
                                                                user.role === 'super_admin' 
                                                                    ? 'bg-red-100 text-red-800 border-red-200'
                                                                    : user.role === 'admin'
                                                                    ? 'bg-purple-100 text-purple-800 border-purple-200' 
                                                                    : 'bg-blue-100 text-blue-800 border-blue-200'
                                                            }
                                                        >
                                                            {user.role === 'super_admin' ? 'Super Admin' : 
                                                             user.role === 'admin' ? 'Admin' : 'Nominee'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getStatusIcon(user.is_suspended)}
                                                        <Badge 
                                                            variant={getStatusBadgeVariant(user.is_suspended)}
                                                            className={user.is_suspended 
                                                                ? 'bg-red-100 text-red-800 border-red-200' 
                                                                : 'bg-green-100 text-green-800 border-green-200'
                                                            }
                                                        >
                                                            {user.is_suspended ? 'Suspended' : 'Active'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getApprovalIcon(user.approval_status)}
                                                        <Badge 
                                                            variant={getApprovalBadgeVariant(user.approval_status)}
                                                            className={
                                                                user.approval_status === 'approved' 
                                                                    ? 'bg-green-100 text-green-800 border-green-200'
                                                                    : user.approval_status === 'rejected'
                                                                    ? 'bg-red-100 text-red-800 border-red-200'
                                                                    : 'bg-orange-100 text-orange-800 border-orange-200'
                                                            }
                                                        >
                                                            {user.approval_status === 'approved' ? 'Approved' : 
                                                             user.approval_status === 'rejected' ? 'Rejected' : 'Pending'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Calendar className="h-3 w-3" />
                                                        {new Date(user.created_at).toLocaleDateString()}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            asChild
                                                        >
                                                            <Link href={route('admin.users.show', user.slug)}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            asChild
                                                        >
                                                            <Link href={route('admin.users.edit', user.slug)}>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleToggleStatus(user.slug)}
                                                        >
                                                            {user.is_suspended ? 
                                                                <UserCheck className="h-4 w-4 text-green-600" /> :
                                                                <UserX className="h-4 w-4 text-red-600" />
                                                            }
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {users.total === 0 && (
                                <div className="text-center py-12">
                                    <UsersIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                                    <p className="text-gray-600 mb-6">Get started by adding your first user.</p>
                                    <Button asChild>
                                        <Link href={route('admin.users.create')}>
                                            <UserPlus className="h-4 w-4 mr-2" />
                                            Add User
                                        </Link>
                                    </Button>
                                </div>
                            )}

                            {/* Pagination would go here */}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 