import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle, Save, User, Mail, Lock, Building2, Phone, FileText, ArrowLeft, Shield } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { FormEventHandler } from 'react';

interface Organization {
    id: number;
    name: string;
}

interface CreateUserProps {
    organizations: Organization[];
    user_role: string;
}

interface UserFormData {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: 'admin' | 'nominee' | 'super_admin';
    organization_id?: number;
    phone?: string;
    bio?: string;
    send_welcome_email: boolean;
    [key: string]: any;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Users', href: '/admin/users' },
    { title: 'Create User', href: '/admin/users/create' },
];

export default function CreateUser({ organizations, user_role }: CreateUserProps) {
    const { data, setData, post, processing, errors, reset } = useForm<UserFormData>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'nominee',
        organization_id: undefined,
        phone: '',
        bio: '',
        send_welcome_email: true,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post('/admin/users', {
            onSuccess: () => {
                reset();
            },
        });
    };

    const roleOptions = user_role === 'super_admin' 
        ? [
            { value: 'super_admin', label: 'Super Admin', description: 'Full system access' },
            { value: 'admin', label: 'Admin', description: 'Organization management' },
            { value: 'nominee', label: 'Nominee', description: 'Basic user access' },
        ]
        : [
            { value: 'admin', label: 'Admin', description: 'Organization management' },
            { value: 'nominee', label: 'Nominee', description: 'Basic user access' },
        ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create User - Admin Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <div className="flex items-center gap-3">
                                <Button variant="ghost" size="sm" asChild>
                                    <Link href="/admin/users">
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to Users
                                    </Link>
                                </Button>
                            </div>
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Create New User
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Add a new user to the system
                            </p>
                        </div>
                    </div>

                    <form onSubmit={submit} className="space-y-8">
                        <div className="grid gap-8 lg:grid-cols-3">
                            {/* Main Form */}
                            <div className="lg:col-span-2 space-y-8">
                                {/* Basic Information */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <User className="h-5 w-5 text-blue-600" />
                                            Basic Information
                                        </CardTitle>
                                        <CardDescription>
                                            Enter the user's basic details
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid gap-6 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="name">Full Name *</Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={data.name}
                                                    onChange={(e) => setData('name', e.target.value)}
                                                    placeholder="Enter full name"
                                                    className={errors.name ? 'border-red-500' : ''}
                                                />
                                                {errors.name && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-3 w-3" />
                                                        {errors.name}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="email">Email Address *</Label>
                                                <div className="relative">
                                                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                                    <Input
                                                        id="email"
                                                        type="email"
                                                        value={data.email}
                                                        onChange={(e) => setData('email', e.target.value)}
                                                        placeholder="<EMAIL>"
                                                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                                                    />
                                                </div>
                                                {errors.email && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-3 w-3" />
                                                        {errors.email}
                                                    </p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <div className="relative">
                                                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                                <Input
                                                    id="phone"
                                                    type="tel"
                                                    value={data.phone}
                                                    onChange={(e) => setData('phone', e.target.value)}
                                                    placeholder="+****************"
                                                    className="pl-10"
                                                />
                                            </div>
                                            {errors.phone && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-3 w-3" />
                                                    {errors.phone}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="bio">Bio</Label>
                                            <Textarea
                                                id="bio"
                                                value={data.bio}
                                                onChange={(e) => setData('bio', e.target.value)}
                                                placeholder="Brief description about the user..."
                                                rows={3}
                                            />
                                            {errors.bio && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-3 w-3" />
                                                    {errors.bio}
                                                </p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Security */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Lock className="h-5 w-5 text-red-600" />
                                            Security
                                        </CardTitle>
                                        <CardDescription>
                                            Set up login credentials
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid gap-6 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="password">Password *</Label>
                                                <Input
                                                    id="password"
                                                    type="password"
                                                    value={data.password}
                                                    onChange={(e) => setData('password', e.target.value)}
                                                    placeholder="Enter password"
                                                    className={errors.password ? 'border-red-500' : ''}
                                                />
                                                {errors.password && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-3 w-3" />
                                                        {errors.password}
                                                    </p>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="password_confirmation">Confirm Password *</Label>
                                                <Input
                                                    id="password_confirmation"
                                                    type="password"
                                                    value={data.password_confirmation}
                                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                                    placeholder="Confirm password"
                                                    className={errors.password_confirmation ? 'border-red-500' : ''}
                                                />
                                                {errors.password_confirmation && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-3 w-3" />
                                                        {errors.password_confirmation}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Sidebar */}
                            <div className="space-y-8">
                                {/* Role & Permissions */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Shield className="h-5 w-5 text-purple-600" />
                                            Role & Permissions
                                        </CardTitle>
                                        <CardDescription>
                                            Set user role and access level
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="role">User Role *</Label>
                                            <Select value={data.role} onValueChange={(value: any) => setData('role', value)}>
                                                <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select role" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {roleOptions.map((role) => (
                                                        <SelectItem key={role.value} value={role.value}>
                                                            <div className="flex flex-col">
                                                                <span className="font-medium">{role.label}</span>
                                                                <span className="text-xs text-gray-500">{role.description}</span>
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.role && (
                                                <p className="text-sm text-red-600 flex items-center gap-1">
                                                    <AlertCircle className="h-3 w-3" />
                                                    {errors.role}
                                                </p>
                                            )}
                                        </div>

                                        {(data.role === 'admin' || data.role === 'nominee') && (
                                            <div className="space-y-2">
                                                <Label htmlFor="organization_id">Organization</Label>
                                                <div className="relative">
                                                    <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />                                                    <Select 
                                                        value={data.organization_id?.toString()} 
                                                        onValueChange={(value) => {
                                                            setData('organization_id', parseInt(value));
                                                        }}
                                                    >
                                                        <SelectTrigger className={`pl-10 ${errors.organization_id ? 'border-red-500' : ''}`}>
                                                            <SelectValue placeholder="Select organization" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {organizations.map((org) => (
                                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                                    {org.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                                {errors.organization_id && (
                                                    <p className="text-sm text-red-600 flex items-center gap-1">
                                                        <AlertCircle className="h-3 w-3" />
                                                        {errors.organization_id}
                                                    </p>
                                                )}
                                            </div>
                                        )}
                                    </CardContent>                                </Card>

                                {/* Options */}
                                <Card className="border-0 shadow-sm">
                                    <CardHeader>
                                        <CardTitle>Options</CardTitle>
                                        <CardDescription>
                                            Additional settings
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="send_welcome_email"
                                                checked={data.send_welcome_email}
                                                onCheckedChange={(checked) => setData('send_welcome_email', !!checked)}
                                            />
                                            <Label htmlFor="send_welcome_email" className="text-sm">
                                                Send welcome email
                                            </Label>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Actions */}
                                <Card className="border-0 shadow-sm">
                                    <CardContent className="p-6">
                                        <div className="space-y-4">
                                            <Button type="submit" className="w-full" disabled={processing}>
                                                <Save className="h-4 w-4 mr-2" />
                                                {processing ? 'Creating User...' : 'Create User'}
                                            </Button>
                                            <Button type="button" variant="outline" className="w-full" asChild>
                                                <Link href="/admin/users">
                                                    Cancel
                                                </Link>
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
} 