import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    ArrowLeft,
    Save,
    Eye,
    EyeOff,
    Shield,
    User,
    Mail,
    Phone,
    Lock,
    FileText,
    AlertCircle,
    AlertTriangle,
    UserX
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface UserData {
    id: number;
    slug: string;
    name: string;
    email: string;
    phone?: string;
    bio?: string;
    role: 'admin' | 'nominee';
    is_suspended: boolean;
    suspension_reason?: string;
    organization: {
        id: number;
        name: string;
        slug: string;
    };
}

interface Organization {
    id: number;
    name: string;
    slug: string;
}

interface Role {
    value: string;
    label: string;
}

interface EditUserProps {
    user: UserData;
    organization: Organization;
    roles: Role[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organization Users', href: '/admin/org-users' },
];

export default function EditUser({ user, organization, roles }: EditUserProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        name: user.name || '',
        email: user.email || '',
        password: '',
        password_confirmation: '',
        role: user.role || '',
        phone: user.phone || '',
        bio: user.bio || '',
        is_suspended: user.is_suspended || false,
        suspension_reason: user.suspension_reason || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.org-users.update', user.slug), {
            onSuccess: () => {
                // Reset password fields on success
                setData(prev => ({
                    ...prev,
                    password: '',
                    password_confirmation: ''
                }));
            }
        });
    };

    const getRoleIcon = (role: string) => {
        return role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <User className="h-4 w-4 text-blue-600" />;
    };

    // Update breadcrumbs with user name
    const userBreadcrumbs = [
        ...breadcrumbs,
        { title: user.name, href: `/admin/org-users/${user.slug}` },
        { title: 'Edit', href: `/admin/org-users/${user.slug}/edit` }
    ];

    return (
        <AppLayout breadcrumbs={userBreadcrumbs}>
            <Head title={`Edit ${user.name} - ${organization.name} - Admin Dashboard`} />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-3">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href={`/admin/org-users/${user.slug}`}>
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to User
                                    </Link>
                                </Button>
                            </div>
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Edit User
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Update {user.name}'s information and settings
                            </p>
                        </div>
                    </div>

                    {/* User Info Card */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-3">
                                <div className="p-3 bg-blue-100 rounded-xl">
                                    <User className="h-6 w-6 text-blue-600" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        Editing: {user.name}
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        Organization: {organization.name}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Edit User Form */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-6">
                            <CardTitle className="text-xl font-semibold">User Information</CardTitle>
                            <CardDescription>
                                Update the user's details. Leave password fields empty to keep current password.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Basic Information */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="name" className="flex items-center space-x-2">
                                            <User className="h-4 w-4" />
                                            <span>Full Name *</span>
                                        </Label>
                                        <Input
                                            id="name"
                                            type="text"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Enter full name"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-600">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email" className="flex items-center space-x-2">
                                            <Mail className="h-4 w-4" />
                                            <span>Email Address *</span>
                                        </Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="Enter email address"
                                            className={errors.email ? 'border-red-500' : ''}
                                        />
                                        {errors.email && (
                                            <p className="text-sm text-red-600">{errors.email}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Password Fields */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="password" className="flex items-center space-x-2">
                                            <Lock className="h-4 w-4" />
                                            <span>New Password</span>
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="password"
                                                type={showPassword ? 'text' : 'password'}
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Leave empty to keep current password"
                                                className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password && (
                                            <p className="text-sm text-red-600">{errors.password}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation" className="flex items-center space-x-2">
                                            <Lock className="h-4 w-4" />
                                            <span>Confirm New Password</span>
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="password_confirmation"
                                                type={showPasswordConfirmation ? 'text' : 'password'}
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Confirm new password"
                                                className={errors.password_confirmation ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                                            >
                                                {showPasswordConfirmation ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password_confirmation && (
                                            <p className="text-sm text-red-600">{errors.password_confirmation}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Role and Phone */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="role" className="flex items-center space-x-2">
                                            <Shield className="h-4 w-4" />
                                            <span>Role *</span>
                                        </Label>
                                        <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a role" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {roles.map((role) => (
                                                    <SelectItem key={role.value} value={role.value}>
                                                        <div className="flex items-center space-x-2">
                                                            {getRoleIcon(role.value)}
                                                            <span>{role.label}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.role && (
                                            <p className="text-sm text-red-600">{errors.role}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone" className="flex items-center space-x-2">
                                            <Phone className="h-4 w-4" />
                                            <span>Phone Number</span>
                                        </Label>
                                        <Input
                                            id="phone"
                                            type="tel"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="Enter phone number"
                                            className={errors.phone ? 'border-red-500' : ''}
                                        />
                                        {errors.phone && (
                                            <p className="text-sm text-red-600">{errors.phone}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Bio */}
                                <div className="space-y-2">
                                    <Label htmlFor="bio" className="flex items-center space-x-2">
                                        <FileText className="h-4 w-4" />
                                        <span>Bio</span>
                                    </Label>
                                    <Textarea
                                        id="bio"
                                        value={data.bio}
                                        onChange={(e) => setData('bio', e.target.value)}
                                        placeholder="Enter a brief bio (optional)"
                                        rows={4}
                                        className={errors.bio ? 'border-red-500' : ''}
                                    />
                                    {errors.bio && (
                                        <p className="text-sm text-red-600">{errors.bio}</p>
                                    )}
                                </div>

                                {/* Suspension Settings */}
                                <Card className="border border-orange-200 bg-orange-50/50">
                                    <CardHeader className="pb-4">
                                        <CardTitle className="text-lg font-semibold text-orange-800 flex items-center space-x-2">
                                            <UserX className="h-5 w-5" />
                                            <span>Account Status</span>
                                        </CardTitle>
                                        <CardDescription className="text-orange-700">
                                            Manage user suspension and access controls
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="is_suspended"
                                                checked={data.is_suspended}
                                                onCheckedChange={(checked) => setData('is_suspended', checked as boolean)}
                                            />
                                            <Label htmlFor="is_suspended" className="text-sm font-medium">
                                                Suspend this user
                                            </Label>
                                        </div>
                                        
                                        {data.is_suspended && (
                                            <div className="space-y-2">
                                                <Label htmlFor="suspension_reason" className="flex items-center space-x-2">
                                                    <AlertTriangle className="h-4 w-4" />
                                                    <span>Suspension Reason</span>
                                                </Label>
                                                <Textarea
                                                    id="suspension_reason"
                                                    value={data.suspension_reason}
                                                    onChange={(e) => setData('suspension_reason', e.target.value)}
                                                    placeholder="Enter reason for suspension"
                                                    rows={3}
                                                    className={errors.suspension_reason ? 'border-red-500' : ''}
                                                />
                                                {errors.suspension_reason && (
                                                    <p className="text-sm text-red-600">{errors.suspension_reason}</p>
                                                )}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Warning for role change */}
                                {data.role !== user.role && (
                                    <Alert>
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>
                                            <strong>Warning:</strong> Changing the user's role will affect their access permissions. 
                                            {data.role === 'admin' && ' They will gain administrative privileges.'}
                                            {data.role === 'nominee' && ' They will lose administrative privileges.'}
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {/* Submit Buttons */}
                                <div className="flex flex-col space-y-3 pt-6 md:flex-row md:justify-end md:space-y-0 md:space-x-3">
                                    <Button variant="outline" asChild>
                                        <Link href={`/admin/org-users/${user.slug}`}>
                                            Cancel
                                        </Link>
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        <Save className="h-4 w-4 mr-2" />
                                        {processing ? 'Updating User...' : 'Update User'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 