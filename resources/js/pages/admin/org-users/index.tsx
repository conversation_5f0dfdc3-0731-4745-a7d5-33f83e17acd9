import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
    Users,
    Plus,
    Search,
    Filter,
    Download,
    RefreshCw,
    Edit,
    Eye,
    UserCheck,
    UserX,
    Shield,
    User,
    Mail,
    Calendar,
    MoreHorizontal,
    CheckCircle,
    XCircle,
    AlertCircle,
    Clock,
    ThumbsUp,
    ThumbsDown,
    ImageIcon,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface UserData {
    id: number;
    slug: string;
    name: string;
    email: string;
    phone?: string;
    role: 'admin' | 'nominee';
    is_suspended: boolean;
    suspension_reason?: string;
    approval_status: 'pending' | 'approved' | 'rejected';
    approved_at?: string;
    rejection_reason?: string;
    created_at: string;
    organization: {
        id: number;
        name: string;
    };
    nominee_profiles?: {
        id: number;
        display_name: string;
        profile_image?: string;
        nominee_code?: string;
    }[];
}

interface Organization {
    id: number;
    name: string;
    slug: string;
}

interface PaginationLink {
    url?: string;
    label: string;
    active: boolean;
}

interface Stats {
    total_users: number;
    active_users: number;
    admins: number;
    nominees: number;
    suspended_users: number;
    pending_approval: number;
    rejected_users: number;
}

interface OrgUsersProps {
    users: {
        data: UserData[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
        links: PaginationLink[];
    };
    organization: Organization;
    filters: {
        search?: string;
        role?: string;
        status?: string;
    };
    roles: Array<{ value: string; label: string }>;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organization Users', href: '/admin/org-users' },
];

export default function OrgUsers({ users, organization, filters, roles, stats }: OrgUsersProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const getStatusBadgeVariant = (is_suspended: boolean) => {
        return is_suspended ? 'destructive' : 'default';
    };

    const getStatusIcon = (is_suspended: boolean) => {
        return is_suspended ? 
            <XCircle className="h-4 w-4 text-red-600" /> : 
            <CheckCircle className="h-4 w-4 text-green-600" />;
    };

    const getRoleIcon = (role: string) => {
        return role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <User className="h-4 w-4 text-blue-600" />;
    };

    const getApprovalIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <ThumbsUp className="h-4 w-4 text-green-600" />;
            case 'rejected':
                return <ThumbsDown className="h-4 w-4 text-red-600" />;
            case 'pending':
            default:
                return <Clock className="h-4 w-4 text-orange-600" />;
        }
    };

    const getApprovalBadgeVariant = (status: string) => {
        switch (status) {
            case 'approved':
                return 'default';
            case 'rejected':
                return 'destructive';
            case 'pending':
            default:
                return 'secondary';
        }
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/org-users', { ...filters, search: searchTerm }, { preserveState: true });
    };

    const handleToggleStatus = (userSlug: string) => {
        router.post(`/admin/org-users/${userSlug}/toggle-status`, {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    const handleDownloadImage = (profileImage: string, userName: string) => {
        const imageUrl = `/storage/${profileImage}`;
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `${userName.replace(/\s+/g, '_')}_profile_image.${profileImage.split('.').pop() || 'jpg'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${organization.name} Users - Admin Dashboard`} />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                {organization.name} Users
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Manage users in your organization
                            </p>
                        </div>
                    </div>

                    {/* Stats Overview */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-blue-100 rounded-xl">
                                        <Users className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-green-100 rounded-xl">
                                        <UserCheck className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Users</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.active_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-purple-100 rounded-xl">
                                        <Shield className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Admins</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.admins}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-yellow-100 rounded-xl">
                                        <User className="h-6 w-6 text-yellow-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Nominees</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.nominees}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-red-100 rounded-xl">
                                        <UserX className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Suspended</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.suspended_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Additional Stats Row for Approvals */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-orange-100 rounded-xl">
                                        <Clock className="h-6 w-6 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Pending Approval</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.pending_approval}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gray-100 rounded-xl">
                                        <ThumbsDown className="h-6 w-6 text-gray-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Rejected</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.rejected_users}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-center">
                                    <Button asChild variant="outline" className="w-full">
                                        <Link href="/admin/org-users/pending/approvals">
                                            <Clock className="h-4 w-4 mr-2" />
                                            Review Pending ({stats.pending_approval})
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Search and Filters */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex items-center space-x-4">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search users by name, email, or phone..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Users Table */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Users</CardTitle>
                            <CardDescription>
                                {users.total} user{users.total !== 1 ? 's' : ''} found
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">User</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Role</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Approval</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Joined</th>
                                            <th className="text-right p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-100">
                                        {users.data.map((user) => (
                                            <tr key={user.id} className="hover:bg-gray-50/50 transition-colors">
                                                <td className="p-4">
                                                    <div className="flex items-center space-x-3">
                                                        {user.role === 'nominee' && user.nominee_profiles?.[0]?.profile_image ? (
                                                            <div className="relative">
                                                                <Avatar className="h-10 w-10">
                                                                    <AvatarImage src={`/storage/${user.nominee_profiles[0].profile_image}`} />
                                                                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium">
                                                                        {user.name.charAt(0).toUpperCase()}
                                                                    </AvatarFallback>
                                                                </Avatar>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="absolute -top-1 -right-1 h-6 w-6 p-0 bg-white border border-gray-200 rounded-full shadow-sm hover:bg-gray-50"
                                                                    onClick={() => handleDownloadImage(user.nominee_profiles![0].profile_image!, user.name)}
                                                                    title="Download profile image"
                                                                >
                                                                    <Download className="h-3 w-3 text-gray-600" />
                                                                </Button>
                                                            </div>
                                                        ) : (
                                                            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                                                {user.name.charAt(0).toUpperCase()}
                                                            </div>
                                                        )}
                                                        <div>
                                                            <p className="font-medium text-gray-900">{user.name}</p>
                                                            {user.role === 'nominee' && user.nominee_profiles?.[0]?.nominee_code && (
                                                                <div className="flex items-center gap-1 mb-1">
                                                                    <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs font-mono">{user.nominee_profiles[0].nominee_code}</span>
                                                                </div>
                                                            )}
                                                            <p className="text-sm text-gray-600 flex items-center gap-1">
                                                                <Mail className="h-3 w-3" />
                                                                {user.email}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getRoleIcon(user.role)}
                                                        <Badge 
                                                            variant={user.role === 'admin' ? 'default' : 'secondary'}
                                                            className={user.role === 'admin' 
                                                                ? 'bg-purple-100 text-purple-800 border-purple-200' 
                                                                : 'bg-blue-100 text-blue-800 border-blue-200'
                                                            }
                                                        >
                                                            {user.role === 'admin' ? 'Admin' : 'Nominee'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getStatusIcon(user.is_suspended)}
                                                        <Badge 
                                                            variant={getStatusBadgeVariant(user.is_suspended)}
                                                            className={user.is_suspended 
                                                                ? 'bg-red-100 text-red-800 border-red-200' 
                                                                : 'bg-green-100 text-green-800 border-green-200'
                                                            }
                                                        >
                                                            {user.is_suspended ? 'Suspended' : 'Active'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getApprovalIcon(user.approval_status)}
                                                        <Badge 
                                                            variant={getApprovalBadgeVariant(user.approval_status)}
                                                            className={
                                                                user.approval_status === 'approved' 
                                                                    ? 'bg-green-100 text-green-800 border-green-200'
                                                                    : user.approval_status === 'rejected'
                                                                    ? 'bg-red-100 text-red-800 border-red-200'
                                                                    : 'bg-orange-100 text-orange-800 border-orange-200'
                                                            }
                                                        >
                                                            {user.approval_status === 'approved' ? 'Approved' : 
                                                             user.approval_status === 'rejected' ? 'Rejected' : 'Pending'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Calendar className="h-3 w-3" />
                                                        {new Date(user.created_at).toLocaleDateString()}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            asChild
                                                        >
                                                            <Link href={`/admin/org-users/${user.slug}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            asChild
                                                        >
                                                            <Link href={`/admin/org-users/${user.slug}/edit`}>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleToggleStatus(user.slug)}
                                                        >
                                                            {user.is_suspended ? 
                                                                <UserCheck className="h-4 w-4 text-green-600" /> :
                                                                <UserX className="h-4 w-4 text-red-600" />
                                                            }
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {users.total === 0 && (
                                <div className="text-center py-12">
                                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                                    <p className="text-gray-600 mb-6">Get started by adding your first user.</p>
                                    <Button asChild>
                                        <Link href="/admin/org-users/create">
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add User
                                        </Link>
                                    </Button>
                                </div>
                            )}

                            {/* Pagination */}
                            {users.last_page > 1 && (
                                <div className="flex items-center justify-between bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                                    <div className="text-sm text-gray-600 font-medium">
                                        Showing <span className="font-bold text-blue-600">{users.from || ((users.current_page - 1) * users.per_page) + 1}</span> to{' '}
                                        <span className="font-bold text-blue-600">{users.to || Math.min(users.current_page * users.per_page, users.total)}</span> of{' '}
                                        <span className="font-bold text-blue-600">{users.total}</span> results
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        {users.links?.length ? (
                                            // Laravel paginator links
                                            users.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? "default" : "outline"}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.visit(link.url)}
                                                    className={link.active 
                                                        ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg" 
                                                        : "hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                                                    }
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))
                                        ) : (
                                            // Fallback manual pagination
                                            <>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    disabled={users.current_page <= 1}
                                                    onClick={() => {
                                                        router.get('/admin/org-users', { 
                                                            ...filters, 
                                                            page: users.current_page - 1 
                                                        }, { preserveState: true });
                                                    }}
                                                    className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                                                >
                                                    <ChevronLeft className="h-4 w-4 mr-1" />
                                                    Previous
                                                </Button>
                                                
                                                {/* Page numbers */}
                                                {Array.from({ length: Math.min(5, users.last_page) }, (_, i) => {
                                                    const pageNum = Math.max(1, users.current_page - 2) + i;
                                                    if (pageNum > users.last_page) return null;
                                                    
                                                    return (
                                                        <Button
                                                            key={pageNum}
                                                            variant={pageNum === users.current_page ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => {
                                                                router.get('/admin/org-users', { 
                                                                    ...filters, 
                                                                    page: pageNum 
                                                                }, { preserveState: true });
                                                            }}
                                                            className={pageNum === users.current_page
                                                                ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                                                                : "hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                                                            }
                                                        >
                                                            {pageNum}
                                                        </Button>
                                                    );
                                                })}
                                                
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    disabled={users.current_page >= users.last_page}
                                                    onClick={() => {
                                                        router.get('/admin/org-users', { 
                                                            ...filters, 
                                                            page: users.current_page + 1 
                                                        }, { preserveState: true });
                                                    }}
                                                    className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                                                >
                                                    Next
                                                    <ChevronRight className="h-4 w-4 ml-1" />
                                                </Button>
                                            </>
                                        )}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 