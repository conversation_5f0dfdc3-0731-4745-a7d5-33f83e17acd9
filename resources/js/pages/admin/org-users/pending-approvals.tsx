import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Users,
    Search,
    Filter,
    Download,
    RefreshCw,
    ArrowLeft,
    Clock,
    ThumbsUp,
    ThumbsDown,
    Mail,
    Calendar,
    Building2,
    Shield,
    User,
    AlertCircle,
    CheckCircle,
    XCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface UserData {
    id: number;
    slug: string;
    name: string;
    email: string;
    phone?: string;
    role: 'admin' | 'nominee';
    approval_status: 'pending';
    created_at: string;
    organization?: {
        id: number;
        name: string;
    };
}

interface Organization {
    id: number;
    name: string;
}

interface PendingApprovalsProps {
    users: {
        data: UserData[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    organization: Organization;
    filters: {
        search?: string;
        role?: string;
    };
    roles: Array<{ value: string; label: string }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organization Users', href: '/admin/org-users' },
    { title: 'Pending Approvals', href: '/admin/org-users/pending/approvals' },
];

export default function PendingApprovals({ users, organization, filters, roles }: PendingApprovalsProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const getRoleIcon = (role: string) => {
        return role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <User className="h-4 w-4 text-blue-600" />;
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/org-users/pending/approvals', { ...filters, search: searchTerm }, { preserveState: true });
    };

    const handleApprove = (userSlug: string) => {
        router.post(`/admin/org-users/${userSlug}/approve`, {
            approval_notes: '',
        }, {
            preserveScroll: true,
        });
    };

    const handleReject = (userSlug: string) => {
        const reason = prompt('Please provide a reason for rejection:');
        if (!reason) return;

        router.post(`/admin/org-users/${userSlug}/reject`, {
            rejection_reason: reason,
        }, {
            preserveScroll: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Pending Approvals - ${organization.name}`} />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <div className="flex items-center gap-3">
                                <Button variant="ghost" size="sm" asChild>
                                    <Link href="/admin/org-users">
                                        <ArrowLeft className="h-4 w-4" />
                                    </Link>
                                </Button>
                                <div>
                                    <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                        Pending User Approvals
                                    </h1>
                                    <p className="text-lg text-muted-foreground">
                                        Review and approve user registrations for {organization.name}
                                    </p>
                                </div>
                            </div>
                        </div>
                        {/* <div className="flex flex-wrap items-center gap-3">
                            <Button variant="outline" size="sm" className="h-9">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                        </div> */}
                    </div>

                    {/* Stats Overview */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-6">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-orange-100 rounded-xl">
                                        <Clock className="h-6 w-6 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Pending Approvals</p>
                                        <p className="text-2xl font-bold text-gray-900">{users.total}</p>
                                    </div>
                                </div>
                                {users.total > 0 && (
                                    <div className="flex items-center space-x-2 text-orange-700 bg-orange-50 px-3 py-2 rounded-lg">
                                        <AlertCircle className="h-4 w-4" />
                                        <span className="text-sm font-medium">
                                            {users.total} user{users.total !== 1 ? 's' : ''} awaiting your review
                                        </span>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Search and Filters */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex items-center space-x-4">
                                <div className="flex-1 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        placeholder="Search pending users by name, email..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Pending Users Table */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle>Pending User Registrations</CardTitle>
                            <CardDescription>
                                {users.total} user{users.total !== 1 ? 's' : ''} awaiting approval for {organization.name}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">User</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Role</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Registration Date</th>
                                            <th className="text-right p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-100">
                                        {users.data.map((user) => (
                                            <tr key={user.id} className="hover:bg-gray-50/50 transition-colors">
                                                <td className="p-4">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center text-white font-medium">
                                                            {user.name.charAt(0).toUpperCase()}
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-900">{user.name}</p>
                                                            <p className="text-sm text-gray-600 flex items-center gap-1">
                                                                <Mail className="h-3 w-3" />
                                                                {user.email}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getRoleIcon(user.role)}
                                                        <Badge 
                                                            variant={user.role === 'admin' ? 'default' : 'secondary'}
                                                            className={user.role === 'admin' 
                                                                ? 'bg-purple-100 text-purple-800 border-purple-200' 
                                                                : 'bg-blue-100 text-blue-800 border-blue-200'
                                                            }
                                                        >
                                                            {user.role === 'admin' ? 'Admin' : 'Nominee'}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-1 text-sm text-gray-600">
                                                        <Calendar className="h-3 w-3" />
                                                        {new Date(user.created_at).toLocaleDateString()}
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleApprove(user.slug)}
                                                            className="border-green-300 text-green-700 hover:bg-green-50"
                                                        >
                                                            <ThumbsUp className="h-4 w-4 mr-1" />
                                                            Approve
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleReject(user.slug)}
                                                            className="border-red-300 text-red-700 hover:bg-red-50"
                                                        >
                                                            <ThumbsDown className="h-4 w-4 mr-1" />
                                                            Reject
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {users.total === 0 && (
                                <div className="text-center py-12">
                                    <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
                                    <p className="text-gray-600 mb-6">There are no pending user approvals for {organization.name} at this time.</p>
                                    <Button asChild variant="outline">
                                        <Link href="/admin/org-users">
                                            <ArrowLeft className="h-4 w-4 mr-2" />
                                            Back to Users
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 