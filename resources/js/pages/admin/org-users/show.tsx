import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
    ArrowLeft,
    Edit,
    Shield,
    User,
    Mail,
    Phone,
    Calendar,
    Building2,
    CheckCircle,
    XCircle,
    Clock,
    AlertTriangle,
    Eye,
    Vote,
    Trophy,
    Activity,
    MoreHorizontal,
    UserCheck,
    UserX,
    ThumbsUp,
    ThumbsDown,
    FileText,
    ExternalLink
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState, useEffect } from 'react';

interface NomineeProfile {
    id: number;
    slug?: string;
    display_name: string;
    bio?: string;
    profile_image?: string;
    nominee_code?: string;
    votes_count: number;
    awardCategories?: {
        id: number;
        name: string;
        slug: string;
    }[];
}

interface UserData {
    id: number;
    slug: string;
    name: string;
    email: string;
    phone?: string;
    bio?: string;
    role: 'admin' | 'nominee';
    is_suspended: boolean;
    suspension_reason?: string;
    approval_status: 'pending' | 'approved' | 'rejected';
    approved_at?: string;
    rejection_reason?: string;
    email_verified_at?: string;
    created_at: string;
    updated_at: string;
    organization: {
        id: number;
        name: string;
        slug: string;
    };
    nominee_profiles?: NomineeProfile[];
}

interface Organization {
    id: number;
    name: string;
    slug: string;
}

interface ShowUserProps {
    user: UserData;
    organization: Organization;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organization Users', href: '/admin/org-users' },
];

export default function ShowUser({ user, organization }: ShowUserProps) {
    const [isToggling, setIsToggling] = useState(false);
    
    // Safety check
    if (!user || !organization) {
        return (
            <AppLayout>
                <Head title="User Details - Error" />
                <div className="p-6">
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            Unable to load user data. Please try refreshing the page.
                        </AlertDescription>
                    </Alert>
                </div>
            </AppLayout>
        );
    }

    const getStatusBadgeVariant = (is_suspended: boolean) => {
        return is_suspended ? 'destructive' : 'default';
    };

    const getStatusIcon = (is_suspended: boolean) => {
        return is_suspended ? 
            <XCircle className="h-4 w-4 text-red-600" /> : 
            <CheckCircle className="h-4 w-4 text-green-600" />;
    };

    const getRoleIcon = (role: string) => {
        return role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <User className="h-4 w-4 text-blue-600" />;
    };

    const getApprovalIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <ThumbsUp className="h-4 w-4 text-green-600" />;
            case 'rejected':
                return <ThumbsDown className="h-4 w-4 text-red-600" />;
            case 'pending':
            default:
                return <Clock className="h-4 w-4 text-orange-600" />;
        }
    };

    const getApprovalBadgeVariant = (status: string) => {
        switch (status) {
            case 'approved':
                return 'default';
            case 'rejected':
                return 'destructive';
            case 'pending':
            default:
                return 'secondary';
        }
    };

    const handleToggleStatus = () => {
        setIsToggling(true);
        router.post(`/admin/org-users/${user.slug}/toggle-status`, {}, {
            preserveScroll: true,
            onFinish: () => setIsToggling(false)
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    };

    // Update breadcrumbs with user name
    const userBreadcrumbs = [
        ...breadcrumbs,
        { title: user.name, href: `/admin/org-users/${user.slug}` }
    ];

    return (
        <AppLayout breadcrumbs={userBreadcrumbs}>
            <Head title={`${user.name} - ${organization.name} - Admin Dashboard`} />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-3">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin/org-users">
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to Users
                                    </Link>
                                </Button>
                            </div>
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                User Details
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Manage user information and settings
                            </p>
                        </div>
                        <div className="flex flex-wrap items-center gap-3">
                            <Button
                                variant={user.is_suspended ? "default" : "destructive"}
                                size="sm"
                                onClick={handleToggleStatus}
                                disabled={isToggling}
                            >
                                {user.is_suspended ? (
                                    <>
                                        <UserCheck className="h-4 w-4 mr-2" />
                                        {isToggling ? 'Activating...' : 'Activate User'}
                                    </>
                                ) : (
                                    <>
                                        <UserX className="h-4 w-4 mr-2" />
                                        {isToggling ? 'Suspending...' : 'Suspend User'}
                                    </>
                                )}
                            </Button>
                            <Button asChild>
                                <Link href={`/admin/org-users/${user.slug}/edit`}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit User
                                </Link>
                            </Button>
                        </div>
                    </div>

                    {/* User Profile Card */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <div className="flex flex-col space-y-6 md:flex-row md:space-y-0 md:space-x-6">
                                <div className="flex-shrink-0">
                                    <Avatar className="h-24 w-24">
                                        <AvatarImage src={user.nominee_profiles?.[0]?.profile_image} />
                                        <AvatarFallback className="text-lg font-semibold">
                                            {getInitials(user.name)}
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                                <div className="flex-1 space-y-4">
                                    <div>
                                        <h2 className="text-2xl font-bold text-gray-900">{user.name}</h2>
                                        <div className="flex flex-wrap items-center gap-3 mt-2">
                                            <Badge variant="outline" className="flex items-center space-x-1">
                                                {getRoleIcon(user.role)}
                                                <span className="capitalize">{user.role}</span>
                                            </Badge>
                                            <Badge variant={getStatusBadgeVariant(user.is_suspended)} className="flex items-center space-x-1">
                                                {getStatusIcon(user.is_suspended)}
                                                <span>{user.is_suspended ? 'Suspended' : 'Active'}</span>
                                            </Badge>
                                            <Badge variant={getApprovalBadgeVariant(user.approval_status)} className="flex items-center space-x-1">
                                                {getApprovalIcon(user.approval_status)}
                                                <span className="capitalize">{user.approval_status}</span>
                                            </Badge>
                                            {user.email_verified_at && (
                                                <Badge variant="outline" className="flex items-center space-x-1">
                                                    <CheckCircle className="h-3 w-3 text-green-600" />
                                                    <span>Email Verified</span>
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                    {user.bio && (
                                        <p className="text-gray-600">{user.bio}</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="grid gap-8 lg:grid-cols-2">
                        {/* Contact Information */}
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Mail className="h-5 w-5" />
                                    <span>Contact Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <Mail className="h-4 w-4 text-gray-500" />
                                    <div>
                                        <p className="text-sm text-gray-600">Email</p>
                                        <p className="font-medium">{user.email}</p>
                                    </div>
                                </div>
                                {user.phone && (
                                    <div className="flex items-center space-x-3">
                                        <Phone className="h-4 w-4 text-gray-500" />
                                        <div>
                                            <p className="text-sm text-gray-600">Phone</p>
                                            <p className="font-medium">{user.phone}</p>
                                        </div>
                                    </div>
                                )}
                                <div className="flex items-center space-x-3">
                                    <Building2 className="h-4 w-4 text-gray-500" />
                                    <div>
                                        <p className="text-sm text-gray-600">Organization</p>
                                        <p className="font-medium">{user.organization.name}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Account Information */}
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Activity className="h-5 w-5" />
                                    <span>Account Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-4 w-4 text-gray-500" />
                                    <div>
                                        <p className="text-sm text-gray-600">Member Since</p>
                                        <p className="font-medium">{formatDate(user.created_at)}</p>
                                    </div>
                                </div>
                                {user.approved_at && (
                                    <div className="flex items-center space-x-3">
                                        <CheckCircle className="h-4 w-4 text-gray-500" />
                                        <div>
                                            <p className="text-sm text-gray-600">Approved On</p>
                                            <p className="font-medium">{formatDate(user.approved_at)}</p>
                                        </div>
                                    </div>
                                )}
                                {user.email_verified_at && (
                                    <div className="flex items-center space-x-3">
                                        <Mail className="h-4 w-4 text-gray-500" />
                                        <div>
                                            <p className="text-sm text-gray-600">Email Verified</p>
                                            <p className="font-medium">{formatDate(user.email_verified_at)}</p>
                                        </div>
                                    </div>
                                )}
                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-4 w-4 text-gray-500" />
                                    <div>
                                        <p className="text-sm text-gray-600">Last Updated</p>
                                        <p className="font-medium">{formatDate(user.updated_at)}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Suspension Information */}
                    {user.is_suspended && (
                        <Card className="border-0 shadow-sm border-red-200 bg-red-50/50">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2 text-red-700">
                                    <AlertTriangle className="h-5 w-5" />
                                    <span>Suspension Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <p className="text-sm text-red-600">Reason for Suspension:</p>
                                    <p className="font-medium text-red-700">
                                        {user.suspension_reason || 'No reason provided'}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Rejection Information */}
                    {user.approval_status === 'rejected' && user.rejection_reason && (
                        <Card className="border-0 shadow-sm border-red-200 bg-red-50/50">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2 text-red-700">
                                    <XCircle className="h-5 w-5" />
                                    <span>Rejection Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <p className="text-sm text-red-600">Reason for Rejection:</p>
                                    <p className="font-medium text-red-700">{user.rejection_reason}</p>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Nominee Profiles */}
                    {user.role === 'nominee' && user.nominee_profiles && user.nominee_profiles.length > 0 && (
                        <Card className="border-0 shadow-sm">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Trophy className="h-5 w-5" />
                                    <span>Nominee Profiles</span>
                                </CardTitle>
                                <CardDescription>
                                    Categories this user is nominated in
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {user.nominee_profiles.map((profile) => (
                                        <div key={profile.id} className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between p-4 border rounded-lg space-y-3 sm:space-y-0">
                                            {/* Left Side: Nominee Info */}
                                            <div className="flex items-center space-x-4 flex-grow">
                                                <Avatar className="h-12 w-12 flex-shrink-0">
                                                    <AvatarImage src={profile.profile_image} />
                                                    <AvatarFallback>
                                                        {getInitials(profile.display_name)}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className="min-w-0">
                                                    <h4 className="font-semibold truncate" title={profile.display_name}>{profile.display_name}</h4>
                                                    {profile.nominee_code && (
                                                        <div className="flex items-center gap-2 text-xs text-blue-600 font-medium mt-1">
                                                            <span className="px-2 py-1 bg-blue-100 rounded text-xs font-mono">{profile.nominee_code}</span>
                                                        </div>
                                                    )}
                                                    <p className="text-sm text-gray-600">
                                                        {profile.awardCategories && profile.awardCategories.length > 0 
                                                            ? profile.awardCategories.map(cat => cat.name).join(', ') 
                                                            : 'No category assigned'}
                                                    </p>
                                                    {profile.bio && (
                                                        <p className="text-sm text-gray-500 mt-1 truncate">{profile.bio}</p>
                                                    )}
                                                </div>
                                            </div>
                                            {/* Right Side: Votes and Actions */}
                                            <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto flex-shrink-0 pt-3 sm:pt-0 border-t sm:border-t-0">
                                                <div className="text-center sm:text-left">
                                                    <p className="text-2xl font-bold text-blue-600">{profile.votes_count}</p>
                                                    <p className="text-xs text-gray-500">Votes</p>
                                                </div>                                                {profile.awardCategories && profile.awardCategories.length > 0 && profile.slug ? (
                                                    <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                                                        <Link href={`/vote/nominee/${profile.slug}`}>
                                                            <ExternalLink className="h-4 w-4 mr-2" />
                                                            View Voting Page
                                                        </Link>
                                                    </Button>
                                                ) : (
                                                    <Button variant="outline" size="sm" disabled className="w-full sm:w-auto">
                                                        <ExternalLink className="h-4 w-4 mr-2" />
                                                        Not Available
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 