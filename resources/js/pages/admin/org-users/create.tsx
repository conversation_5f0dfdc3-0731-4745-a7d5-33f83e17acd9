import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    UserPlus,
    ArrowLeft,
    Save,
    Eye,
    EyeOff,
    Shield,
    User,
    Mail,
    Phone,
    Lock,
    FileText,
    AlertCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface Organization {
    id: number;
    name: string;
    slug: string;
}

interface Role {
    value: string;
    label: string;
}

interface CreateUserProps {
    organization: Organization;
    roles: Role[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organization Users', href: '/admin/org-users' },
    { title: 'Create User', href: '/admin/org-users/create' },
];

export default function CreateUser({ organization, roles }: CreateUserProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: '',
        phone: '',
        bio: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.org-users.store'), {
            onSuccess: () => {
                reset();
            }
        });
    };

    const getRoleIcon = (role: string) => {
        return role === 'admin' ? 
            <Shield className="h-4 w-4 text-purple-600" /> : 
            <User className="h-4 w-4 text-blue-600" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Create User - ${organization.name} - Admin Dashboard`} />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-3">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin/org-users">
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to Users
                                    </Link>
                                </Button>
                            </div>
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Create New User
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Add a new user to {organization.name}
                            </p>
                        </div>
                    </div>

                    {/* Organization Info */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-3">
                                <div className="p-3 bg-blue-100 rounded-xl">
                                    <UserPlus className="h-6 w-6 text-blue-600" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        Adding user to: {organization.name}
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        The user will be automatically assigned to your organization
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Create User Form */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader className="pb-6">
                            <CardTitle className="text-xl font-semibold">User Information</CardTitle>
                            <CardDescription>
                                Fill in the details for the new user. They will receive login credentials via email.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Basic Information */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="name" className="flex items-center space-x-2">
                                            <User className="h-4 w-4" />
                                            <span>Full Name *</span>
                                        </Label>
                                        <Input
                                            id="name"
                                            type="text"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Enter full name"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-red-600">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email" className="flex items-center space-x-2">
                                            <Mail className="h-4 w-4" />
                                            <span>Email Address *</span>
                                        </Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="Enter email address"
                                            className={errors.email ? 'border-red-500' : ''}
                                        />
                                        {errors.email && (
                                            <p className="text-sm text-red-600">{errors.email}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Password Fields */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="password" className="flex items-center space-x-2">
                                            <Lock className="h-4 w-4" />
                                            <span>Password *</span>
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="password"
                                                type={showPassword ? 'text' : 'password'}
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Enter password"
                                                className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password && (
                                            <p className="text-sm text-red-600">{errors.password}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation" className="flex items-center space-x-2">
                                            <Lock className="h-4 w-4" />
                                            <span>Confirm Password *</span>
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="password_confirmation"
                                                type={showPasswordConfirmation ? 'text' : 'password'}
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Confirm password"
                                                className={errors.password_confirmation ? 'border-red-500 pr-10' : 'pr-10'}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                                            >
                                                {showPasswordConfirmation ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                        {errors.password_confirmation && (
                                            <p className="text-sm text-red-600">{errors.password_confirmation}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Role and Phone */}
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="role" className="flex items-center space-x-2">
                                            <Shield className="h-4 w-4" />
                                            <span>Role *</span>
                                        </Label>
                                        <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a role" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {roles.map((role) => (
                                                    <SelectItem key={role.value} value={role.value}>
                                                        <div className="flex items-center space-x-2">
                                                            {getRoleIcon(role.value)}
                                                            <span>{role.label}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.role && (
                                            <p className="text-sm text-red-600">{errors.role}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone" className="flex items-center space-x-2">
                                            <Phone className="h-4 w-4" />
                                            <span>Phone Number</span>
                                        </Label>
                                        <Input
                                            id="phone"
                                            type="tel"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="Enter phone number"
                                            className={errors.phone ? 'border-red-500' : ''}
                                        />
                                        {errors.phone && (
                                            <p className="text-sm text-red-600">{errors.phone}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Bio */}
                                <div className="space-y-2">
                                    <Label htmlFor="bio" className="flex items-center space-x-2">
                                        <FileText className="h-4 w-4" />
                                        <span>Bio</span>
                                    </Label>
                                    <Textarea
                                        id="bio"
                                        value={data.bio}
                                        onChange={(e) => setData('bio', e.target.value)}
                                        placeholder="Enter a brief bio (optional)"
                                        rows={4}
                                        className={errors.bio ? 'border-red-500' : ''}
                                    />
                                    {errors.bio && (
                                        <p className="text-sm text-red-600">{errors.bio}</p>
                                    )}
                                </div>

                                {/* Info Alert */}
                                <Alert>
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        Users created by organization admins are automatically approved and can log in immediately.
                                        They will receive an email notification with their login credentials.
                                    </AlertDescription>
                                </Alert>

                                {/* Submit Buttons */}
                                <div className="flex flex-col space-y-3 pt-6 md:flex-row md:justify-end md:space-y-0 md:space-x-3">
                                    <Button variant="outline" asChild>
                                        <Link href="/admin/org-users">
                                            Cancel
                                        </Link>
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        <Save className="h-4 w-4 mr-2" />
                                        {processing ? 'Creating User...' : 'Create User'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 