import React, { useState } from 'react';
import { Head, useForm, Link, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, Building2, Mail, Phone, Globe, Upload, Save } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

interface Organization {
  id: number;
  slug: string;
  name: string;
  description: string;
  contact_email: string;
  contact_phone?: string;
  website?: string;
  logo?: string;
  is_active: boolean;
}

interface EditOrganizationProps {
  organization: Organization;
}

export default function EditOrganization({ organization }: EditOrganizationProps) {
  const { data, setData, put, processing, errors } = useForm<{
    name: string;
    description: string;
    contact_email: string;
    contact_phone: string;
    website: string;
    logo: File | null;
    remove_logo: boolean;
  }>({
    name: organization.name,
    description: organization.description,
    contact_email: organization.contact_email,
    contact_phone: organization.contact_phone || '',
    website: organization.website || '',
    logo: null,
    remove_logo: false,
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(
    organization.logo ? `/storage/${organization.logo}` : null
  );
  const [showCurrentLogo, setShowCurrentLogo] = useState<boolean>(!!organization.logo);

  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organizations', href: '/admin/organizations' },
    { title: organization.name, href: `/admin/organizations/${organization.slug}` },
    { title: 'Edit', href: `/admin/organizations/${organization.slug}/edit` },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    router.post(route('admin.organizations.update', organization.slug), {
      ...data,
      _method: 'PUT',
    }, {
      forceFormData: true,
      onSuccess: () => {
        // Handle success - redirect is handled by backend
      },
      onError: (errors) => {
        // Form submission errors handled by form validation
      }
    });
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (2MB max)
      if (file.size > 2048 * 1024) {
        alert('File size must be less than 2MB');
        e.target.value = '';
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        e.target.value = '';
        return;
      }

      setData('logo', file);
      setData('remove_logo', false);
      setShowCurrentLogo(false);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.onerror = () => {
        alert('Error reading file');
        setLogoPreview(null);
      };
      reader.readAsDataURL(file);
    } else {
      setData('logo', null);
      if (organization.logo && !data.remove_logo) {
        setLogoPreview(`/storage/${organization.logo}`);
        setShowCurrentLogo(true);
      } else {
        setLogoPreview(null);
        setShowCurrentLogo(false);
      }
    }
  };

  const removeLogo = () => {
    setData('logo', null);
    setData('remove_logo', true);
    setLogoPreview(null);
    setShowCurrentLogo(false);
    
    // Clear file input
    const fileInput = document.getElementById('logo') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const restoreLogo = () => {
    setData('remove_logo', false);
    if (organization.logo) {
      setLogoPreview(`/storage/${organization.logo}`);
      setShowCurrentLogo(true);
    }
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Edit ${organization.name}`} />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-800">Edit Organization</h1>
            <p className="text-gray-600 mt-1">
              Update {organization.name} information
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-blue-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Building2 className="h-5 w-5 text-blue-600" />
                  Organization Details
                </CardTitle>
                <CardDescription className="text-blue-600">
                  Update the organization information
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Organization Name *</Label>
                      <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        placeholder="Enter organization name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="contact_email">Contact Email *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contact_email"
                          type="email"
                          value={data.contact_email}
                          onChange={(e) => setData('contact_email', e.target.value)}
                          placeholder="<EMAIL>"
                          className={`pl-10 ${errors.contact_email ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.contact_email && (
                        <p className="text-sm text-red-500">{errors.contact_email}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Describe the organization's purpose and activities"
                      rows={4}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contact_phone">Contact Phone</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contact_phone"
                          value={data.contact_phone}
                          onChange={(e) => setData('contact_phone', e.target.value)}
                          placeholder="+****************"
                          className={`pl-10 ${errors.contact_phone ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.contact_phone && (
                        <p className="text-sm text-red-500">{errors.contact_phone}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="website"
                          type="url"
                          value={data.website}
                          onChange={(e) => setData('website', e.target.value)}
                          placeholder="https://organization.com"
                          className={`pl-10 ${errors.website ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.website && (
                        <p className="text-sm text-red-500">{errors.website}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      asChild
                      className="border-gray-300 hover:border-gray-400 hover:bg-gray-50 hover:shadow-md transition-all duration-200"
                    >
                      <Link href={route('admin.organizations.show', organization.slug)}>
                        Cancel
                      </Link>
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={processing} 
                      className={`transition-all duration-200 hover:shadow-md ${
                        (data.logo || data.remove_logo) 
                          ? 'bg-blue-600 hover:bg-blue-700 text-white border-0' 
                          : 'bg-blue-500 hover:bg-blue-600 text-white border-0'
                      }`}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {processing ? 'Updating...' : 'Update Organization'}
                      {(data.logo || data.remove_logo) && !processing && (
                        <span className="ml-2 text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                          {data.logo && data.remove_logo ? 'Logo changes' : 
                           data.logo ? 'New logo' : 'Remove logo'}
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Logo Upload */}
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-green-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <Upload className="h-5 w-5 text-green-600" />
                  Organization Logo
                </CardTitle>
                <CardDescription className="text-green-600">
                  Update or remove the organization logo
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Current/Preview Logo */}
                  {(logoPreview || showCurrentLogo) && (
                    <div className="flex flex-col items-center space-y-2">
                      <div className="relative">
                        <img
                          src={logoPreview || `/storage/${organization.logo}`}
                          alt="Logo preview"
                          className="h-24 w-24 object-cover rounded-lg border"
                        />
                        {data.remove_logo && (
                          <div className="absolute inset-0 bg-red-500 bg-opacity-50 rounded-lg flex items-center justify-center">
                            <span className="text-white text-xs font-medium">Will be removed</span>
                          </div>
                        )}
                      </div>
                      
                      {showCurrentLogo && !data.logo && (
                        <p className="text-xs text-gray-500">Current logo</p>
                      )}
                      {data.logo && (
                        <p className="text-xs text-green-600">New logo selected</p>
                      )}
                    </div>
                  )}

                  {/* File Input */}
                  <div>
                    <Label htmlFor="logo">
                      {organization.logo ? 'Choose New Logo' : 'Choose Logo'}
                    </Label>
                    <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="mt-1"
                    />
                    {errors.logo && (
                      <p className="text-sm text-red-500 mt-1">{errors.logo}</p>
                    )}
                  </div>

                  {/* Action Buttons */}
                  {organization.logo && (
                    <div className="flex gap-2">
                      {!data.remove_logo ? (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={removeLogo}
                          className="text-red-600 hover:text-red-700"
                        >
                          Remove Current Logo
                        </Button>
                      ) : (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={restoreLogo}
                          className="text-green-600 hover:text-green-700"
                        >
                          Keep Current Logo
                        </Button>
                      )}
                    </div>
                  )}

                  <p className="text-sm text-gray-500">
                    {organization.logo 
                      ? "Upload a new file to replace current logo, or remove it entirely. Click 'Update Organization' to save changes."
                      : "Recommended: Square image, max 2MB. Click 'Update Organization' to save changes."
                    }
                  </p>

                  {/* Save Reminder */}
                  {(data.logo || data.remove_logo) && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <p className="text-sm text-blue-700 font-medium">
                          {data.logo ? "New logo ready to upload" : "Logo marked for removal"}
                        </p>
                      </div>
                      <p className="text-xs text-blue-600 mt-1">
                        ⬅️ Click the blue "Update Organization" button on the left to save your changes.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Organization Status */}
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-purple-50 rounded-t-lg">
                <CardTitle className="text-purple-700">Organization Status</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                    <span className="text-sm font-medium text-purple-700">Current Status</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      organization.is_active 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-400 text-white'
                    }`}>
                      {organization.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-sm text-purple-600">
                    Status can be changed from the organizations list
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Help */}
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-amber-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-amber-700">
                  <AlertCircle className="h-5 w-5 text-amber-600" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-2 text-sm text-amber-700">
                  <p>• Organization name must be unique</p>
                  <p>• Contact email will be used for notifications</p>
                  <p>• Description helps users understand the organization</p>
                  <p>• All fields marked with * are required</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
} 