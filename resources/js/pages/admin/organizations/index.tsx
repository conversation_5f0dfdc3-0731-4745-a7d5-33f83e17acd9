import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Building2,
    Plus,
    Search,
    Filter,
    Download,
    RefreshCw,
    Edit,
    Eye,
    Users,
    Trophy,
    Calendar,
    MoreHorizontal,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface Organization {
    id: number;
    slug: string;
    name: string;
    email: string;
    phone?: string;
    website?: string;
    status: 'active' | 'inactive' | 'suspended';
    created_at: string;
    users_count: number;
    categories_count: number;
    total_revenue: string;
    total_votes: number;
    admin?: {
        id: number;
        name: string;
        email: string;
    };
}

interface OrganizationsProps {
    organizations: {
        data: Organization[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        status?: string;
    };
    stats?: {
        total_organizations?: number;
        active_organizations?: number;
        total_revenue?: number;
        total_votes?: number;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organizations', href: '/admin/organizations' },
];

export default function Organizations({ organizations, filters, stats }: OrganizationsProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'active':
                return 'default';
            case 'inactive':
                return 'secondary';
            case 'suspended':
                return 'destructive';
            default:
                return 'outline';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'inactive':
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
            case 'suspended':
                return <XCircle className="h-4 w-4 text-red-600" />;
            default:
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
        }
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/organizations', { ...filters, search: searchTerm }, { preserveState: true });
    };

    const handleToggleStatus = (organizationSlug: string) => {
        router.post(`/admin/organizations/${organizationSlug}/toggle-status`, {}, {
            preserveScroll: true,
            onSuccess: () => {
                // Handle success
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Organizations - Admin Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Organizations
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Manage registered organizations and their settings
                            </p>
                        </div>
                        {/* <div className="flex flex-wrap items-center gap-3">
                            <Button variant="outline" size="sm" className="h-9">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                            <Button asChild>
                                <Link href="/admin/organizations/create">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Organization
                                </Link>
                            </Button>
                        </div> */}
                    </div>

                    {/* Search */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex gap-4">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search organizations by name, email, or website..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <Button type="submit">Search</Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Stats Cards */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Organizations</CardTitle>
                                <Building2 className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {(stats?.total_organizations ?? 0).toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Registered organizations
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Active Organizations</CardTitle>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {(stats?.active_organizations ?? 0).toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Currently active
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
                                <CediSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    GHS {(stats?.total_revenue ?? 0).toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    All organizations
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Votes</CardTitle>
                                <Trophy className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {(stats?.total_votes ?? 0).toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Across all categories
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Organizations Table */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building2 className="h-5 w-5 text-blue-600" />
                                All Organizations
                            </CardTitle>
                            <CardDescription>
                                Showing {organizations.data.length} of {organizations.total} organizations
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">Organization</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Admin</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Stats</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Revenue</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Created</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {organizations.data.map((org) => (
                                            <tr key={org.id} className="hover:bg-gray-50">
                                                <td className="p-4">
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                            <Building2 className="h-6 w-6 text-blue-600" />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-900">{org.name}</p>
                                                            <p className="text-sm text-gray-500">{org.email}</p>
                                                            {org.website && (
                                                                <p className="text-sm text-blue-600 hover:underline">
                                                                    <a href={org.website} target="_blank" rel="noopener noreferrer">
                                                                        {org.website}
                                                                    </a>
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getStatusIcon(org.status)}
                                                        <Badge variant={getStatusBadgeVariant(org.status)}>
                                                            {(org.status || 'unknown').toUpperCase()}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    {org.admin ? (
                                                        <div>
                                                            <p className="font-medium text-gray-900">{org.admin.name}</p>
                                                            <p className="text-sm text-gray-500">{org.admin.email}</p>
                                                        </div>
                                                    ) : (
                                                        <span className="text-sm text-gray-500">No admin assigned</span>
                                                    )}
                                                </td>
                                                <td className="p-4">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center gap-2 text-sm">
                                                            <Users className="h-3 w-3 text-gray-400" />
                                                            <span>{org.users_count} users</span>
                                                        </div>
                                                        <div className="flex items-center gap-2 text-sm">
                                                            <Trophy className="h-3 w-3 text-gray-400" />
                                                            <span>{org.categories_count} categories</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="space-y-1">
                                                        <p className="font-medium text-gray-900">{org.total_revenue}</p>
                                                        <p className="text-sm text-gray-500">{org.total_votes} votes</p>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-4 w-4 text-gray-400" />
                                                        <span className="text-sm text-gray-900">
                                                            {new Date(org.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/admin/organizations/${org.slug}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/admin/organizations/${org.slug}/edit`}>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button 
                                                            variant="ghost" 
                                                            size="sm"
                                                            onClick={() => handleToggleStatus(org.slug)}
                                                        >
                                                            {org.status === 'active' ? (
                                                                <XCircle className="h-4 w-4 text-red-600" />
                                                            ) : (
                                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                                            )}
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pagination */}
                    {organizations.last_page > 1 && (
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-700">
                                        Showing {((organizations.current_page - 1) * organizations.per_page) + 1} to{' '}
                                        {Math.min(organizations.current_page * organizations.per_page, organizations.total)} of{' '}
                                        {organizations.total} results
                                    </p>
                                    <div className="flex gap-2">
                                        {organizations.current_page > 1 && (
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/organizations?page=${organizations.current_page - 1}`}>
                                                    Previous
                                                </Link>
                                            </Button>
                                        )}
                                        {organizations.current_page < organizations.last_page && (
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/organizations?page=${organizations.current_page + 1}`}>
                                                    Next
                                                </Link>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}