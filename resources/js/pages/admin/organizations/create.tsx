import React, { useState } from 'react';
import { Head, useForm, Link, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, Building2, Mail, Phone, Globe, Upload } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

interface CreateOrganizationProps {
  // No props needed for create page
}

export default function CreateOrganization({}: CreateOrganizationProps) {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    description: '',
    contact_email: '',
    contact_phone: '',
    website: '',
    logo: null as File | null,
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organizations', href: '/admin/organizations' },
    { title: 'Create', href: '/admin/organizations/create' },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    router.post(route('admin.organizations.store'), data, {
      forceFormData: true,
      onSuccess: () => {
        // Handle success - redirect is handled by backend
      },
      onError: (errors) => {
        // Form submission errors handled by form validation
      }
    });
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (2MB max)
      if (file.size > 2048 * 1024) {
        alert('File size must be less than 2MB');
        e.target.value = '';
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        e.target.value = '';
        return;
      }

      setData('logo', file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.onerror = () => {
        alert('Error reading file');
        setLogoPreview(null);
      };
      reader.readAsDataURL(file);
    } else {
      setData('logo', null);
      setLogoPreview(null);
    }
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Organization" />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-800">Create Organization</h1>
            <p className="text-gray-600 mt-1">
              Add a new organization to the system
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-blue-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Building2 className="h-5 w-5 text-blue-600" />
                  Organization Details
                </CardTitle>
                <CardDescription className="text-blue-600">
                  Enter the basic information for the new organization
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Organization Name *</Label>
                      <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        placeholder="Enter organization name"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="contact_email">Contact Email *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contact_email"
                          type="email"
                          value={data.contact_email}
                          onChange={(e) => setData('contact_email', e.target.value)}
                          placeholder="<EMAIL>"
                          className={`pl-10 ${errors.contact_email ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.contact_email && (
                        <p className="text-sm text-red-500">{errors.contact_email}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Describe the organization's purpose and activities"
                      rows={4}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contact_phone">Contact Phone</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contact_phone"
                          value={data.contact_phone}
                          onChange={(e) => setData('contact_phone', e.target.value)}
                          placeholder="+****************"
                          className={`pl-10 ${errors.contact_phone ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.contact_phone && (
                        <p className="text-sm text-red-500">{errors.contact_phone}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="website"
                          type="url"
                          value={data.website}
                          onChange={(e) => setData('website', e.target.value)}
                          placeholder="https://organization.com"
                          className={`pl-10 ${errors.website ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.website && (
                        <p className="text-sm text-red-500">{errors.website}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      asChild
                      className="border-gray-300 hover:border-gray-400 hover:bg-gray-50 hover:shadow-md transition-all duration-200"
                    >
                      <Link href={route('admin.organizations.index')}>
                        Cancel
                      </Link>
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={processing}
                      className="bg-blue-500 hover:bg-blue-600 text-white border-0 hover:shadow-md transition-all duration-200"
                    >
                      {processing ? 'Creating...' : 'Create Organization'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Logo Upload */}
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-green-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <Upload className="h-5 w-5 text-green-600" />
                  Organization Logo
                </CardTitle>
                <CardDescription className="text-green-600">
                  Upload a logo for the organization (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {logoPreview && (
                    <div className="flex justify-center">
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="h-24 w-24 object-cover rounded-lg border"
                      />
                    </div>
                  )}
                  <div>
                    <Label htmlFor="logo">Choose Logo</Label>
                    <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="mt-1"
                    />
                    {errors.logo && (
                      <p className="text-sm text-red-500 mt-1">{errors.logo}</p>
                    )}
                  </div>
                  <p className="text-sm text-gray-500">
                    Recommended: Square image, max 2MB
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Help */}
            <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
              <CardHeader className="bg-amber-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-amber-700">
                  <AlertCircle className="h-5 w-5 text-amber-600" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-2 text-sm text-amber-700">
                  <p>• Organization name must be unique</p>
                  <p>• Contact email will be used for notifications</p>
                  <p>• Description helps users understand the organization</p>
                  <p>• All fields marked with * are required</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
} 