import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';
import { 
  Building2, 
  Users, 
  Award, 
  TrendingUp, 
  Mail, 
  Phone, 
  Globe, 
  Edit,
  MoreHorizontal,
  UserCheck,
  Trophy,
  Copy,
  ExternalLink
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem } from '@/types';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface Organization {
  id: number;
  name: string;
  slug: string;
  description: string;
  contact_email: string;
  contact_phone?: string;
  website?: string;
  logo?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  users: any[];
  awardCategories: any[];
}

interface Stats {
  total_users: number;
  total_admins: number;
  total_nominees: number;
  total_categories: number;
  active_categories: number;
  total_nominees_profiles: number;
  total_votes: number;
  total_revenue: number;
}

interface ShowOrganizationProps {
  organization: Organization;
  stats: Stats;
}

export default function ShowOrganization({ organization, stats }: ShowOrganizationProps) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Organizations', href: '/admin/organizations' },
    { title: organization.name, href: `/admin/organizations/${organization.slug}` },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${organization.name} - Organization Details`} />
      
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {organization.logo && (
              <img
                src={`/storage/${organization.logo}`}
                alt={organization.name}
                className="h-16 w-16 rounded-xl object-cover border-2 border-blue-100"
              />
            )}
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <span className="text-gray-800">{organization.name}</span>
                <Badge 
                  variant={organization.is_active ? 'default' : 'secondary'}
                  className={organization.is_active 
                    ? 'bg-green-500 hover:bg-green-600 text-white border-0' 
                    : 'bg-gray-400 hover:bg-gray-500 text-white border-0'
                  }
                >
                  {organization.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </h1>
              <p className="text-gray-600 mt-1">
                Organization Details and Statistics
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button 
              asChild
              className="bg-blue-500 hover:bg-blue-600 text-white border-0 hover:shadow-md transition-all duration-200"
            >
              <Link href={route('admin.organizations.edit', organization.slug)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  className="border-gray-300 hover:border-gray-400 hover:bg-gray-50 hover:shadow-md transition-all duration-200"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-gray-200 hover:shadow-lg transition-shadow duration-200">
                <DropdownMenuItem>
                  <Link href={route('admin.organizations.index')}>
                    Back to Organizations
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 bg-blue-50 hover:bg-blue-100 hover:shadow-md transition-all duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">Total Users</CardTitle>
              <Users className="h-5 w-5 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-800">{stats.total_users}</div>
              <p className="text-xs text-blue-600 mt-1">
                {stats.total_admins} admins, {stats.total_nominees} nominees
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-purple-50 hover:bg-purple-100 hover:shadow-md transition-all duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-700">Award Categories</CardTitle>
              <Award className="h-5 w-5 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-800">{stats.total_categories}</div>
              <p className="text-xs text-purple-600 mt-1">
                {stats.active_categories} active categories
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-amber-50 hover:bg-amber-100 hover:shadow-md transition-all duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-amber-700">Total Votes</CardTitle>
              <Trophy className="h-5 w-5 text-amber-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-800">{stats.total_votes.toLocaleString()}</div>
              <p className="text-xs text-amber-600 mt-1">
                Across all categories
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-green-50 hover:bg-green-100 hover:shadow-md transition-all duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700">Total Revenue</CardTitle>
              <CediSign className="h-5 w-5 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-800">GHS {stats.total_revenue.toLocaleString()}</div>
              <p className="text-xs text-green-600 mt-1">
                From voting activities
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Organization Details */}
          <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
            <CardHeader className="bg-slate-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2 text-slate-700">
                <Building2 className="h-5 w-5 text-slate-600" />
                Organization Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div>
                <h4 className="font-medium text-sm text-slate-500 uppercase tracking-wide">Description</h4>
                <p className="mt-1 text-slate-800 leading-relaxed">{organization.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-slate-500 uppercase tracking-wide">Contact Email</h4>
                  <div className="mt-1 flex items-center gap-2">
                    <Mail className="h-4 w-4 text-blue-500" />
                    <a href={`mailto:${organization.contact_email}`} className="text-blue-600 hover:text-blue-700 hover:underline transition-colors">
                      {organization.contact_email}
                    </a>
                  </div>
                </div>

                {organization.contact_phone && (
                  <div>
                    <h4 className="font-medium text-sm text-slate-500 uppercase tracking-wide">Contact Phone</h4>
                    <div className="mt-1 flex items-center gap-2">
                      <Phone className="h-4 w-4 text-green-500" />
                      <a href={`tel:${organization.contact_phone}`} className="text-green-600 hover:text-green-700 hover:underline transition-colors">
                        {organization.contact_phone}
                      </a>
                    </div>
                  </div>
                )}
              </div>

              {/* Additional Organization Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    Organization Links
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Personalized Nomination Link */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700">
                      Personalized Nomination Application Link
                    </Label>
                    <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <Input
                        readOnly
                        value={`${window.location.origin}/apply/${organization.slug}`}
                        className="flex-1 bg-white border-blue-300 text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/apply/${organization.slug}`);
                          toast.success("Link copied!", {
                            description: `Nomination link for ${organization.name} copied to clipboard.`,
                          });
                        }}
                        className="shrink-0"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`${window.location.origin}/apply/${organization.slug}`, '_blank')}
                        className="shrink-0"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-slate-600">
                      Share this personalized link with potential nominees to apply specifically for your organization. 
                      They won't need to select your organization manually - it will be pre-selected for them.
                    </p>
                  </div>

                  {/* General Links */}
                  {organization.website && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-slate-700">Website</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          readOnly
                          value={organization.website}
                          className="flex-1 text-sm"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(organization.website, '_blank')}
                          className="shrink-0"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                <div>
                  <h4 className="font-medium text-sm text-slate-500 uppercase tracking-wide">Created</h4>
                  <p className="mt-1 text-slate-800">
                    {new Date(organization.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-slate-500 uppercase tracking-wide">Status</h4>
                  <Badge 
                    variant={organization.is_active ? 'default' : 'secondary'} 
                    className={`mt-1 ${organization.is_active 
                      ? 'bg-green-500 hover:bg-green-600 text-white border-0' 
                      : 'bg-gray-400 hover:bg-gray-500 text-white border-0'
                    }`}
                  >
                    {organization.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="border-0 bg-white hover:shadow-lg transition-all duration-200 ring-1 ring-gray-200">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2 text-indigo-700">
                <TrendingUp className="h-5 w-5 text-indigo-600" />
                Performance Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-blue-700">Nominee Profiles</span>
                  <span className="text-sm font-bold text-blue-800">{stats.total_nominees_profiles}</span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium text-green-700">Average Votes per Category</span>
                  <span className="text-sm font-bold text-green-800">
                    {stats.total_categories > 0 ? Math.round(stats.total_votes / stats.total_categories) : 0}
                  </span>
                </div>

                <div className="flex justify-between items-center p-3 bg-amber-50 rounded-lg">
                  <span className="text-sm font-medium text-amber-700">Average Revenue per Vote</span>
                  <span className="text-sm font-bold text-amber-800">
                    GHS {stats.total_votes > 0 ? (stats.total_revenue / stats.total_votes).toFixed(2) : '0.00'}
                  </span>
                </div>

                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-sm font-medium text-purple-700">Users per Category</span>
                  <span className="text-sm font-bold text-purple-800">
                    {stats.total_categories > 0 ? Math.round(stats.total_users / stats.total_categories) : 0}
                  </span>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Organization Health</span>
                  <Badge 
                    variant={
                      stats.total_votes > 100 && stats.total_revenue > 1000 ? 'default' :
                      stats.total_votes > 50 && stats.total_revenue > 500 ? 'secondary' : 'outline'
                    }
                    className={
                      stats.total_votes > 100 && stats.total_revenue > 1000 
                        ? 'bg-green-500 hover:bg-green-600 text-white border-0' :
                      stats.total_votes > 50 && stats.total_revenue > 500 
                        ? 'bg-blue-500 hover:bg-blue-600 text-white border-0' 
                        : 'bg-gray-300 hover:bg-gray-400 text-gray-700 border-0'
                    }
                  >
                    {stats.total_votes > 100 && stats.total_revenue > 1000 ? 'Excellent' :
                     stats.total_votes > 50 && stats.total_revenue > 500 ? 'Good' : 'Growing'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Toaster position="top-right" />
    </AppLayout>
  );
} 