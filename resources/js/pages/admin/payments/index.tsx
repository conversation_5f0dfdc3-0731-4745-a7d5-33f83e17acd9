import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    CreditCard,
    Search,
    Filter,
    Download,
    RefreshCw,
    Eye,
    Calendar,
    CheckCircle,
    XCircle,
    Clock,
    AlertCircle,
    Building2,
    User,
    Trophy
} from 'lucide-react';
import CediSign from '@/components/icons/cedi-sign';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { useState } from 'react';

interface Payment {
    id: number;
    reference: string;
    amount: string;
    status: 'completed' | 'pending' | 'failed' | 'refunded';
    payment_method: string;
    nominee: {
        id: number;
        display_name: string;
        slug: string;
    };
    category: {
        id: number;
        name: string;
    };
    organization: {
        id: number;
        name: string;
    };
    voter_email?: string;
    votes_count: number;
    created_at: string;
    processed_at?: string;
    gateway_response?: string;
}

interface PaymentsProps {
    payments: {
        data: Payment[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        status?: string;
        organization_id?: number;
        date_from?: string;
        date_to?: string;
    };
    stats: {
        total_payments: number;
        completed_payments: number;
        pending_payments: number;
        failed_payments: number;
        total_revenue: string;
        today_revenue: string;
    };
    user_role: string;
    organizations?: Array<{ id: number; name: string }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin Dashboard', href: '/admin' },
    { title: 'Payments', href: '/admin/payments' },
];

export default function Payments({ payments, filters, stats, user_role, organizations }: PaymentsProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'completed':
                return 'default';
            case 'pending':
                return 'secondary';
            case 'failed':
                return 'destructive';
            case 'refunded':
                return 'outline';
            default:
                return 'outline';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-600" />;
            case 'failed':
                return <XCircle className="h-4 w-4 text-red-600" />;
            case 'refunded':
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
            default:
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
        }
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/payments', { ...filters, search: searchTerm }, { preserveState: true });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Payments - Admin Dashboard" />

            <div className="min-h-screen bg-gray-50/50">
                <div className="space-y-8 p-6 md:p-8">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                Payment Management
                            </h1>
                            <p className="text-lg text-muted-foreground">
                                Monitor and manage payment transactions
                            </p>
                        </div>
                        {/* <div className="flex flex-wrap items-center gap-3">
                            <Button variant="outline" size="sm" className="h-9">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button variant="outline" size="sm" className="h-9">
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                        </div> */}
                    </div>

                    {/* Search */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="flex gap-4">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search by reference, nominee, or voter email..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <Button type="submit">Search</Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Stats Cards */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-6">
                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Payments</CardTitle>
                                <CreditCard className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.total_payments.toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    All transactions
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Completed</CardTitle>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.completed_payments.toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Successful payments
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Pending</CardTitle>
                                <Clock className="h-4 w-4 text-yellow-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.pending_payments.toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Awaiting processing
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Failed</CardTitle>
                                <XCircle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.failed_payments.toLocaleString()}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Failed transactions
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
                                <CediSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.total_revenue}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    All time
                                </p>
                            </CardContent>
                        </Card>

                        <Card className="border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Today's Revenue</CardTitle>
                                <CediSign className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">
                                    {stats.today_revenue}
                                </div>
                                <p className="text-xs text-muted-foreground">
                                    Today only
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Payments Table */}
                    <Card className="border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="h-5 w-5 text-blue-600" />
                                All Payments
                            </CardTitle>
                            <CardDescription>
                                Showing {payments.data.length} of {payments.total} payments
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b">
                                        <tr>
                                            <th className="text-left p-4 font-medium text-gray-900">Payment</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Status</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Nominee</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Category</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Voter</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Amount</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Date</th>
                                            <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {payments.data.map((payment) => (
                                            <tr key={payment.id} className="hover:bg-gray-50">
                                                <td className="p-4">
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                            <CreditCard className="h-5 w-5 text-green-600" />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-900">#{payment.reference}</p>
                                                            <p className="text-sm text-gray-500">{payment.payment_method}</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        {getStatusIcon(payment.status)}
                                                        <Badge variant={getStatusBadgeVariant(payment.status)}>
                                                            {payment.status.toUpperCase()}
                                                        </Badge>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <User className="h-4 w-4 text-gray-400" />
                                                        <Link 
                                                            href={`/nominees/${payment.nominee.slug}`}
                                                            className="text-sm text-blue-600 hover:underline"
                                                        >
                                                            {payment.nominee.display_name}
                                                        </Link>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <Trophy className="h-4 w-4 text-gray-400" />
                                                        <span className="text-sm text-gray-900">{payment.category.name}</span>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div>
                                                        {payment.voter_email ? (
                                                            <p className="text-sm text-gray-900">{payment.voter_email}</p>
                                                        ) : (
                                                            <p className="text-sm text-gray-500">Anonymous</p>
                                                        )}
                                                        <p className="text-xs text-gray-500">{payment.votes_count} votes</p>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <CediSign className="h-4 w-4 text-green-600" />
                                                        <span className="font-medium text-gray-900">{payment.amount}</span>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center gap-2">
                                                            <Calendar className="h-3 w-3 text-gray-400" />
                                                            <span className="text-sm text-gray-900">
                                                                {new Date(payment.created_at).toLocaleDateString()}
                                                            </span>
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                            {new Date(payment.created_at).toLocaleTimeString()}
                                                        </p>
                                                    </div>
                                                </td>
                                                <td className="p-4">
                                                    <div className="flex items-center gap-2">
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/admin/payments/${payment.id}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pagination */}
                    {payments.last_page > 1 && (
                        <Card className="border-0 shadow-sm">
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-700">
                                        Showing {((payments.current_page - 1) * payments.per_page) + 1} to{' '}
                                        {Math.min(payments.current_page * payments.per_page, payments.total)} of{' '}
                                        {payments.total} results
                                    </p>
                                    <div className="flex gap-2">
                                        {payments.current_page > 1 && (
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/payments?page=${payments.current_page - 1}`}>
                                                    Previous
                                                </Link>
                                            </Button>
                                        )}
                                        {payments.current_page < payments.last_page && (
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/payments?page=${payments.current_page + 1}`}>
                                                    Next
                                                </Link>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 