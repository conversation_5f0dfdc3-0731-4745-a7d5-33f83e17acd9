import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Heart, 
    Trophy, 
    Users, 
    Calendar, 
    Star, 
    AlertCircle, 
    CheckCircle,
    Share2,
    Bookmark,
    MessageCircle,
    TrendingUp,
    Award,
    Sparkles,
    Crown,
    Flame,
    Instagram,
    Twitter,
    Facebook,
    Linkedin,
    Globe,
    MapPin,
    Clock,
    Zap,
    ArrowRight,
    Shield,
    ChevronRight
} from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';
import CediSign from '@/components/icons/cedi-sign';

interface Nominee {
    id?: number;
    slug?: string;
    display_name?: string;
    bio?: string;
    profile_image?: string;
    additional_images?: string[];
    social_links?: Record<string, string>;
    achievements?: string[] | any[];
    why_vote_for_me?: string;
    total_votes?: number;
    ranking?: number;
    can_receive_votes?: boolean;
}

interface Category {
    id?: number;
    name?: string;
    description?: string;
    price_per_vote?: number;
    discount_percentage?: number;
    discount_min_votes?: number;
    voting_start_date?: string;
    voting_end_date?: string;
    is_voting_active?: boolean;
}

interface OtherNominee {
    slug?: string;
    display_name?: string;
    profile_image?: string;
    total_votes?: number;
}

interface SeoData {
    title?: string;
    description?: string;
    keywords?: string;
    og_title?: string;
    og_description?: string;
    og_image?: string;
    og_url?: string;
    twitter_title?: string;
    twitter_description?: string;
    twitter_image?: string;
    canonical_url?: string;
}

interface Props {
    nominee?: Nominee;
    category?: Category;
    other_nominees?: OtherNominee[];
    paystack_public_key?: string;
    seo?: SeoData;
}

export default function VotingShow({ nominee, category, other_nominees = [], paystack_public_key, seo }: Props) {
    
    // Error boundary effect
    useEffect(() => {
        const handleError = (error: ErrorEvent) => {
            // Silent error handling - errors are logged server-side
            setError('An unexpected error occurred. Please refresh the page.');
        };
        
        window.addEventListener('error', handleError);
        return () => window.removeEventListener('error', handleError);
    }, []);
    
    // Additional safety check with error logging
    useEffect(() => {
        // Validate critical data
        if (!nominee) {
            setError('Nominee data is missing. Please try refreshing the page.');
        }
        if (!category) {
            setError('Category data is missing. Please try refreshing the page.');
        }
    }, [nominee, category, other_nominees, paystack_public_key]);
    const [voteCount, setVoteCount] = useState<number | string>(1);
    const [voterName, setVoterName] = useState('');
    const [voterEmail, setVoterEmail] = useState('');
    const [voterPhone, setVoterPhone] = useState('');
    const [pricing, setPricing] = useState<any>(null);
    const [isCalculating, setIsCalculating] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [isLiked, setIsLiked] = useState(false);
    const [isBookmarked, setIsBookmarked] = useState(false);

    // Safety check for required data with debugging
    if (!nominee || !category) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-6">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <AlertCircle className="h-8 w-8 text-red-600" />
                    </div>
                    <h1 className="text-2xl font-bold text-slate-900 mb-4">Page Not Available</h1>
                    <p className="text-slate-600 mb-6">
                        This voting page is currently unavailable. This may be due to permissions or missing data.
                    </p>
                    <div className="space-y-2 text-sm text-slate-500 bg-slate-50 rounded-lg p-4">
                        <p><strong>Status:</strong></p>
                        <p>• Nominee: {nominee ? '✅ OK' : '❌ Missing'}</p>
                        <p>• Category: {category ? '✅ OK' : '❌ Missing'}</p>
                        <p>• Other Nominees: {other_nominees ? other_nominees.length : 0}</p>
                    </div>
                    <div className="mt-6">
                        <Button 
                            onClick={() => window.location.reload()} 
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            Refresh Page
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    const calculatePrice = async () => {
        const numVoteCount = typeof voteCount === 'string' ? parseInt(voteCount) || 0 : voteCount;
        if (numVoteCount < 1) return;
        
        setIsCalculating(true);
        setError(null);
        
        try {
            const response = await fetch(`/vote/${nominee.slug}/calculate-price`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ vote_count: numVoteCount }),
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setPricing(data);
            } else {
                setError(data.message || 'Failed to calculate price');
            }
        } catch (error) {
            setError('Failed to calculate price. Please try again.');
        } finally {
            setIsCalculating(false);
        }
    };

    const initiatePayment = async () => {
        const numVoteCount = typeof voteCount === 'string' ? parseInt(voteCount) || 0 : voteCount;
        if (!voterEmail || numVoteCount < 1) {
            setError('Please fill in all required fields');
            return;
        }
        
        setIsProcessing(true);
        setError(null);
        setSuccess(null);
        
        try {
            const response = await fetch(`/vote/${nominee.slug}/initiate-payment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    vote_count: numVoteCount,
                    voter_name: voterName,
                    voter_email: voterEmail,
                    voter_phone: voterPhone,
                }),
            });
            
            const data = await response.json();
            
            if (response.ok && data.status) {
                // Redirect to Paystack checkout
                if (data.data.authorization_url) {
                    setSuccess('Redirecting to payment...');
                    window.location.href = data.data.authorization_url;
                } else {
                    setError('Payment initialization failed - no authorization URL');
                }
            } else {
                setError(data.error || data.message || 'Payment initialization failed');
            }
        } catch (error) {
            setError('Failed to initiate payment. Please try again.');
        } finally {
            setIsProcessing(false);
        }
    };

    // Reset vote count to 1 on component mount
    useEffect(() => {
        setVoteCount(1);
    }, []);

    // Auto-calculate price when vote count changes
    useEffect(() => {
        const numVoteCount = typeof voteCount === 'string' ? parseInt(voteCount) || 0 : voteCount;
        if (numVoteCount > 0) {
            const timer = setTimeout(() => {
                calculatePrice();
            }, 500);
            return () => clearTimeout(timer);
        }
    }, [voteCount]);

    const getSocialIcon = (platform: string) => {
        switch (platform.toLowerCase()) {
            case 'instagram': return <Instagram className="h-4 w-4" />;
            case 'twitter': return <Twitter className="h-4 w-4" />;
            case 'facebook': return <Facebook className="h-4 w-4" />;
            case 'linkedin': return <Linkedin className="h-4 w-4" />;
            default: return <Globe className="h-4 w-4" />;
        }
    };

    const getRankingBadge = (ranking?: number) => {
        if (!ranking || typeof ranking !== 'number') return { icon: Trophy, color: 'text-slate-600', bg: 'bg-slate-50 border-slate-200' };
        if (ranking === 1) return { icon: Crown, color: 'text-yellow-600', bg: 'bg-yellow-50 border-yellow-200' };
        if (ranking <= 3) return { icon: Award, color: 'text-orange-600', bg: 'bg-orange-50 border-orange-200' };
        if (ranking <= 10) return { icon: Star, color: 'text-blue-600', bg: 'bg-blue-50 border-blue-200' };
        return { icon: Trophy, color: 'text-slate-600', bg: 'bg-slate-50 border-slate-200' };
    };

    const handleShare = async () => {
        const shareData = {
            title: `Vote for ${nominee?.display_name || 'Nominee'} - ${category?.name || 'Category'}`,
            text: `Support ${nominee?.display_name || 'this nominee'} in the ${category?.name || 'this category'} category! Currently ranked #${nominee?.ranking || 'N/A'} with ${(nominee?.total_votes || 0).toLocaleString()} votes.`,
            url: window.location.href,
        };

        try {
            // Check if Web Share API is supported
            if (navigator.share) {
                await navigator.share(shareData);
            } else {
                // Fallback: Copy to clipboard
                await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
                setSuccess('Link copied to clipboard!');
                setTimeout(() => setSuccess(null), 3000);
            }
        } catch (error) {
            // Fallback: Copy to clipboard
            try {
                await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
                setSuccess('Link copied to clipboard!');
                setTimeout(() => setSuccess(null), 3000);
            } catch (clipboardError) {
                setError('Failed to share. Please copy the URL manually.');
                setTimeout(() => setError(null), 3000);
            }
        }
    };

    const rankingBadge = getRankingBadge(nominee.ranking);
    const RankingIcon = rankingBadge.icon;

    return (
        <>
            <Head title={seo?.title || `${nominee?.display_name || 'Nominee'} - Vote on VoteYourFav`}>
                <meta name="description" content={seo?.description || `Vote for ${nominee?.display_name} in the ${category?.name} category. Currently ranked #${nominee?.ranking || 'N/A'} with ${(nominee?.total_votes || 0).toLocaleString()} votes. Support your favorite nominee on VoteYourFav's secure voting platform.`} />
                <meta name="keywords" content={seo?.keywords || `vote, voting, award, ${nominee?.display_name}, ${category?.name}, nomination, Ghana, competition, contest, VoteYourFav`} />
                
                {/* Open Graph tags */}
                <meta property="og:title" content={seo?.og_title || `Vote for ${nominee?.display_name} - ${category?.name}`} />
                <meta property="og:description" content={seo?.og_description || `Support ${nominee?.display_name} in the ${category?.name} category. Ranked #${nominee?.ranking || 'N/A'} with ${(nominee?.total_votes || 0).toLocaleString()} votes. Vote securely on VoteYourFav.`} />
                <meta property="og:image" content={seo?.og_image || (nominee?.profile_image?.startsWith('http') ? nominee?.profile_image : `${window.location.origin}${nominee?.profile_image || '/voteyourfav_logo.png'}`)} />
                <meta property="og:url" content={seo?.og_url || route('vote.show', nominee?.slug)} />
                <meta property="og:type" content="website" />
                
                {/* Twitter Card tags */}
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={seo?.twitter_title || `Vote for ${nominee?.display_name} - ${category?.name}`} />
                <meta name="twitter:description" content={seo?.twitter_description || `Support ${nominee?.display_name} in the ${category?.name} category. Currently ranked #${nominee?.ranking || 'N/A'} with ${(nominee?.total_votes || 0).toLocaleString()} votes.`} />
                <meta name="twitter:image" content={seo?.twitter_image || (nominee?.profile_image?.startsWith('http') ? nominee?.profile_image : `${window.location.origin}${nominee?.profile_image || '/voteyourfav_logo.png'}`)} />
                
                {/* Canonical URL */}
                <link rel="canonical" href={seo?.canonical_url || route('vote.show', nominee?.slug)} />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-slate-100 via-slate-50 to-blue-50/30 relative overflow-hidden" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                {/* Background Elements - More subtle */}
                <div className="absolute inset-0 opacity-30">
                    <div className="absolute inset-0" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.02'%3E%3Ccircle cx='40' cy='40' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                    }}></div>
                </div>
                <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-blue-400/10 to-transparent rounded-full blur-3xl opacity-50"></div>
                <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-indigo-400/10 to-transparent rounded-full blur-3xl opacity-50"></div>

                {/* Header Navigation */}
                <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200/50 shadow-sm">
                    <nav className="relative z-10 flex items-center justify-between p-3 sm:p-4 lg:px-6 max-w-screen-xl mx-auto">
                        <div className="flex items-center space-x-2">
                            <AppLogoIcon className="h-7 w-7 sm:h-8 sm:w-8 object-contain" />
                            <span className="text-lg sm:text-xl font-bold text-slate-900">VoteYourFav</span>
                        </div>
                        <div className="flex items-center space-x-2 sm:space-x-3">
                            {category && (
                                <Badge variant="outline" className="border-blue-300 bg-blue-50 text-blue-700 text-xs sm:text-sm px-2 py-1 sm:px-3 sm:py-1.5">
                                    {category.name || 'Category'}
                                </Badge>
                            )}
                            <Button variant="ghost" size="icon" className="rounded-full text-slate-600 hover:bg-slate-200/70">
                                <Share2 className="h-4 w-4 sm:h-5 sm:w-5" onClick={handleShare} />
                            </Button>
                        </div>
                    </nav>
                </header>

                {/* Main Content Area */}
                <main className="relative z-10 max-w-screen-xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <div className="relative inline-block mb-8">
                            <div className="relative">
                                <Avatar className="w-24 h-24 sm:w-32 sm:h-32 mx-auto mb-4 border-4 border-white shadow-lg ring-4 ring-blue-500/30">
                                    <AvatarImage 
                                        src={nominee?.profile_image} alt={nominee?.display_name || 'Nominee'}
                                        className="block w-full h-full object-cover"
                                    />
                                    <AvatarFallback className="bg-blue-500/10 text-blue-700 font-semibold text-lg">
                                        {nominee.display_name?.charAt(0) || 'N'}
                                    </AvatarFallback>
                                </Avatar>
                                
                                {/* Ranking Badge */}
                                <div className={`absolute -top-2 -right-2 ${rankingBadge.bg} ${rankingBadge.color} border-0 rounded-full p-2 backdrop-blur-sm`}>
                                    <RankingIcon className="h-5 w-5" />
                                </div>
                                
                                {/* Verified Badge */}
                                <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2">
                                    <CheckCircle className="h-4 w-4" />
                                </div>
                            </div>
                        </div>

                        <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-4 leading-tight">
                            {nominee?.display_name || 'Nominee Name'}
                        </h1>
                        
                        <div className="flex flex-wrap justify-center items-center gap-4 mb-8">
                            <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 border-0">
                                <Trophy className="h-4 w-4 text-yellow-600" />
                                <span className="text-slate-700 font-medium">Rank #{nominee?.ranking || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 border-0">
                                <Heart className="h-4 w-4 text-red-500" />
                                <span className="text-slate-700 font-medium">{(nominee?.total_votes || 0).toLocaleString()} votes</span>
                            </div>
                            <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 border-0">
                                <TrendingUp className="h-4 w-4 text-green-600" />
                                <span className="text-slate-700 font-medium">Trending</span>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-center space-x-3 mb-12">
                            <Button 
                                variant="outline" 
                                size="sm" 
                                className="bg-white/80 border-0 text-slate-700 hover:bg-white transition-all duration-300 rounded-full"
                                onClick={() => setIsLiked(!isLiked)}
                            >
                                <Heart className={`h-4 w-4 mr-2 transition-colors duration-300 ${isLiked ? 'fill-red-500 text-red-500' : ''}`} />
                                {isLiked ? 'Liked' : 'Like'}
                            </Button>
                            <Button 
                                variant="outline" 
                                size="sm" 
                                className="bg-white/80 border-0 text-slate-700 hover:bg-white transition-all duration-300 rounded-full"
                                onClick={() => setIsBookmarked(!isBookmarked)}
                            >
                                <Bookmark className={`h-4 w-4 mr-2 transition-colors duration-300 ${isBookmarked ? 'fill-yellow-500 text-yellow-500' : ''}`} />
                                {isBookmarked ? 'Saved' : 'Save'}
                            </Button>
                            <Button 
                                variant="outline" 
                                size="sm" 
                                className="bg-white/80 border-0 text-slate-700 hover:bg-white transition-all duration-300 rounded-full"
                                onClick={handleShare}
                            >
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                            </Button>
                        </div>
                    </div>

                    {/* Main Content Grid */}
                    <div className="grid lg:grid-cols-3 gap-8">
                        {/* Left Column - Profile Details */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* About Section */}
                            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2 text-slate-900">
                                        <Users className="h-5 w-5 text-blue-600" />
                                        <span>About {nominee?.display_name || 'Nominee'}</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-slate-600 leading-relaxed mb-6">{nominee?.bio || 'No bio available.'}</p>
                                    
                                    {/* Social Links */}
                                    {nominee?.social_links && Object.keys(nominee.social_links).length > 0 && (
                                        <div className="flex flex-wrap gap-3">
                                            {Object.entries(nominee.social_links || {}).filter(([platform, url]) => platform && url).map(([platform, url]) => (
                                                <a
                                                    key={platform}
                                                    href={url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="flex items-center space-x-2 bg-slate-50 hover:bg-blue-50 text-slate-700 hover:text-blue-700 rounded-full px-4 py-2 transition-all duration-300 border-0"
                                                >
                                                    {getSocialIcon(platform)}
                                                    <span className="capitalize text-sm font-medium">{platform}</span>
                                                </a>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Why Vote Section */}
                            {nominee?.why_vote_for_me && (
                                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                    <CardHeader>
                                        <CardTitle className="flex items-center space-x-2 text-slate-900">
                                            <Zap className="h-5 w-5 text-yellow-600" />
                                            <span style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}>Why Vote for Me?</span>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-slate-600 leading-relaxed">{nominee?.why_vote_for_me}</p>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Achievements Section */}
                            {nominee?.achievements && Array.isArray(nominee.achievements) && nominee.achievements.length > 0 && (
                                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                    <CardHeader>
                                        <CardTitle className="flex items-center space-x-2 text-slate-900">
                                            <Award className="h-5 w-5 text-orange-600" />
                                            <span>Achievements & Recognition</span>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid gap-3">
                                            {Array.isArray(nominee?.achievements) ? nominee.achievements.filter(achievement => achievement && typeof achievement === 'string').map((achievement, index) => (
                                                <div key={index} className="flex items-center space-x-3 bg-slate-50 rounded-xl p-4 hover:bg-blue-50 transition-colors duration-300">
                                                    <Star className="h-4 w-4 text-yellow-600 flex-shrink-0" />
                                                    <span className="text-slate-700">{achievement}</span>
                                                </div>
                                            )) : (
                                                <div className="text-slate-500 text-sm">No achievements data available</div>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Additional Images Gallery */}
                            {nominee?.additional_images && Array.isArray(nominee.additional_images) && nominee.additional_images.length > 0 && (
                                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                    <CardHeader>
                                        <CardTitle className="flex items-center space-x-2 text-slate-900">
                                            <MessageCircle className="h-5 w-5 text-purple-600" />
                                            <span>Gallery</span>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                            {nominee?.additional_images?.filter(image => image && typeof image === 'string').map((image, index) => (
                                                <div key={index} className="aspect-square rounded-xl overflow-hidden bg-slate-100 transition-all duration-300">
                                                    <img 
                                                        src={image} 
                                                        alt={`${nominee?.display_name || 'Nominee'} - Image ${index + 1}`}
                                                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Right Column - Voting Panel */}
                        <div className="space-y-6">
                            {/* Voting Card - Stripe-style pricing */}
                            <Card className="bg-white/90 backdrop-blur-xl border-slate-200/50 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-500 sticky top-6 overflow-hidden">
                                {/* Gradient border effect */}
                                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 opacity-0 hover:opacity-100 transition-opacity duration-500" style={{ padding: '1px' }}>
                                    <div className="w-full h-full bg-white rounded-lg"></div>
                                </div>
                                
                                <CardHeader className="relative">
                                    <CardTitle className="flex items-center space-x-2 text-slate-900">
                                        <Heart className="h-6 w-6 text-red-500" />
                                        <span style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}>Cast Your Vote</span>
                                    </CardTitle>
                                    <CardDescription className="text-slate-600">
                                        Support {nominee?.display_name || 'this nominee'} and help them win!
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6 relative">
                                    {/* Error Alert */}
                                    {error && (
                                        <Alert variant="destructive" className="bg-red-50 border-red-200 text-red-800 rounded-xl">
                                            <AlertCircle className="h-4 w-4" />
                                            <AlertDescription>{error}</AlertDescription>
                                        </Alert>
                                    )}

                                    {/* Success Alert */}
                                    {success && (
                                        <Alert className="bg-green-50 border-green-200 text-green-800 rounded-xl">
                                            <CheckCircle className="h-4 w-4" />
                                            <AlertDescription>{success}</AlertDescription>
                                        </Alert>
                                    )}

                                    {(category?.is_voting_active && nominee?.can_receive_votes) ? (
                                        <>
                                            {/* Vote Count Selector */}
                                            <div className="space-y-3">
                                                <Label htmlFor="voteCount" className="text-slate-900 font-semibold">Number of Votes</Label>
                                                <div className="relative">
                                                    <Input
                                                        id="voteCount"
                                                        type="number"
                                                        min="1"
                                                        max="100000"
                                                        value={voteCount}
                                                        onChange={(e) => {
                                                            const value = e.target.value;
                                                            if (value === '') {
                                                                setVoteCount('');
                                                            } else {
                                                                const num = parseInt(value);
                                                                if (!isNaN(num) && num >= 1) {
                                                                    setVoteCount(num);
                                                                }
                                                            }
                                                        }}
                                                        onBlur={(e) => {
                                                            const value = e.target.value;
                                                            if (value === '' || parseInt(value) < 1) {
                                                                setVoteCount(1);
                                                            }
                                                        }}
                                                        className="bg-slate-50 border-slate-200 text-slate-900 placeholder-slate-400 text-lg font-semibold text-center rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                                                        autoComplete="off"
                                                        autoCorrect="off"
                                                        autoCapitalize="off"
                                                        spellCheck="false"
                                                    />
                                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                        <Heart className="h-5 w-5 text-red-500" />
                                                    </div>
                                                </div>
                                                <div className="flex justify-between items-center text-sm">
                                                    <span className="text-slate-600">GHS {category?.price_per_vote || '0.00'} per vote</span>
                                                    {(category?.discount_percentage || 0) > 0 && (
                                                        <span className="text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-full">
                                                            {category?.discount_percentage || 0}% off {category?.discount_min_votes || 1}+ votes!
                                                        </span>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Pricing Display - Stripe-style */}
                                            {pricing && (
                                                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200/50 relative overflow-hidden">
                                                    {/* Subtle pattern overlay */}
                                                    <div className="absolute inset-0 opacity-30" style={{
                                                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%236366f1' fill-opacity='0.1'%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3C/g%3E%3C/svg%3E")`
                                                    }}></div>
                                                    
                                                    <div className="space-y-3 relative">
                                                        <div className="flex justify-between items-center">
                                                            <span className="text-slate-600">Base Price:</span>
                                                            <span className="text-slate-900 font-semibold">GHS {pricing.base_price}</span>
                                                        </div>
                                                        {pricing.discount > 0 && (
                                                            <div className="flex justify-between items-center">
                                                                <span className="text-green-600">Discount ({pricing.discount_percentage}%):</span>
                                                                <span className="text-green-600 font-semibold">-GHS {pricing.discount}</span>
                                                            </div>
                                                        )}
                                                        {pricing.paystack_surcharge && (
                                                            <div className="flex justify-between items-center">
                                                                <span className="text-slate-600 text-sm">Processing Fee (3%):</span>
                                                                <span className="text-slate-900 font-semibold">GHS {pricing.paystack_surcharge}</span>
                                                            </div>
                                                        )}
                                                        <Separator className="bg-slate-200" />
                                                        <div className="flex justify-between items-center text-lg">
                                                            <span className="text-slate-900 font-bold">Total:</span>
                                                            <span className="text-blue-600 font-bold text-2xl">GHS {pricing.final_price}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}

                                            {/* Voter Information */}
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="voterEmail" className="text-slate-900 font-semibold">Email Address *</Label>
                                                    <Input
                                                        id="voterEmail"
                                                        type="email"
                                                        value={voterEmail}
                                                        onChange={(e) => setVoterEmail(e.target.value)}
                                                        required
                                                        placeholder="<EMAIL>"
                                                        className="bg-slate-50 border-slate-200 text-slate-900 placeholder-slate-400 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="voterName" className="text-slate-900 font-semibold">Full Name (Optional)</Label>
                                                    <Input
                                                        id="voterName"
                                                        type="text"
                                                        value={voterName}
                                                        onChange={(e) => setVoterName(e.target.value)}
                                                        placeholder="Your full name"
                                                        className="bg-slate-50 border-slate-200 text-slate-900 placeholder-slate-400 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="voterPhone" className="text-slate-900 font-semibold">Phone Number (Optional)</Label>
                                                    <Input
                                                        id="voterPhone"
                                                        type="tel"
                                                        value={voterPhone}
                                                        onChange={(e) => setVoterPhone(e.target.value)}
                                                        placeholder="+233 XX XXX XXXX"
                                                        className="bg-slate-50 border-slate-200 text-slate-900 placeholder-slate-400 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                                                    />
                                                </div>
                                            </div>

                                            {/* Vote Button - Superhuman-style */}
                                            <Button 
                                                onClick={initiatePayment}
                                                disabled={!voterEmail || (typeof voteCount === 'string' ? parseInt(voteCount) || 0 : voteCount) < 1 || isProcessing || isCalculating}
                                                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold py-4 text-lg shadow-lg hover:shadow-xl hover:shadow-blue-500/20 hover:-translate-y-0.5 transition-all duration-300 rounded-xl relative overflow-hidden group"
                                                size="lg"
                                            >
                                                {/* Button shine effect */}
                                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                                                
                                                {isProcessing ? (
                                                    <div className="flex items-center space-x-2 relative z-10">
                                                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                                        <span>Processing...</span>
                                                    </div>
                                                ) : isCalculating ? (
                                                    <div className="flex items-center space-x-2 relative z-10">
                                                        <div className="animate-pulse">💫</div>
                                                        <span>Calculating...</span>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center justify-center space-x-2 relative z-10">
                                                        <Zap className="h-5 w-5" />
                                                        <span>Vote Now - GHS {pricing?.final_price || '...'}</span>
                                                        <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                                                    </div>
                                                )}
                                            </Button>

                                            {/* Trust Indicators */}
                                            <div className="text-center text-sm text-slate-500 space-y-3">
                                                <div className="flex items-center justify-center space-x-2">
                                                    <Shield className="h-4 w-4 text-green-500" />
                                                    <span>Secure payment with Paystack</span>
                                                </div>
                                                <div className="flex items-center justify-center space-x-2">
                                                    <Clock className="h-4 w-4 text-blue-500" />
                                                    <span>Votes counted instantly</span>
                                                </div>
                                                <div className="text-xs text-slate-400 pt-2 border-t border-slate-200">
                                                    <span>Price includes 3% processing fee</span>
                                                </div>
                                            </div>
                                        </>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Calendar className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                                            <h3 className="text-xl font-semibold text-slate-900 mb-2">Voting Closed</h3>
                                            <p className="text-slate-600">
                                                Voting is not currently active for this category.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Other Nominees - Linear-inspired sidebar */}
                            {other_nominees.length > 0 && (
                                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                    <CardHeader>
                                        <CardTitle className="flex items-center space-x-2 text-slate-900">
                                            <Users className="h-5 w-5 text-blue-600" />
                                            <span>Other Nominees</span>
                                        </CardTitle>
                                        <CardDescription className="text-slate-600">
                                            Discover other amazing candidates
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {other_nominees.filter(other => other && other.slug && other.display_name).map((other) => (
                                                <Link 
                                                    key={other.slug}
                                                    href={`/vote/nominee/${other.slug}`}
                                                    className="flex items-center space-x-4 p-4 rounded-xl bg-slate-50 hover:bg-blue-50 transition-all duration-300 group border-0"
                                                >
                                                    <Avatar className="h-12 w-12 border-0">
                                                        <AvatarImage src={other.profile_image} alt={other.display_name} />
                                                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                                                            {other.display_name?.charAt(0) || 'N'}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div className="flex-1">
                                                        <h4 className="font-semibold text-slate-900 group-hover:text-blue-700 transition-colors">
                                                            {other.display_name}
                                                        </h4>
                                                        <div className="flex items-center space-x-2 text-sm text-slate-600">
                                                            <Heart className="h-3 w-3 text-red-500" />
                                            <span>{(other.total_votes || 0).toLocaleString()} votes</span>
                                        </div>
                                                    </div>
                                                    <ChevronRight className="h-5 w-5 text-slate-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-300" />
                                                </Link>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Category Info */}
                            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-none hover:bg-white/90 transition-all duration-300">
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2 text-slate-900">
                                        <Trophy className="h-5 w-5 text-yellow-600" />
                                        <span>Category Details</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <h4 className="font-semibold text-slate-900 mb-2">{category?.name || 'Category Name'}</h4>
                                        <p className="text-slate-600 text-sm leading-relaxed">{category?.description || 'Category description not available.'}</p>
                                    </div>
                                    {category?.voting_start_date && category?.voting_end_date && (
                                        <div className="flex items-center space-x-2 text-sm bg-slate-50 rounded-xl p-3">
                                            <Calendar className="h-4 w-4 text-blue-600" />
                                            <span className="text-slate-700">
                                                Voting: {new Date(category.voting_start_date).toLocaleDateString()} - {new Date(category.voting_end_date).toLocaleDateString()}
                                            </span>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
} 