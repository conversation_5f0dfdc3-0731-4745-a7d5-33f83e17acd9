import { useState, FormEvent } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Search, 
    Trophy, 
    Users, 
    Star,
    AlertCircle,
    CheckCircle,
    ArrowRight,
    Sparkles,
    Crown,
    Vote,
    Target,
    Info
} from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';

interface SeoData {
    title?: string;
    description?: string;
    keywords?: string;
    og_title?: string;
    og_description?: string;
    canonical_url?: string;
}

interface Props {
    seo?: SeoData;
    errors?: {
        nominee_code?: string;
    };
}

export default function NomineeVotingForm({ seo, errors }: Props) {
    const [nomineeCode, setNomineeCode] = useState('');
    const [isSearching, setIsSearching] = useState(false);

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        
        if (!nomineeCode.trim()) {
            return;
        }

        setIsSearching(true);
        
        router.post('/vote/nominee/search', {
            nominee_code: nomineeCode.trim()
        }, {
            onFinish: () => setIsSearching(false),
            preserveScroll: true
        });
    };

    return (
        <>
            <Head>
                <title>{seo?.title || 'Vote for Your Favorite Nominee - VoteYourFav'}</title>
                <meta name="description" content={seo?.description} />
                <meta name="keywords" content={seo?.keywords} />
                
                {/* Open Graph tags */}
                <meta property="og:title" content={seo?.og_title} />
                <meta property="og:description" content={seo?.og_description} />
                <meta property="og:type" content="website" />
                <meta property="og:site_name" content="VoteYourFav" />
                
                {/* Canonical URL */}
                {seo?.canonical_url && <link rel="canonical" href={seo.canonical_url} />}
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
                {/* Header */}
                <header className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <AppLogoIcon className="h-8 w-8" />
                                <div>
                                    <h1 className="text-xl font-bold text-gray-900">VoteYourFav</h1>
                                    <p className="text-xs text-gray-600">Vote for Your Favorites</p>
                                </div>
                            </div>
                            <nav className="hidden md:flex items-center space-x-6">
                                <a href="/vote" className="text-gray-600 hover:text-blue-600 transition-colors">
                                    Browse Categories
                                </a>
                                <a href="/results" className="text-gray-600 hover:text-blue-600 transition-colors">
                                    Results
                                </a>
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main className="container mx-auto px-4 py-12">
                    <div className="max-w-2xl mx-auto">

                        {/* Search Form */}
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                            <CardHeader className="text-center pb-6">
                                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                                    <Target className="w-6 h-6 text-blue-600" />
                                    Find Your Nominee
                                </CardTitle>
                                <CardDescription className="text-gray-600">
                                    Enter the nominee's unique code to access their voting page
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Error Alert */}
                                {errors?.nominee_code && (
                                    <Alert variant="destructive" className="border-red-200 bg-red-50">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>{errors.nominee_code}</AlertDescription>
                                    </Alert>
                                )}

                                {/* Info Alert */}
                                <Alert className="border-blue-200 bg-blue-50">
                                    <Info className="h-4 w-4 text-blue-600" />
                                    <AlertDescription className="text-blue-800">
                                        <strong>Nominee codes can be:</strong>
                                        <br />
                                        • 5-character codes (e.g., AB123)
                                        • Full nominee codes (e.g., John-Doe-VYF789)
                                    </AlertDescription>
                                </Alert>

                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="nominee_code" className="text-lg font-medium text-gray-900">
                                            Nominee Code
                                        </Label>
                                        <div className="relative">
                                            <Input
                                                id="nominee_code"
                                                type="text"
                                                value={nomineeCode}
                                                onChange={(e) => setNomineeCode(e.target.value)}
                                                placeholder="Enter nominee code (e.g., AB123 or John-Doe-VYF789)"
                                                className="text-lg py-6 pl-12 pr-4 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                                                disabled={isSearching}
                                                required
                                            />
                                            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                        </div>
                                    </div>

                                    <Button
                                        type="submit"
                                        disabled={isSearching || !nomineeCode.trim()}
                                        className="w-full py-6 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
                                    >
                                        {isSearching ? (
                                            <>
                                                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                                Searching...
                                            </>
                                        ) : (
                                            <>
                                                Find Nominee
                                                <ArrowRight className="w-5 h-5 ml-2" />
                                            </>
                                        )}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Features Section */}
                        <div className="mt-12 grid md:grid-cols-3 gap-6">
                            <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-100">
                                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
                                    <Star className="w-6 h-6 text-blue-600" />
                                </div>
                                <h3 className="font-semibold text-gray-900 mb-2">Easy Voting</h3>
                                <p className="text-sm text-gray-600">
                                    Simple and secure voting process with multiple payment options
                                </p>
                            </div>

                            <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-100">
                                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
                                    <Crown className="w-6 h-6 text-purple-600" />
                                </div>
                                <h3 className="font-semibold text-gray-900 mb-2">Multiple Categories</h3>
                                <p className="text-sm text-gray-600">
                                    Vote for nominees across various award categories
                                </p>
                            </div>

                            <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-100">
                                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                                    <Trophy className="w-6 h-6 text-green-600" />
                                </div>
                                <h3 className="font-semibold text-gray-900 mb-2">Real-time Results</h3>
                                <p className="text-sm text-gray-600">
                                    Track voting progress and see live rankings
                                </p>
                            </div>
                        </div>

                        {/* Browse Categories CTA */}
                        <div className="mt-12 text-center">
                            <p className="text-gray-600 mb-4">
                                Don't have a nominee code? Browse nominees by category
                            </p>
                            <Button
                                variant="outline"
                                size="lg"
                                asChild
                                className="border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
                            >
                                <a href="/vote">
                                    <Users className="w-5 h-5 mr-2" />
                                    Browse All Categories
                                </a>
                            </Button>
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer className="mt-16 py-8 border-t border-gray-200 bg-white/80 backdrop-blur-md">
                    <div className="container mx-auto px-4 text-center">
                        <div className="flex items-center justify-center space-x-2 mb-2">
                            <AppLogoIcon className="h-6 w-6" />
                            <span className="font-semibold text-gray-900">VoteYourFav</span>
                        </div>
                        <p className="text-sm text-gray-600">
                            Your trusted platform for secure and transparent voting
                        </p>
                    </div>
                </footer>
            </div>
        </>
    );
}
