import { Head, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Clock, AlertCircle, Calendar } from 'lucide-react';
import { route } from 'ziggy-js';

interface Category {
    id: number;
    name: string;
    description: string;
    organization_name: string;
    voting_start_date: string | null;
    voting_end_date: string | null;
}

interface CategoryNotAvailableProps {
    category: Category;
    message: string;
    reason: string;
}

export default function CategoryNotAvailable({ category, message, reason }: CategoryNotAvailableProps) {
    const getStatusInfo = () => {
        if (reason === 'voting_not_active') {
            const now = new Date();
            const startDate = category.voting_start_date ? new Date(category.voting_start_date) : null;
            const endDate = category.voting_end_date ? new Date(category.voting_end_date) : null;

            if (startDate && now < startDate) {
                return {
                    title: 'Voting Not Started',
                    subtitle: `Voting will begin on ${startDate.toLocaleDateString()}`,
                    icon: <Calendar className="h-12 w-12 text-blue-500" />,
                    color: 'blue'
                };
            } else if (endDate && now > endDate) {
                return {
                    title: 'Voting Has Ended',
                    subtitle: `Voting ended on ${endDate.toLocaleDateString()}`,
                    icon: <Clock className="h-12 w-12 text-gray-500" />,
                    color: 'gray'
                };
            }
        }

        return {
            title: 'Category Not Available',
            subtitle: 'This category is currently not available for voting',
            icon: <AlertCircle className="h-12 w-12 text-red-500" />,
            color: 'red'
        };
    };

    const statusInfo = getStatusInfo();

    return (
        <>
            <Head title={`${category.name} - Not Available`} />
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                {/* Header */}
                <div className="bg-white/80 backdrop-blur-sm border-b border-white/50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex items-center justify-between">
                            <Link
                                href={route('welcome')}
                                className="inline-flex items-center text-slate-600 hover:text-slate-900 transition-colors duration-200"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Back to Home
                            </Link>
                            <div className="text-sm text-slate-500">
                                {category.organization_name}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <div className="text-center">
                        {/* Status Icon */}
                        <div className="flex justify-center mb-6">
                            {statusInfo.icon}
                        </div>

                        {/* Category Name */}
                        <h1 className="text-4xl font-bold text-slate-900 mb-4">
                            {category.name}
                        </h1>

                        {/* Status Title */}
                        <h2 className={`text-2xl font-semibold mb-4 ${
                            statusInfo.color === 'blue' ? 'text-blue-600' :
                            statusInfo.color === 'gray' ? 'text-gray-600' :
                            'text-red-600'
                        }`}>
                            {statusInfo.title}
                        </h2>

                        {/* Status Subtitle */}
                        <p className="text-lg text-slate-600 mb-6">
                            {statusInfo.subtitle}
                        </p>

                        {/* Category Description */}
                        {category.description && (
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50 mb-8 max-w-2xl mx-auto">
                                <h3 className="text-lg font-semibold text-slate-900 mb-3">About This Category</h3>
                                <p className="text-slate-600 leading-relaxed">
                                    {category.description}
                                </p>
                            </div>
                        )}

                        {/* Voting Dates */}
                        {(category.voting_start_date || category.voting_end_date) && (
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50 mb-8 max-w-2xl mx-auto">
                                <h3 className="text-lg font-semibold text-slate-900 mb-4">Voting Schedule</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    {category.voting_start_date && (
                                        <div className="text-center">
                                            <div className="text-sm text-slate-500 mb-1">Starts</div>
                                            <div className="text-lg font-semibold text-slate-900">
                                                {new Date(category.voting_start_date).toLocaleDateString()}
                                            </div>
                                        </div>
                                    )}
                                    {category.voting_end_date && (
                                        <div className="text-center">
                                            <div className="text-sm text-slate-500 mb-1">Ends</div>
                                            <div className="text-lg font-semibold text-slate-900">
                                                {new Date(category.voting_end_date).toLocaleDateString()}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Actions */}
                        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                            <Link
                                href={route('welcome')}
                                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
                            >
                                <ArrowLeft className="w-5 h-5 mr-2" />
                                Explore Other Categories
                            </Link>
                            
                            {statusInfo.color === 'blue' && (
                                <button
                                    onClick={() => window.location.reload()}
                                    className="inline-flex items-center px-8 py-4 bg-white/80 backdrop-blur-sm text-slate-700 rounded-full font-semibold text-lg hover:bg-white hover:shadow-lg hover:shadow-slate-500/10 transition-all duration-300 border border-white/50"
                                >
                                    Check Again
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
