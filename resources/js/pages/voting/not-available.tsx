import { Head } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
    Alert<PERSON>ircle, 
    ArrowLeft, 
    Clock,
    Info
} from 'lucide-react';

interface Nominee {
    id: number;
    slug: string;
    display_name: string;
    bio?: string;
    profile_image?: string;
}

interface Props {
    nominee: Nominee;
    message: string;
    reason: string;
}

export default function NotAvailable({ nominee, message, reason }: Props) {
    const getReasonIcon = () => {
        switch (reason) {
            case 'category_not_assigned':
                return <Clock className="h-8 w-8 text-orange-600" />;
            case 'voting_ended':
                return <AlertCircle className="h-8 w-8 text-red-600" />;
            case 'voting_not_started':
                return <Clock className="h-8 w-8 text-blue-600" />;
            default:
                return <Info className="h-8 w-8 text-gray-600" />;
        }
    };

    const getReasonTitle = () => {
        switch (reason) {
            case 'category_not_assigned':
                return 'Voting Not Yet Available';
            case 'voting_ended':
                return 'Voting Has Ended';
            case 'voting_not_started':
                return 'Voting Hasn\'t Started';
            default:
                return 'Voting Not Available';
        }
    };

    const getReasonColor = () => {
        switch (reason) {
            case 'category_not_assigned':
                return 'border-orange-200 bg-orange-50';
            case 'voting_ended':
                return 'border-red-200 bg-red-50';
            case 'voting_not_started':
                return 'border-blue-200 bg-blue-50';
            default:
                return 'border-gray-200 bg-gray-50';
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
            <Head title={`${nominee.display_name} - Voting Not Available`} />
            
            <div className="container mx-auto px-4 py-12">
                <div className="max-w-2xl mx-auto space-y-8">
                    {/* Back Button */}
                    <Button 
                        variant="ghost" 
                        onClick={() => window.history.back()}
                        className="mb-4"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Go Back
                    </Button>

                    {/* Nominee Profile */}
                    <Card>
                        <CardHeader className="text-center">
                            <div className="flex justify-center mb-4">
                                <Avatar className="h-24 w-24">
                                    <AvatarImage src={nominee.profile_image} alt={nominee.display_name} />
                                    <AvatarFallback className="text-2xl">
                                        {nominee.display_name.split(' ').map(n => n[0]).join('')}
                                    </AvatarFallback>
                                </Avatar>
                            </div>
                            <CardTitle className="text-2xl">{nominee.display_name}</CardTitle>
                            {nominee.bio && (
                                <CardDescription className="text-center mt-2">
                                    {nominee.bio}
                                </CardDescription>
                            )}
                        </CardHeader>
                    </Card>

                    {/* Status Message */}
                    <Card className={getReasonColor()}>
                        <CardHeader>
                            <div className="flex items-center justify-center space-x-3">
                                {getReasonIcon()}
                                <CardTitle className="text-xl">{getReasonTitle()}</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="text-center space-y-4">
                            <p className="text-gray-700">{message}</p>
                            
                            {reason === 'category_not_assigned' && (
                                <div className="bg-white rounded-lg p-4 border">
                                    <h4 className="font-medium text-gray-900 mb-2">What does this mean?</h4>
                                    <p className="text-sm text-gray-600">
                                        This nominee hasn't been assigned to any voting categories yet. 
                                        The voting will become available once an administrator assigns 
                                        them to a category and activates voting.
                                    </p>
                                </div>
                            )}

                            <div className="pt-4">
                                <Button 
                                    onClick={() => window.location.href = '/'}
                                    className="bg-blue-600 hover:bg-blue-700 text-white"
                                >
                                    Return to Home
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Additional Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Stay Updated</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600 text-sm">
                                Check back later or contact the event organizers for more information 
                                about when voting will become available for this nominee.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
} 