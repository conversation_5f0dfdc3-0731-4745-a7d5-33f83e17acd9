import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>L<PERSON><PERSON>, Trophy, Users, Vote, Clock, Star, ArrowRight } from 'lucide-react';
import { route } from 'ziggy-js';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Nominee {
    id: number;
    slug: string;
    display_name: string;
    bio: string;
    profile_image: string | null;
    total_votes: number;
    ranking: number;
    can_receive_votes: boolean;
}

interface Category {
    id: number;
    name: string;
    description: string;
    slug: string;
    price_per_vote: number;
    discount_percentage: number;
    discount_min_votes: number;
    voting_start_date: string;
    voting_end_date: string;
    organization_name: string;
    is_voting_active: boolean;
}

interface Stats {
    total_nominees: number;
    total_votes: number;
    top_nominee: Nominee | null;
}

interface CategoryProps {
    category: Category;
    nominees: Nominee[];
    stats: Stats;
}

export default function CategoryShow({ category, nominees, stats }: CategoryProps) {
    const getRankingColor = (ranking: number) => {
        switch (ranking) {
            case 1:
                return 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white';
            case 2:
                return 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800';
            case 3:
                return 'bg-gradient-to-r from-amber-600 to-orange-600 text-white';
            default:
                return 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white';
        }
    };

    const getRankingIcon = (ranking: number) => {
        if (ranking <= 3) {
            return <Trophy className="h-4 w-4" />;
        }
        return <Star className="h-4 w-4" />;
    };

    return (        <>
            <Head title={`${category.name} - Vote for Your Favorite`}>
                <meta name="description" content={`Vote in the ${category.name} category on VoteYourFav. View ${stats.total_nominees} nominees, ${stats.total_votes.toLocaleString()} total votes cast. Support your favorite nominees in this exciting competition.`} />
                <meta name="keywords" content={`vote, voting, award, ${category.name}, nomination, contest, competition, Ghana, VoteYourFav, nominees`} />
                  {/* Open Graph tags */}
                <meta property="og:title" content={`${category.name} - Vote for Your Favorite on VoteYourFav`} />
                <meta property="og:description" content={`Discover and vote for amazing nominees in the ${category.name} category. ${stats.total_nominees} nominees competing with ${stats.total_votes.toLocaleString()} votes cast so far.`} />
                <meta property="og:url" content={route('vote.showCategory', category.slug)} />
                <meta property="og:type" content="website" />
                <meta property="og:image" content={`${window.location.origin}/voteyourfav_logo.png`} />
                
                {/* Twitter Card tags */}
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:title" content={`${category.name} - Vote for Your Favorite`} />
                <meta name="twitter:description" content={`Vote for amazing nominees in the ${category.name} category. ${stats.total_nominees} nominees, ${stats.total_votes.toLocaleString()} votes cast. Join the competition on VoteYourFav!`} />
                <meta name="twitter:image" content={`${window.location.origin}/voteyourfav_logo.png`} />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                {/* Header */}
                <div className="bg-white/80 backdrop-blur-sm border-b border-white/50 sticky top-0 z-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex items-center justify-between">
                            <Link
                                href={route('welcome')}
                                className="inline-flex items-center text-slate-600 hover:text-slate-900 transition-colors duration-200"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Back to Home
                            </Link>
                            <div className="text-sm text-slate-500">
                                {category.organization_name}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Category Header */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center mb-8">
                        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
                            {category.name}
                        </h1>
                        <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-6">
                            {category.description}
                        </p>
                        
                        {/* Category Stats */}
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Users className="h-6 w-6 text-blue-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">{stats.total_nominees}</div>
                                <div className="text-sm text-slate-600">Nominees</div>
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Vote className="h-6 w-6 text-green-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">{stats.total_votes.toLocaleString()}</div>
                                <div className="text-sm text-slate-600">Total Votes</div>
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Clock className="h-6 w-6 text-purple-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">₵{Number(category.price_per_vote).toFixed(2)}</div>
                                <div className="text-sm text-slate-600">Per Vote</div>
                            </div>
                        </div>
                    </div>

                    {/* Nominees Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {nominees.map((nominee) => (
                            <div
                                key={nominee.id}
                                className="group bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 overflow-hidden hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300"
                            >
                                {/* Ranking Badge */}
                                <div className="absolute top-4 left-4 z-10">
                                    <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-semibold ${getRankingColor(nominee.ranking)}`}>
                                        {getRankingIcon(nominee.ranking)}
                                        #{nominee.ranking}
                                    </div>
                                </div>

                                {/* Profile Image */}
                                <div className="relative h-64 bg-gradient-to-br from-slate-200 to-slate-300 overflow-hidden">
                                    <Avatar className="h-full w-full rounded-none">
                                        <AvatarImage 
                                            src={nominee.profile_image || undefined} 
                                            alt={nominee.display_name} 
                                            className="h-full w-full object-cover group-hover:scale-105 transition-transform duration-300"
                                        />
                                        <AvatarFallback className="h-full w-full flex items-center justify-center bg-slate-200 rounded-none">
                                            <Users className="h-16 w-16 text-slate-400" />
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                </div>

                                {/* Nominee Info */}
                                <div className="p-6">
                                    <h3 className="text-xl font-semibold text-slate-900 mb-2 truncate">
                                        {nominee.display_name}
                                    </h3>
                                    
                                    <p className="text-slate-600 text-sm leading-relaxed mb-4 line-clamp-3">
                                        {nominee.bio || 'No bio available.'}
                                    </p>
                                    
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center gap-2">
                                            <Vote className="h-4 w-4 text-green-600" />
                                            <span className="text-lg font-bold text-slate-900">{nominee.total_votes.toLocaleString()}</span>
                                            <span className="text-sm text-slate-500">votes</span>
                                        </div>
                                    </div>
                                    
                                    {nominee.can_receive_votes ? (
                                        <Link
                                            href={route('vote.show', nominee.slug)}
                                            className="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full text-sm font-medium hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
                                        >
                                            Vote Now
                                            <ArrowRight className="h-4 w-4 ml-2" />
                                        </Link>
                                    ) : (
                                        <button
                                            disabled
                                            className="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-400 text-white rounded-full text-sm font-medium cursor-not-allowed"
                                        >
                                            Voting Closed
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Empty State */}
                    {nominees.length === 0 && (
                        <div className="text-center py-16">
                            <Users className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-slate-900 mb-2">No Nominees Yet</h3>
                            <p className="text-slate-600 max-w-md mx-auto">
                                There are no nominees in this category at the moment. Check back later!
                            </p>
                        </div>
                    )}

                    {/* Back to Home */}
                    <div className="text-center mt-12">
                        <Link
                            href={route('welcome')}
                            className="inline-flex items-center px-8 py-4 bg-white/80 backdrop-blur-sm text-slate-700 rounded-full font-semibold text-lg hover:bg-white hover:shadow-lg hover:shadow-slate-500/10 transition-all duration-300 border border-white/50"
                        >
                            <ArrowLeft className="w-5 h-5 mr-2" />
                            Explore More Categories
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
