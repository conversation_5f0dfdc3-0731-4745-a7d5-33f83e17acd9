import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>Left, Trophy, Users, Vote, Clock, Star, ArrowRight, Search } from 'lucide-react';
import { route } from 'ziggy-js';
import { useState } from 'react';

interface Category {
    id: number;
    name: string;
    description: string;
    slug: string;
    organization_name: string;
    price_per_vote: number;
    voting_start_date: string | null;
    voting_end_date: string | null;
    is_voting_active: boolean;
    nominees_count: number;
    votes_count: number;
}

interface Stats {
    total_categories: number;
    active_categories: number;
    total_votes: number;
}

interface CategoriesProps {
    categories: Category[];
    stats: Stats;
}

const categoryStyles = [
    { icon: "🚀", gradient: "from-purple-500 to-pink-500" },
    { icon: "🎵", gradient: "from-blue-500 to-cyan-500" },
    { icon: "📱", gradient: "from-green-500 to-emerald-500" },
    { icon: "💡", gradient: "from-orange-500 to-red-500" },
    { icon: "⭐", gradient: "from-indigo-500 to-purple-500" },
    { icon: "🤝", gradient: "from-teal-500 to-blue-500" },
    { icon: "🏆", gradient: "from-yellow-500 to-amber-500" },
    { icon: "🎨", gradient: "from-pink-500 to-rose-500" },
];

export default function Categories({ categories, stats }: CategoriesProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
    const [filterOrganization, setFilterOrganization] = useState<string>('all');

    // Get unique organizations for the filter dropdown
    const uniqueOrganizations = Array.from(
        new Set(categories.map(category => category.organization_name))
    ).sort();

    const filteredCategories = categories.filter(category => {
        const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            category.organization_name.toLowerCase().includes(searchTerm.toLowerCase());
        
        const matchesFilter = filterStatus === 'all' || 
                            (filterStatus === 'active' && category.is_voting_active) ||
                            (filterStatus === 'inactive' && !category.is_voting_active);

        const matchesOrganization = filterOrganization === 'all' ||
                            category.organization_name === filterOrganization;
        
        return matchesSearch && matchesFilter && matchesOrganization;
    });    return (
        <>
            <Head title="All Categories - Vote for Your Favorites">
                <meta name="description" content="Explore all voting categories on VoteYourFav. Discover award categories, browse nominees, and cast your votes in ongoing competitions. Join thousands of voters supporting their favorite nominees." />
                <meta property="og:type" content="website" />
                <meta property="og:title" content="All Voting Categories - VoteYourFav" />
                <meta property="og:description" content="Browse all available voting categories and discover amazing nominees competing for awards. Cast your votes and support your favorites on VoteYourFav's secure voting platform." />
                <meta name="twitter:card" content="summary" />
                <meta name="twitter:title" content="All Voting Categories - VoteYourFav" />
                <meta name="twitter:description" content="Explore all voting categories and support your favorite nominees. Join the excitement of competitive voting on VoteYourFav platform." />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
                {/* Header */}
                <div className="bg-white/80 backdrop-blur-sm border-b border-white/50 sticky top-0 z-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex items-center justify-between">
                            <Link
                                href={route('welcome')}
                                className="inline-flex items-center text-slate-600 hover:text-slate-900 transition-colors duration-200"
                            >
                                <ArrowLeft className="h-5 w-5 mr-2" />
                                Back to Home
                            </Link>
                            <div className="flex items-center space-x-4">
                                <Link
                                    href="/vote/nominee"
                                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-full font-medium hover:bg-green-700 transition-all duration-300 text-sm"
                                >
                                    <Search className="w-4 h-4 mr-2" />
                                    Vote by Code
                                </Link>
                                <Link
                                    href="/results"
                                    className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-full font-medium hover:bg-indigo-700 transition-all duration-300 text-sm"
                                >
                                    <Trophy className="w-4 h-4 mr-2" />
                                    View Results
                                </Link>
                                <div className="text-sm text-slate-500">
                                    All Voting Categories
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Hero Section */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center mb-8">
                        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
                            All Categories
                        </h1>
                        <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-6">
                            Discover all voting categories and cast your votes for your favorites
                        </p>
                        
                        {/* Stats */}
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto mb-8">
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Trophy className="h-6 w-6 text-blue-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">{stats.total_categories}</div>
                                <div className="text-sm text-slate-600">Total Categories</div>
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Clock className="h-6 w-6 text-green-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">{stats.active_categories}</div>
                                <div className="text-sm text-slate-600">Active Voting</div>
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
                                <div className="flex items-center justify-center mb-2">
                                    <Vote className="h-6 w-6 text-purple-600" />
                                </div>
                                <div className="text-2xl font-bold text-slate-900">{stats.total_votes.toLocaleString()}</div>
                                <div className="text-sm text-slate-600">Total Votes</div>
                            </div>
                        </div>

                        {/* Search and Filter */}
                        <div className="flex flex-col sm:flex-row gap-4 max-w-4xl mx-auto">
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                                <input
                                    type="text"
                                    placeholder="Search categories..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-3 bg-white/60 backdrop-blur-sm border border-white/50 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'inactive')}
                                className="px-4 py-3 bg-white/60 backdrop-blur-sm border border-white/50 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="all">All Status</option>
                                <option value="active">Active Voting</option>
                                <option value="inactive">Voting Closed</option>
                            </select>
                            <select
                                value={filterOrganization}
                                onChange={(e) => setFilterOrganization(e.target.value)}
                                className="px-4 py-3 bg-white/60 backdrop-blur-sm border border-white/50 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="all">All Organizations</option>
                                {uniqueOrganizations.map((organization) => (
                                    <option key={organization} value={organization}>
                                        {organization}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Categories Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                        {filteredCategories.map((category, index) => {
                            const style = categoryStyles[index % categoryStyles.length];
                            return (
                                <Link
                                    key={category.id}
                                    href={route('vote.showCategory', category.slug)}
                                    className="group relative bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 overflow-hidden hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300 cursor-pointer"
                                >
                                    <div className={`absolute inset-0 bg-gradient-to-br ${style.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
                                    
                                    {/* Status Badge */}
                                    <div className="absolute top-4 right-4 z-10">
                                        <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold ${
                                            category.is_voting_active 
                                                ? 'bg-green-100 text-green-800' 
                                                : 'bg-gray-100 text-gray-600'
                                        }`}>
                                            <div className={`w-2 h-2 rounded-full ${
                                                category.is_voting_active ? 'bg-green-500' : 'bg-gray-400'
                                            }`}></div>
                                            {category.is_voting_active ? 'Active' : 'Closed'}
                                        </div>
                                    </div>

                                    <div className="relative p-6">
                                        
                                        <h3 className="text-xl font-semibold text-slate-900 mb-2 truncate" title={category.name}>
                                            {category.name}
                                        </h3>
                                        
                                        <div className="text-sm text-blue-600 font-medium mb-3 truncate" title={category.organization_name}>
                                            {category.organization_name}
                                        </div>
                                        
                                        <p className="text-slate-600 text-sm leading-relaxed mb-4 line-clamp-3" title={category.description}>
                                            {category.description}
                                        </p>
                                        
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="flex items-center gap-2">
                                                <Users className="h-4 w-4 text-slate-500" />
                                                <span className="text-sm text-slate-600">{category.nominees_count} nominees</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-slate-500">From</span>
                                                <span className="text-lg font-bold text-slate-900">₵{Number(category.price_per_vote).toFixed(2)}</span>
                                            </div>
                                        </div>
                                        
                                        <div className="flex items-center justify-center">
                                            <div className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                                                category.is_voting_active
                                                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white group-hover:from-blue-700 group-hover:to-indigo-700 group-hover:shadow-lg group-hover:shadow-blue-500/25'
                                                    : 'bg-gray-200 text-gray-600'
                                            }`}>
                                                {category.is_voting_active ? 'Browse & Vote' : 'View Results'}
                                                <ArrowRight className="w-4 h-4 ml-1 inline" />
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            );
                        })}
                    </div>

                    {/* Empty State */}
                    {filteredCategories.length === 0 && (
                        <div className="text-center py-16">
                            <Trophy className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-slate-900 mb-2">No Categories Found</h3>
                            <p className="text-slate-600 max-w-md mx-auto mb-6">
                                {searchTerm || filterStatus !== 'all' || filterOrganization !== 'all' 
                                    ? 'Try adjusting your search or filter criteria.' 
                                    : 'There are no voting categories available at the moment.'}
                            </p>
                            {(searchTerm || filterStatus !== 'all' || filterOrganization !== 'all') && (
                                <button
                                    onClick={() => {
                                        setSearchTerm('');
                                        setFilterStatus('all');
                                        setFilterOrganization('all');
                                    }}
                                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300"
                                >
                                    Clear Filters
                                </button>
                            )}
                        </div>
                    )}

                    {/* Vote by Code Promotion */}
                    <div className="mt-16 mb-12">
                        <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 backdrop-blur-sm rounded-2xl p-8 border border-white/50 text-center">
                            <div className="max-w-2xl mx-auto">
                                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mb-6">
                                    <Search className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-2xl font-bold text-slate-900 mb-4">
                                    Have a Nominee Code?
                                </h3>
                                <p className="text-lg text-slate-600 mb-6">
                                    Skip browsing and vote directly using your nominee's unique code. 
                                    Fast, easy, and secure voting in just a few clicks.
                                </p>
                                <Link
                                    href="/vote/nominee"
                                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-full font-semibold text-lg hover:from-green-700 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                                >
                                    <Search className="w-5 h-5 mr-2" />
                                    Vote by Nominee Code
                                    <ArrowRight className="w-5 h-5 ml-2" />
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Back to Home */}
                    <div className="text-center">
                        <Link
                            href={route('welcome')}
                            className="inline-flex items-center px-8 py-4 bg-white/80 backdrop-blur-sm text-slate-700 rounded-full font-semibold text-lg hover:bg-white hover:shadow-lg hover:shadow-slate-500/10 transition-all duration-300 border border-white/50"
                        >
                            <ArrowLeft className="w-5 h-5 mr-2" />
                            Back to Home
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
