/* This file is generated by Ziggy. */
declare module 'ziggy-js' {
  interface RouteList {
    "home": [],
    "categories.index": [],
    "category.show": [
        {
            "name": "slug",
            "required": true
        }
    ],
    "vote.show": [
        {
            "name": "slug",
            "required": true
        }
    ],
    "vote.calculate-price": [
        {
            "name": "slug",
            "required": true
        }
    ],
    "vote.initiate-payment": [
        {
            "name": "slug",
            "required": true
        }
    ],
    "payment.webhook": [],
    "payment.callback": [],
    "payment.verify": [],
    "payment.status": [],
    "payment.public-key": [],
    "dashboard": [],
    "nomination.create": [],
    "nomination.apply": [],
    "profile.edit": [],
    "profile.update": [],
    "profile.destroy": [],
    "password.edit": [],
    "password.update": [],
    "appearance": [],
    "register": [],
    "login": [],
    "password.request": [],
    "password.email": [],
    "password.reset": [
        {
            "name": "token",
            "required": true
        }
    ],
    "password.store": [],
    "verification.notice": [],
    "verification.verify": [
        {
            "name": "id",
            "required": true
        },
        {
            "name": "hash",
            "required": true
        }
    ],
    "verification.send": [],
    "password.confirm": [],
    "logout": [],
    "admin.dashboard": [],
    "admin.dashboard.index": [],
    "admin.analytics.index": [],
    "admin.analytics.export": [],
    "admin.categories.index": [],
    "admin.categories.create": [],
    "admin.categories.store": [],
    "admin.categories.show": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.edit": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.update": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.destroy": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.toggle-status": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.nominees": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.assign-nominee": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.categories.remove-nominee": [
        {
            "name": "category",
            "required": true,
            "binding": "slug"
        },
        {
            "name": "nominee_profile_id",
            "required": true
        }
    ],
    "admin.payments.index": [],
    "admin.payments.show": [
        {
            "name": "payment",
            "required": true,
            "binding": "id"
        }
    ],
    "admin.org-users.index": [],
    "admin.org-users.create": [],
    "admin.org-users.store": [],
    "admin.org-users.show": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.edit": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.update": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.destroy": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.toggle-status": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.pending-approvals": [],
    "admin.org-users.approve": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.org-users.reject": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.index": [],
    "admin.users.create": [],
    "admin.users.store": [],
    "admin.users.show": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.edit": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.update": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.destroy": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.toggle-status": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.pending-approvals": [],
    "admin.users.approve": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.users.reject": [
        {
            "name": "user",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.organizations.index": [],
    "admin.organizations.create": [],
    "admin.organizations.store": [],
    "admin.organizations.show": [
        {
            "name": "organization",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.organizations.edit": [
        {
            "name": "organization",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.organizations.update": [
        {
            "name": "organization",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.organizations.destroy": [
        {
            "name": "organization",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.organizations.toggle-status": [
        {
            "name": "organization",
            "required": true,
            "binding": "slug"
        }
    ],
    "admin.settings.index": [],
    "admin.settings.update": [],
    "admin.settings.backup": [],
    "admin.settings.logs": [],
    "storage.local": [
        {
            "name": "path",
            "required": true
        }
    ]
}
}
export {};
