const Ziggy = {"url":"http:\/\/votecaster.test","port":null,"defaults":{},"routes":{"telescope":{"uri":"telescope\/{view?}","methods":["GET","HEAD"],"wheres":{"view":"(.*)"},"parameters":["view"]},"telegram.webhook":{"uri":"api\/telegram\/webhook","methods":["POST"]},"telegram.set-webhook":{"uri":"api\/telegram\/set-webhook","methods":["GET","HEAD"]},"telegram.bot-info":{"uri":"api\/telegram\/bot-info","methods":["GET","HEAD"]},"telegram.test":{"uri":"api\/telegram\/test","methods":["GET","HEAD"]},"ussd.webhook":{"uri":"api\/ussd\/webhook","methods":["POST"]},"paystack.webhook":{"uri":"api\/ussd\/payment-callback","methods":["POST"]},"ussd.test":{"uri":"api\/ussd\/test","methods":["GET","HEAD"]},"ussdk.":{"uri":"api\/ussdk\/initiate-payment","methods":["POST"]},"nalo.":{"uri":"api\/nalo\/health","methods":["GET","HEAD"]},"nalo.ussd.webhook":{"uri":"api\/nalo\/ussd\/webhook","methods":["POST"]},"welcome":{"uri":"\/","methods":["GET","HEAD"]},"robots":{"uri":"robots.txt","methods":["GET","HEAD"]},"sitemap":{"uri":"sitemap.xml","methods":["GET","HEAD"]},"vote.categories":{"uri":"vote","methods":["GET","HEAD"]},"vote.showCategory":{"uri":"vote\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"vote.show":{"uri":"vote\/nominee\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"vote.calculate-price":{"uri":"vote\/{slug}\/calculate-price","methods":["POST"],"parameters":["slug"]},"vote.initiate-payment":{"uri":"vote\/{slug}\/initiate-payment","methods":["POST"],"parameters":["slug"]},"payment.callback":{"uri":"payment\/callback","methods":["GET","HEAD"]},"payment.verify":{"uri":"payment\/verify","methods":["POST"]},"payment.status":{"uri":"payment\/status","methods":["GET","HEAD"]},"payment.public-key":{"uri":"payment\/public-key","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"nomination.create":{"uri":"apply-for-nomination","methods":["GET","HEAD"]},"nomination.apply":{"uri":"apply-for-nomination","methods":["POST"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"admin.dashboard":{"uri":"admin","methods":["GET","HEAD"]},"admin.dashboard.index":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.analytics.index":{"uri":"admin\/analytics","methods":["GET","HEAD"]},"admin.analytics.export":{"uri":"admin\/analytics\/export","methods":["GET","HEAD"]},"admin.categories.index":{"uri":"admin\/categories","methods":["GET","HEAD"]},"admin.categories.create":{"uri":"admin\/categories\/create","methods":["GET","HEAD"]},"admin.categories.store":{"uri":"admin\/categories","methods":["POST"]},"admin.categories.show":{"uri":"admin\/categories\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.edit":{"uri":"admin\/categories\/{category}\/edit","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.update":{"uri":"admin\/categories\/{category}","methods":["PUT","PATCH"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.destroy":{"uri":"admin\/categories\/{category}","methods":["DELETE"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.toggle-status":{"uri":"admin\/categories\/{category}\/toggle-status","methods":["POST"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.nominees":{"uri":"admin\/categories\/{category}\/nominees","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.assign-nominee":{"uri":"admin\/categories\/{category}\/nominees","methods":["POST"],"parameters":["category"],"bindings":{"category":"slug"}},"admin.categories.remove-nominee":{"uri":"admin\/categories\/{category}\/nominees\/{nominee_profile_id}","methods":["DELETE"],"parameters":["category","nominee_profile_id"],"bindings":{"category":"slug"}},"admin.payments.index":{"uri":"admin\/payments","methods":["GET","HEAD"]},"admin.payments.show":{"uri":"admin\/payments\/{payment}","methods":["GET","HEAD"],"parameters":["payment"],"bindings":{"payment":"id"}},"admin.org-users.index":{"uri":"admin\/org-users","methods":["GET","HEAD"]},"admin.org-users.create":{"uri":"admin\/org-users\/create","methods":["GET","HEAD"]},"admin.org-users.store":{"uri":"admin\/org-users","methods":["POST"]},"admin.org-users.show":{"uri":"admin\/org-users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.edit":{"uri":"admin\/org-users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.update":{"uri":"admin\/org-users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.destroy":{"uri":"admin\/org-users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.toggle-status":{"uri":"admin\/org-users\/{user}\/toggle-status","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.pending-approvals":{"uri":"admin\/org-users\/pending\/approvals","methods":["GET","HEAD"]},"admin.org-users.approve":{"uri":"admin\/org-users\/{user}\/approve","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.org-users.reject":{"uri":"admin\/org-users\/{user}\/reject","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.toggle-status":{"uri":"admin\/users\/{user}\/toggle-status","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.pending-approvals":{"uri":"admin\/users\/pending\/approvals","methods":["GET","HEAD"]},"admin.users.approve":{"uri":"admin\/users\/{user}\/approve","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.users.reject":{"uri":"admin\/users\/{user}\/reject","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.pending-organizations.index":{"uri":"admin\/pending-organizations","methods":["GET","HEAD"]},"admin.pending-organizations.approve":{"uri":"admin\/pending-organizations\/{user}\/approve","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.pending-organizations.reject":{"uri":"admin\/pending-organizations\/{user}\/reject","methods":["POST"],"parameters":["user"],"bindings":{"user":"slug"}},"admin.organizations.index":{"uri":"admin\/organizations","methods":["GET","HEAD"]},"admin.organizations.create":{"uri":"admin\/organizations\/create","methods":["GET","HEAD"]},"admin.organizations.store":{"uri":"admin\/organizations","methods":["POST"]},"admin.organizations.show":{"uri":"admin\/organizations\/{organization}","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"slug"}},"admin.organizations.edit":{"uri":"admin\/organizations\/{organization}\/edit","methods":["GET","HEAD"],"parameters":["organization"],"bindings":{"organization":"slug"}},"admin.organizations.update":{"uri":"admin\/organizations\/{organization}","methods":["PUT","PATCH"],"parameters":["organization"],"bindings":{"organization":"slug"}},"admin.organizations.destroy":{"uri":"admin\/organizations\/{organization}","methods":["DELETE"],"parameters":["organization"],"bindings":{"organization":"slug"}},"admin.organizations.toggle-status":{"uri":"admin\/organizations\/{organization}\/toggle-status","methods":["POST"],"parameters":["organization"],"bindings":{"organization":"slug"}},"admin.settings.index":{"uri":"admin\/settings","methods":["GET","HEAD"]},"admin.settings.update":{"uri":"admin\/settings","methods":["PUT"]},"admin.settings.backup":{"uri":"admin\/settings\/backup","methods":["GET","HEAD"]},"admin.settings.logs":{"uri":"admin\/settings\/logs","methods":["GET","HEAD"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
