<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="google-site-verification" content="HQ_bz1Gx35RDDCLtcqRpNZptlSAO355H0NAm1Unrac0" />

        {{-- SEO Meta Tags --}}
        <meta name="description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly voting platform. Sign up free!">
        <meta name="keywords" content="online voting, awards, contests, elections, voting platform, Ghana, VoteYourFav, nominees, polls">
        <meta name="author" content="Vote Your Fav">
        <meta name="robots" content="index, follow">
        <meta name="language" content="{{ str_replace('_', '-', app()->getLocale()) }}">
        
        {{-- Open Graph Meta Tags (Social Media Sharing) --}}
        <meta property="og:site_name" content="{{ config('app.name', 'Vote Your Fav') }}">
        <meta property="og:type" content="website">
        <meta property="og:title" content="{{ config('app.name', 'Vote Your Fav') }} - Online Voting Platform">
        <meta property="og:description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly voting platform.">
        <meta property="og:image" content="{{ config('app.url') }}/voteyourfav_logo.png">
        <meta property="og:url" content="{{ config('app.url') }}">
        <meta property="og:locale" content="{{ str_replace('_', '-', app()->getLocale()) }}">
        
        {{-- Twitter Card Meta Tags --}}
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="{{ config('app.name', 'Vote Your Fav') }} - Online Voting Platform">
        <meta name="twitter:description" content="Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly.">
        <meta name="twitter:image" content="{{ config('app.url') }}/voteyourfav_logo.png">
        
        {{-- Mobile/PWA Meta Tags --}}
        <meta name="theme-color" content="#3b82f6">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'Vote Your Fav') }}">
        
        {{-- Canonical URL --}}
        <link rel="canonical" href="{{ url()->current() }}">

        {{-- Inline script to detect system dark mode preference and apply it immediately --}}
        {{-- Removed script as we always want light mode --}}

        {{-- Inline style to set the HTML background color based on our theme in app.css --}}
        <style>
            html {
                background-color: oklch(1 0 0);
            }

            html.dark {
                background-color: oklch(0.145 0 0);
            }
        </style>

        <title inertia>{{ config('app.name', 'Vote Your Fav') }}</title>

        <link rel="icon" href="/voteyourfav_logo.png" type="image/png">
        <link rel="apple-touch-icon" href="/voteyourfav_logo.png">

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

        {{-- Structured Data (JSON-LD) for SEO --}}
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "{{ config('app.name', 'Vote Your Fav') }}",
            "description": "Host, manage, and participate in online voting for awards, contests, and elections. Secure, transparent, and user-friendly voting platform.",
            "url": "{{ config('app.url') }}",
            "applicationCategory": "VotingApplication",
            "operatingSystem": "Web Browser",
            "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
            },
            "creator": {
                "@type": "Organization",
                "name": "VoteYourFav",
                "url": "{{ config('app.url') }}"
            }
        }
        </script>

        @routes
        @viteReactRefresh
        @vite('resources/js/app.tsx')
        @inertiaHead
    </head>
    <body class="font-sans antialiased">
        @inertia
    </body>
</html>
