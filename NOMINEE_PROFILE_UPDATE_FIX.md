# Nominee Profile Picture Update Fix

## Issue
Nominees were unable to update their profile pictures. The "Update Image" button in the nominee dashboard redirected to a profile settings page that only handled basic user information (name and email) but not nominee profile data including profile pictures.

## Root Cause
1. The `ProfileController` only handled basic `User` model fields
2. The `ProfileUpdateRequest` only validated basic user fields
3. The profile settings frontend page only had fields for name and email
4. No functionality existed to update `NomineeProfile` data including `profile_image`

## Solution

### 1. Updated ProfileController (`app/Http/Controllers/Settings/ProfileController.php`)
- Added nominee profile data loading in the `edit()` method
- Enhanced `update()` method to handle nominee profile updates including file uploads
- Added profile image upload/deletion logic with proper storage handling
- Added support for updating all nominee profile fields (display_name, bio, achievements, why_vote_for_me, social_links)
- Enhanced `destroy()` method to clean up profile images on account deletion

### 2. Updated ProfileUpdateRequest (`app/Http/Requests/Settings/ProfileUpdateRequest.php`)
- Added conditional validation rules for nominee users
- Added profile image validation (max 5MB, image types: jpeg, png, jpg, gif)
- Added validation for all nominee profile fields with appropriate limits
- Added custom validation messages for better UX

### 3. Updated Profile Settings Frontend (`resources/js/pages/settings/profile.tsx`)
- Complete rewrite to support nominee profile editing
- Added profile picture upload with preview functionality
- Added sections for:
  - Profile Picture (with upload, preview, and remove functionality)
  - Nominee Profile Details (display_name, bio, achievements, why_vote_for_me)
  - Social Media Links (Facebook, Twitter, Instagram, LinkedIn, YouTube, TikTok)
- Used Card components for better organization
- Added proper form handling for file uploads using POST with _method=PATCH
- Added loading states and success feedback

## Key Features Added

### Profile Picture Management
- Upload new profile pictures (JPG, PNG, GIF up to 5MB)
- Real-time preview of selected images
- Remove uploaded images
- Automatic cleanup of old images when new ones are uploaded
- Fallback display when no image is present

### Comprehensive Profile Editing
- Display name editing
- Biography editing (2000 char limit)
- Achievements section (2000 char limit)
- "Why vote for me" section (2000 char limit)
- Social media links for all major platforms

### User Experience Improvements
- Organized layout using Cards
- Only shows nominee-specific fields for nominee users
- Clear validation messages
- Loading states during form submission
- Success feedback after updates
- Proper file upload handling

## Files Modified

1. `app/Http/Controllers/Settings/ProfileController.php` - Enhanced to handle nominee profiles
2. `app/Http/Requests/Settings/ProfileUpdateRequest.php` - Added nominee field validation
3. `resources/js/pages/settings/profile.tsx` - Complete rewrite with nominee support

## Technical Details

### File Upload Handling
- Uses Laravel's Storage facade with 'public' disk
- Stores images in 'profile-images' directory
- Automatically deletes old images when updating
- Proper multipart/form-data handling

### Security & Validation
- File type validation (images only)
- File size limits (5MB)
- Text field length limits
- Proper sanitization of user inputs

### Database Structure
The fix works with the existing `nominee_profiles` table structure:
- `profile_image` - stores the file path
- `display_name` - nominee's display name
- `bio` - biography text
- `achievements` - achievements text
- `why_vote_for_me` - campaign message
- `social_links` - JSON field for social media URLs

## How to Test

1. Login as a nominee user
2. Navigate to Settings → Profile (or click "Update Image" from nominee dashboard)
3. Upload a new profile picture and verify preview works
4. Update nominee profile fields (bio, achievements, etc.)
5. Update social media links
6. Submit the form and verify all changes are saved
7. Check that the profile picture appears correctly on the nominee dashboard and public voting pages

## Notes

- The fix maintains backward compatibility with existing non-nominee users
- Profile picture uploads are handled securely with proper validation
- The UI is responsive and follows the existing design patterns
- Form submission uses POST with `_method=PATCH` to handle file uploads properly