
                {/* Popular Categories Section */}
                <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 sm:pb-24 lg:pb-32">
                    <div className="text-center mb-12 sm:mb-16 lg:mb-20">
                        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-slate-900 mb-4 sm:mb-6 px-2" style={{ fontFamily: 'Urbanist, sans-serif' }}>
                            Popular{' '}
                            <span 
                                className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"
                                style={{ fontFamily: 'Instrument Serif, serif', fontStyle: 'italic' }}
                            >
                                Categories
                            </span>
                        </h2>
                        <p className="text-base sm:text-lg md:text-xl text-slate-600 max-w-2xl mx-auto px-4">
                            Discover trending voting categories across different organizations
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                        {popularCategories.filter(category => category && category.id).map((category, index) => {
                            const style = categoryStyles[index % categoryStyles.length];
                            return (
                            <Link 
                                key={category.id}
                                href={route('vote.showCategory', { slug: category.slug })}
                                className="group relative bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 overflow-hidden hover:bg-white/80 hover:shadow-xl hover:shadow-slate-500/10 hover:-translate-y-2 transition-all duration-300 cursor-pointer"
                            >
                                {/* <WaveEffect className="opacity-30 group-hover:opacity-50 transition-opacity duration-300" color="#6366f1" intensity={0.2} /> */}
                                    <div className={`absolute inset-0 bg-gradient-to-br ${style.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
                                <div className="relative p-6">
                                   
                                    
                                        <h3 className="text-xl font-semibold text-slate-900 mb-2 truncate" title={category.name || ''} style={{ fontFamily: 'Urbanist, sans-serif' }}>
                                            {category.name || 'Untitled Category'}
                                    </h3>
                                    
                                        <div className="text-sm text-blue-600 font-medium mb-3 truncate" title={category.organization_name || ''}>
                                            {category.organization_name || 'Unknown Organization'}
                                    </div>
                                    
                                        <p className="text-slate-600 text-sm leading-relaxed mb-4 line-clamp-3" title={category.description || ''}>
                                        {category.description || 'No description available.'}
                                    </p>
                                    
                                    <div className="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-2">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm text-slate-500">From</span>
                                                <span className="text-lg font-bold text-slate-900">₵{Number(category.price_per_vote || 0).toFixed(2)}</span>
                                            <span className="text-sm text-slate-500">per vote</span>
                                        </div>
                                        <div className="w-full sm:w-auto px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full text-sm font-medium group-hover:from-blue-700 group-hover:to-indigo-700 group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300 flex items-center justify-center">
                                            Browse & Vote
                                            <ArrowRight className="w-4 h-4 ml-1 inline" />
                                        </div>
                                    </div>
                                </div>
                            </Link>
                            );
                        })}
                    </div>

                    <div className="text-center">
                        <Link
                            href={route('vote.categories')}
                            className="inline-flex items-center px-8 py-4 bg-white/80 backdrop-blur-sm text-slate-700 rounded-full font-semibold text-lg hover:bg-white hover:shadow-lg hover:shadow-slate-500/10 transition-all duration-300 border border-white/50"
                        >
                            View All Categories
                            <ArrowRight className="w-5 h-5 ml-2" />
                        </Link>
                    </div>
                </div>