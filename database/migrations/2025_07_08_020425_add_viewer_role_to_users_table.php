<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the existing role column
            $table->dropColumn('role');
        });

        Schema::table('users', function (Blueprint $table) {
            // Add the new role column with viewer included
            $table->enum('role', ['super_admin', 'admin', 'nominee', 'viewer'])->default('nominee')->after('email_verified_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the role column
            $table->dropColumn('role');
        });

        Schema::table('users', function (Blueprint $table) {
            // Restore the original role column without viewer
            $table->enum('role', ['super_admin', 'admin', 'nominee'])->default('nominee')->after('email_verified_at');
        });
    }
};
