<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('award_categories', function (Blueprint $table) {
            // Drop the old unique index on the 'slug' column
            $table->dropUnique('award_categories_slug_unique');

            // Add a new compound unique index for 'slug' and 'organization_id'
            $table->unique(['slug', 'organization_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('award_categories', function (Blueprint $table) {
            // Drop the compound unique index
            $table->dropUnique(['slug', 'organization_id']);

            // Re-add the old unique index on 'slug'
            $table->unique('slug');
        });
    }
};
