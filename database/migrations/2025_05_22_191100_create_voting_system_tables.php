<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create organizations table
        Schema::create('organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('website')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Add voting fields to users table
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['super_admin', 'admin', 'nominee'])->default('nominee')->after('email_verified_at');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null')->after('role');
            $table->string('phone')->nullable()->after('organization_id');
            $table->text('bio')->nullable()->after('phone');
            $table->string('profile_image')->nullable()->after('bio');
            $table->string('slug')->unique()->nullable()->after('profile_image');
            $table->boolean('is_suspended')->default(false)->after('slug');
            $table->string('suspension_reason')->nullable()->after('is_suspended');
            $table->text('suspension_notice')->nullable()->after('suspension_reason');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('pending')->after('suspension_notice');
            $table->timestamp('approved_at')->nullable()->after('approval_status');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null')->after('approved_at');
            $table->text('approval_notes')->nullable()->after('approved_by');
            $table->text('rejection_reason')->nullable()->after('approval_notes');
        });

        // Create award categories table
        Schema::create('award_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->datetime('voting_start_date');
            $table->datetime('voting_end_date');
            $table->decimal('price_per_vote', 8, 2); // Price in GHS
            $table->decimal('discount_percentage', 5, 2)->nullable()->default(0); // Discount percentage
            $table->integer('discount_min_votes')->nullable()->default(0); // Minimum votes for discount
            $table->boolean('is_active')->default(true);
            $table->boolean('is_suspended')->default(false);
            $table->string('suspension_reason')->nullable();
            $table->text('suspension_notice')->nullable();
            $table->timestamps();
        });

        // Create payments table
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('reference')->unique(); // Paystack reference
            $table->string('paystack_reference')->nullable(); // Paystack transaction reference
            $table->decimal('amount', 8, 2); // Amount in GHS
            $table->string('currency', 3)->default('GHS');
            $table->enum('status', ['pending', 'success', 'failed', 'cancelled'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->string('gateway_response')->nullable();
            $table->json('metadata')->nullable(); // Additional Paystack metadata
            $table->timestamp('paid_at')->nullable();
            $table->string('customer_email');
            $table->string('customer_name')->nullable();
            $table->string('customer_phone')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
        Schema::dropIfExists('award_categories');
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role',
                'organization_id',
                'phone',
                'bio',
                'profile_image',
                'slug',
                'is_suspended',
                'suspension_reason',
                'suspension_notice',
                'approval_status',
                'approved_at',
                'approved_by',
                'approval_notes',
                'rejection_reason'
            ]);
        });
        
        Schema::dropIfExists('organizations');
    }
}; 