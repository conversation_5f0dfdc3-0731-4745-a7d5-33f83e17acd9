<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create nominee profiles table (without direct award_category_id)
        Schema::create('nominee_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('slug')->unique();
            $table->string('nominee_code', 6)->unique()->nullable();
            $table->string('display_name');
            $table->text('bio')->nullable();
            $table->string('profile_image')->nullable();
            $table->json('additional_images')->nullable(); // Array of additional images
            $table->json('social_links')->nullable(); // Social media links
            $table->json('achievements')->nullable(); // List of achievements
            $table->text('why_vote_for_me')->nullable();
            $table->integer('total_votes')->default(0);
            $table->integer('link_clicks')->default(0);
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_suspended')->default(false);
            $table->string('suspension_reason')->nullable();
            $table->text('suspension_notice')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });

        // Create award_category_nominee_profile pivot table
        Schema::create('award_category_nominee_profile', function (Blueprint $table) {
            $table->id();
            $table->foreignId('award_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('nominee_profile_id')->constrained()->onDelete('cascade');
            $table->timestamp('assigned_at')->nullable();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['award_category_id', 'nominee_profile_id'], 'category_nominee_unique');
        });

        // Create votes table
        Schema::create('votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('nominee_profile_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_id')->constrained()->onDelete('cascade');
            $table->string('voter_name')->nullable();
            $table->string('voter_email')->nullable();
            $table->string('voter_phone')->nullable();
            $table->integer('vote_count');
            $table->decimal('amount_paid', 8, 2); // Amount in GHS
            $table->decimal('discount_applied', 5, 2)->default(0);
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('votes');
        Schema::dropIfExists('award_category_nominee_profile');
        Schema::dropIfExists('nominee_profiles');
    }
}; 