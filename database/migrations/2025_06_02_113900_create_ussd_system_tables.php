<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create USSD sessions table
        Schema::create('ussd_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id', 100)->unique(); // Limit session_id length
            $table->string('msisdn', 15);
            $table->string('user_id', 20)->default('');
            $table->string('network', 10);
            $table->json('menu_state')->nullable(); // Current menu context
            $table->json('user_data')->nullable(); // Temporary data storage
            $table->string('current_screen', 50)->default('main_menu');
            $table->string('last_input')->nullable();
            $table->timestamp('last_activity');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['session_id', 'is_active']);
            $table->index(['msisdn', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ussd_sessions');
    }
}; 