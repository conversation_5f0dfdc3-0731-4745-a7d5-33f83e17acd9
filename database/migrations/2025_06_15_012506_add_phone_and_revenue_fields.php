<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add phone number field to users table if it doesn't exist
        if (!Schema::hasColumn('users', 'phone_number')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('phone_number')->nullable()->after('phone');
            });
        }

        // Add revenue percentage field to organizations table
        Schema::table('organizations', function (Blueprint $table) {
            $table->decimal('revenue_percentage', 5, 2)->default(10.00)->after('is_active')
                  ->comment('Percentage of revenue this organization pays to the platform');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'phone_number')) {
                $table->dropColumn('phone_number');
            }
        });

        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn('revenue_percentage');
        });
    }
};
