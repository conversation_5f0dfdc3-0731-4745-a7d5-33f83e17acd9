<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('award_categories', function (Blueprint $table) {
            // Drop the compound unique index
            $table->dropUnique(['slug', 'organization_id']);

            // Re-add the old unique index on 'slug'
            $table->unique('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('award_categories', function (Blueprint $table) {
            // Drop the single unique index
            $table->dropUnique(['slug']);

            // Re-add the compound unique index
            $table->unique(['slug', 'organization_id']);
        });
    }
};
