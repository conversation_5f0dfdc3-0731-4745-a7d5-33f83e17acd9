<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use App\Models\Organization;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class NomineeDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Assuming 'UENR CSS' is the organization.
        // You might want to make this more dynamic if needed.
        $organization = Organization::firstOrCreate(
            ['name' => 'UENR CSS'],
            ['description' => 'UENR CSS Awards']
        );

        $nominees = [
            [
                'name' => 'Aklamati Precious',
                'email' => '<EMAIL>',
                'phone' => '0201113937',
                'image' =>  null,
                'category' => 'Fresher of the Year',
            ],
            [
                'name' => 'Faustina Asantewaa',
                'email' => '<EMAIL>',
                'phone' => '0542589640',
                'image' => null,
                'category' => 'Most Popular Student of the Year',
            ],
            [
                'name' => '<PERSON><PERSON><PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '0537463778',
                'image' => null,
                'category' => 'Face of CSS (Male)',
            ],
            [
                'name' => 'Nartey Ishmael',
                'email' => '<EMAIL>',
                'phone' => '0531865923',
                'image' => null,
                'category' => 'Best Pal',
            ],
            [
                'name' => 'Esther Akpene Dokosi',
                'email' => '<EMAIL>',
                'phone' => '0534691113',
                'image' => null,
                'category' => 'Lady of the Year',
            ],
            [
                'name' => 'NPANGNA BALATIGNAN ISAAC',
                'email' => '<EMAIL>',
                'phone' => '0536438015',
                'image' => null,
                'category' => 'Designer of the Year',
            ],
            [
                'name' => 'Adu Brimah',
                'email' => '<EMAIL>',
                'phone' => '0554371582',
                'image' => null,
                'category' => 'Gentleman Of the Year',
            ],
            [
                'name' => 'Takyi Romanus',
                'email' => '<EMAIL>',
                'phone' => '0541090471',
                'image' => null,
                'category' => 'Programmer of the Year',
            ],
            [
                'name' => 'FRIMPONG OSEI CHRIS',
                'email' => '<EMAIL>',
                'phone' => '0547648208',
                'image' => null,
                'category' => 'Face of CSS (Male)',
            ],
            [
                'name' => 'ACHEAW RAPHAEL',
                'email' => '<EMAIL>',
                'phone' => '0549960176',
                'image' => null,
                'category' => 'Face of CSS (Male)',
            ],
            [
                'name' => 'Nsiah Anthoinette',
                'email' => '<EMAIL>',
                'phone' => '0596101872',
                'image' => null,
                'category' => 'Face of CSS (Female)',
            ],
            [
                'name' => 'Boamah Kingsley Owusu',
                'email' => '<EMAIL>',
                'phone' => '0596243439',
                'image' => null,
                'category' => 'Gentleman Of the Year',
            ],
        ];

        foreach ($nominees as $nomineeData) {
            $user = User::firstOrCreate(
                ['email' => $nomineeData['email']],
                [
                    'name' => $nomineeData['name'],
                    'password' => Hash::make('password'), // Default password
                    'organization_id' => $organization->id,
                    'role' => User::ROLE_NOMINEE,
                ]
            );

            $category = AwardCategory::firstOrCreate(
                ['name' => $nomineeData['category'], 'organization_id' => $organization->id],
                [
                    'description' => $nomineeData['category'],
                    'voting_start_date' => now(),
                    'voting_end_date' => now()->addWeek(),
                    'price_per_vote' => 1.0,
                ]
            );

            $nomineeProfile = NomineeProfile::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'display_name' => $nomineeData['name'],
                    'bio' => 'Nominee for ' . $nomineeData['category'],
                    'profile_image' => $nomineeData['image'],
                    'is_approved' => true,
                ]
            );

            // Attach the category to the nominee profile
            $nomineeProfile->awardCategories()->syncWithoutDetaching([$category->id]);
        }
    }
}
