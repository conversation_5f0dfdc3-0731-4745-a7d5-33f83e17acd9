<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportSqlDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🗂️  Importing data from new.sql...');

        // Disable foreign key checks for SQLite
        DB::statement('PRAGMA foreign_keys = OFF');

        try {
            // Import organizations first
            $this->importOrganizations();
            
            // Import award categories
            $this->importAwardCategories();
            
            // Import users
            $this->importUsers();
            
            // Import nominee profiles
            $this->importNomineeProfiles();
            
            // Import category-nominee relationships
            $this->importCategoryNomineeRelationships();
            
            // Import votes if any
            $this->importVotes();
            
            // Import payments if any
            $this->importPayments();

            $this->command->info('✅ Successfully imported all data!');

        } catch (\Exception $e) {
            $this->command->error('❌ Error importing data: ' . $e->getMessage());
            Log::error('SQL Import Error: ' . $e->getMessage());
        } finally {
            // Re-enable foreign key checks
            DB::statement('PRAGMA foreign_keys = ON');
        }
    }

    private function importOrganizations()
    {
        $this->command->line('📋 Importing organizations...');
        
        // Create organizations from SQL data
        $organizations = [
            [
                'id' => 1,
                'name' => 'UENR CSS Awards',
                'slug' => 'uenr-css-awards',
                'description' => 'Celebrating excellence in technology and innovation.',
                'logo' => null,
                'website' => null,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+233 20 123 4567',
                'is_active' => 1,
                'revenue_percentage' => 12.00,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 12:51:00'
            ],
            [
                'id' => 4,
                'name' => 'Khing Koats',
                'slug' => 'khing-koats',
                'description' => null,
                'logo' => null,
                'website' => null,
                'contact_email' => null,
                'contact_phone' => null,
                'is_active' => 0,
                'revenue_percentage' => 10.00,
                'created_at' => '2025-06-15 14:46:16',
                'updated_at' => '2025-06-15 14:46:16'
            ],
            [
                'id' => 5,
                'name' => 'manny enterprize',
                'slug' => 'manny-enterprize',
                'description' => null,
                'logo' => null,
                'website' => null,
                'contact_email' => null,
                'contact_phone' => null,
                'is_active' => 0,
                'revenue_percentage' => 10.00,
                'created_at' => '2025-06-16 09:28:17',
                'updated_at' => '2025-06-16 09:28:17'
            ],
            [
                'id' => 6,
                'name' => 'Tech Stuff',
                'slug' => 'tech-stuff',
                'description' => null,
                'logo' => null,
                'website' => null,
                'contact_email' => null,
                'contact_phone' => null,
                'is_active' => 0,
                'revenue_percentage' => 10.00,
                'created_at' => '2025-06-25 12:13:44',
                'updated_at' => '2025-06-25 12:13:44'
            ],
            [
                'id' => 7,
                'name' => 'UENR ITS Excellence Awards',
                'slug' => 'uenr-its-excellence-awards',
                'description' => null,
                'logo' => null,
                'website' => null,
                'contact_email' => null,
                'contact_phone' => null,
                'is_active' => 1,
                'revenue_percentage' => 20.00,
                'created_at' => '2025-06-28 19:50:11',
                'updated_at' => '2025-06-28 19:50:47'
            ]
        ];

        foreach ($organizations as $organization) {
            DB::table('organizations')->insert($organization);
        }
    }

    private function importAwardCategories()
    {
        $this->command->line('🏆 Importing award categories...');
        
        $categories = [
            [
                'id' => 1,
                'name' => 'Executive of the Year',
                'slug' => 'executive-of-the-year',
                'description' => 'Honoring outstanding leadership and executive excellence in the tech community.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 13:05:49'
            ],
            [
                'id' => 2,
                'name' => 'Most Popular Student of the Year',
                'slug' => 'most-popular-student-of-the-year',
                'description' => 'Recognizing the most well-known and influential student in the academic community.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 3,
                'name' => 'Most Influential Student of the Year',
                'slug' => 'most-influential-student-of-the-year',
                'description' => 'Celebrating students who have made significant impact on campus and beyond.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 4,
                'name' => 'Face of CSS (male)',
                'slug' => 'face-of-css-male',
                'description' => 'Recognizing the male student who best represents the Computer Science Society.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 5,
                'name' => 'Face of CSS (female)',
                'slug' => 'face-of-css-female',
                'description' => 'Recognizing the female student who best represents the Computer Science Society.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 6,
                'name' => 'Programmer of the Year',
                'slug' => 'programmer-of-the-year',
                'description' => 'Honoring exceptional programming talent and achievements.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 7,
                'name' => 'Designer of the Year',
                'slug' => 'designer-of-the-year',
                'description' => 'Celebrating outstanding design talent and creativity.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 8,
                'name' => 'Gentleman of the Year',
                'slug' => 'gentleman-of-the-year',
                'description' => 'Recognizing male students exemplifying excellent character and conduct.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 9,
                'name' => 'Most Fashionable Student of the Year (male)',
                'slug' => 'most-fashionable-student-of-the-year-male',
                'description' => 'Celebrating male students with outstanding style and fashion sense.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 10,
                'name' => 'Most Fashionable Student of the Year (female)',
                'slug' => 'most-fashionable-student-of-the-year-female',
                'description' => 'Celebrating female students with outstanding style and fashion sense.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 11,
                'name' => 'Course Rep of the Year',
                'slug' => 'course-rep-of-the-year',
                'description' => 'Honoring exceptional service and leadership as a course representative.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 12,
                'name' => 'Entrepreneur of the Year',
                'slug' => 'entrepreneur-of-the-year',
                'description' => 'Recognizing outstanding student entrepreneurship and business acumen.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 13,
                'name' => 'Content Creator of the Year',
                'slug' => 'content-creator-of-the-year',
                'description' => 'Celebrating exceptional digital content creation and social media influence.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 14,
                'name' => 'Best Pal',
                'slug' => 'best-pal',
                'description' => 'Recognizing students known for their exceptional friendship and camaraderie.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 15,
                'name' => 'Fresher of the Year',
                'slug' => 'fresher-of-the-year',
                'description' => 'Celebrating outstanding first-year students who have made a significant impact.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 16,
                'name' => 'Sports Personality of the Year',
                'slug' => 'sports-personality-of-the-year',
                'description' => 'Honoring excellence in sports and athletic achievements.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 17,
                'name' => 'Class of the Year',
                'slug' => 'class-of-the-year',
                'description' => 'Recognizing the most outstanding academic class or cohort.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 18,
                'name' => 'Photogenic',
                'slug' => 'photogenic',
                'description' => 'Celebrating students with exceptional camera presence and photogenic qualities.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 19,
                'name' => 'Student Politician of the Year',
                'slug' => 'student-politician-of-the-year',
                'description' => 'Honoring excellence in student politics and campus leadership.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ],
            [
                'id' => 20,
                'name' => 'Most Popular Fresher of the Year',
                'slug' => 'most-popular-fresher-of-the-year',
                'description' => 'Recognizing the most well-known and popular first-year student.',
                'organization_id' => 1,
                'voting_start_date' => '2025-06-15 10:36:00',
                'voting_end_date' => '2025-09-15 10:36:00',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
                'is_active' => 1,
                'is_suspended' => 0,
                'created_at' => '2025-06-15 10:36:00',
                'updated_at' => '2025-06-15 10:36:00'
            ]
        ];

        foreach ($categories as $category) {
            DB::table('award_categories')->insert($category);
        }
    }

    private function importUsers()
    {
        $this->command->line('👥 Importing users...');
        
        // Sample users for nominees
        $users = [
            [
                'id' => 22,
                'name' => 'Morrison',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456789',
                'phone_number' => '+233123456789',
                'bio' => 'Sample bio for Morrison',
                'profile_image' => null,
                'slug' => 'morrison',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-29 03:19:08',
                'updated_at' => '2025-06-29 03:19:08'
            ],
            [
                'id' => 23,
                'name' => 'Student Leader',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456790',
                'phone_number' => '+233123456790',
                'bio' => 'Dedicated student representative',
                'profile_image' => null,
                'slug' => 'student-leader',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-19 20:46:20',
                'updated_at' => '2025-06-19 20:46:20'
            ],
            [
                'id' => 24,
                'name' => 'Female Leader',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456791',
                'phone_number' => '+233123456791',
                'bio' => 'Inspiring female leader in tech',
                'profile_image' => null,
                'slug' => 'female-leader',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-24 17:19:23',
                'updated_at' => '2025-06-24 17:19:23'
            ],
            [
                'id' => 25,
                'name' => 'Gentleman',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456792',
                'phone_number' => '+233123456792',
                'bio' => 'Exemplary character and conduct',
                'profile_image' => null,
                'slug' => 'gentleman',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-25 20:01:48',
                'updated_at' => '2025-06-25 20:01:48'
            ],
            [
                'id' => 26,
                'name' => 'Fashion King',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456793',
                'phone_number' => '+233123456793',
                'bio' => 'Style icon and fashion enthusiast',
                'profile_image' => null,
                'slug' => 'fashion-king',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-27 11:35:51',
                'updated_at' => '2025-06-27 11:35:51'
            ],
            [
                'id' => 27,
                'name' => 'Male Representative',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456794',
                'phone_number' => '+233123456794',
                'bio' => 'Representing the best of CSS',
                'profile_image' => null,
                'slug' => 'male-representative',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-27 13:48:22',
                'updated_at' => '2025-06-27 13:48:22'
            ],
            [
                'id' => 28,
                'name' => 'Second Gentleman',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'remember_token' => null,
                'role' => 'nominee',
                'organization_id' => 1,
                'phone' => '+233123456795',
                'phone_number' => '+233123456795',
                'bio' => 'Another exemplary candidate',
                'profile_image' => null,
                'slug' => 'second-gentleman',
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => null,
                'approval_notes' => null,
                'rejection_reason' => null,
                'created_at' => '2025-06-29 03:19:08',
                'updated_at' => '2025-06-29 03:19:08'
            ]
        ];

        foreach ($users as $user) {
            try {
                $result = DB::table('users')->insert($user);
                $this->command->line('  ✓ Inserted user: ' . $user['name'] . ' (result: ' . $result . ')');
            } catch (\Exception $e) {
                $this->command->error('  ✗ Failed to insert user: ' . $user['name'] . ' - ' . $e->getMessage());
            }
        }
    }

    private function importNomineeProfiles()
    {
        $this->command->line('🎯 Importing nominee profiles...');
        
        // Sample nominee profiles from the SQL data
        $nominees = [
            [
                'id' => 5,
                'user_id' => 22,
                'slug' => 'morrison',
                'nominee_code' => 'ROZ91',
                'display_name' => 'Morrison',
                'bio' => 'Morrison - representing excellence in leadership',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Outstanding leadership in community projects',
                'why_vote_for_me' => 'I represent the values of excellence and integrity that our community needs.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-29 03:19:08',
                'updated_at' => '2025-06-29 03:19:08'
            ],
            [
                'id' => 6,
                'user_id' => 23,
                'slug' => 'student-leader',
                'nominee_code' => 'STU92',
                'display_name' => 'Student Leader',
                'bio' => 'Student Leader - Course Representative of the Year candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Leading student initiatives and academic excellence',
                'why_vote_for_me' => 'Leading with integrity and vision for our student community.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-19 20:46:20',
                'updated_at' => '2025-06-19 20:46:20'
            ],
            [
                'id' => 7,
                'user_id' => 24,
                'slug' => 'female-leader',
                'nominee_code' => 'FEM93',
                'display_name' => 'Female Leader',
                'bio' => 'Female Leader - Face of CSS (female) candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Pioneering women in technology and leadership',
                'why_vote_for_me' => 'Empowering women in technology and inspiring the next generation.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-24 17:19:23',
                'updated_at' => '2025-06-24 17:19:23'
            ],
            [
                'id' => 8,
                'user_id' => 25,
                'slug' => 'gentleman',
                'nominee_code' => 'GEN94',
                'display_name' => 'Gentleman',
                'bio' => 'Gentleman - Gentleman of the Year candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Exemplary character and community service',
                'why_vote_for_me' => 'Leading by example with honor and integrity in all aspects.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-25 20:01:48',
                'updated_at' => '2025-06-25 20:01:48'
            ],
            [
                'id' => 9,
                'user_id' => 26,
                'slug' => 'fashion-king',
                'nominee_code' => 'FAS95',
                'display_name' => 'Fashion King',
                'bio' => 'Fashion King - Most Fashionable Student (male) candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Style icon and fashion trendsetter',
                'why_vote_for_me' => 'Fashion is my passion and expression of creativity.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-27 11:35:51',
                'updated_at' => '2025-06-27 11:35:51'
            ],
            [
                'id' => 10,
                'user_id' => 27,
                'slug' => 'male-representative',
                'nominee_code' => 'MAL96',
                'display_name' => 'Male Representative',
                'bio' => 'Male Representative - Face of CSS (male) candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Outstanding representation of CSS values',
                'why_vote_for_me' => 'The face that represents our values and aspirations.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-27 13:48:22',
                'updated_at' => '2025-06-27 13:48:22'
            ],
            [
                'id' => 11,
                'user_id' => 28,
                'slug' => 'second-gentleman',
                'nominee_code' => 'GEN97',
                'display_name' => 'Second Gentleman',
                'bio' => 'Second Gentleman - Gentleman of the Year candidate',
                'profile_image' => null,
                'additional_images' => null,
                'social_links' => json_encode(['instagram' => null, 'twitter' => null, 'facebook' => null]),
                'achievements' => 'Consistent character and leadership excellence',
                'why_vote_for_me' => 'Character defines true gentlemen and I exemplify this daily.',
                'total_votes' => 0,
                'link_clicks' => 0,
                'is_approved' => 1,
                'is_suspended' => 0,
                'suspension_reason' => null,
                'suspension_notice' => null,
                'admin_notes' => null,
                'created_at' => '2025-06-29 03:19:08',
                'updated_at' => '2025-06-29 03:19:08'
            ]
        ];

        foreach ($nominees as $nominee) {
            DB::table('nominee_profiles')->insert($nominee);
        }
    }

    private function importCategoryNomineeRelationships()
    {
        $this->command->line('🔗 Importing category-nominee relationships...');
        
        $relationships = [
            ['id' => 5, 'award_category_id' => 2, 'nominee_profile_id' => 5, 'assigned_at' => '2025-06-19 10:14:57', 'created_at' => '2025-06-19 10:14:57', 'updated_at' => '2025-06-19 10:14:57'],
            ['id' => 6, 'award_category_id' => 11, 'nominee_profile_id' => 6, 'assigned_at' => '2025-06-19 20:46:20', 'created_at' => '2025-06-19 20:46:20', 'updated_at' => '2025-06-19 20:46:20'],
            ['id' => 7, 'award_category_id' => 5, 'nominee_profile_id' => 7, 'assigned_at' => '2025-06-24 17:19:23', 'created_at' => '2025-06-24 17:19:23', 'updated_at' => '2025-06-24 17:19:23'],
            ['id' => 8, 'award_category_id' => 8, 'nominee_profile_id' => 8, 'assigned_at' => '2025-06-25 20:01:48', 'created_at' => '2025-06-25 20:01:48', 'updated_at' => '2025-06-25 20:01:48'],
            ['id' => 9, 'award_category_id' => 9, 'nominee_profile_id' => 9, 'assigned_at' => '2025-06-27 11:35:51', 'created_at' => '2025-06-27 11:35:51', 'updated_at' => '2025-06-27 11:35:51'],
            ['id' => 10, 'award_category_id' => 4, 'nominee_profile_id' => 10, 'assigned_at' => '2025-06-27 13:48:22', 'created_at' => '2025-06-27 13:48:22', 'updated_at' => '2025-06-27 13:48:22'],
            ['id' => 11, 'award_category_id' => 8, 'nominee_profile_id' => 11, 'assigned_at' => '2025-06-29 03:19:08', 'created_at' => '2025-06-29 03:19:08', 'updated_at' => '2025-06-29 03:19:08'],
        ];

        foreach ($relationships as $relationship) {
            DB::table('award_category_nominee_profile')->insert($relationship);
        }
    }

    private function importVotes()
    {
        $this->command->line('🗳️  Importing votes...');
        // Import votes data if available in the SQL file
    }

    private function importPayments()
    {
        $this->command->line('💳 Importing payments...');
        // Import payments data if available in the SQL file
    }
} 