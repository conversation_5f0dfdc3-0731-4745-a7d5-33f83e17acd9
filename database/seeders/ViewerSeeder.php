<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class ViewerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test viewer user
        $viewer = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Test Viewer',
            'password' => Hash::make('password'),
            'role' => User::ROLE_VIEWER,
            'slug' => 'test-viewer',
            'phone' => '**********',
            'email_verified_at' => now(),
            'bio' => 'A test viewer account with read-only access to the system.',
        ]);

        $this->command->info('Viewer user created:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password');
        $this->command->info('Role: viewer');
    }
}
