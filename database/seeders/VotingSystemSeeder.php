<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\NomineeProfile;
use App\Models\Payment;
use App\Models\Vote;
use Carbon\Carbon;

class VotingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin (only if not exists)
        $superAdmin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Super Admin',
            'password' => Hash::make('password'),
            'role' => User::ROLE_SUPER_ADMIN,
            'slug' => 'super-admin',
            'phone' => '0257853094',
            'email_verified_at' => now(),
        ]);

        // Create Organizations
        $techOrg = Organization::firstOrCreate([
            'slug' => 'uenr-css-awards'
        ], [
            'name' => 'UENR CSS Awards',
            'description' => 'Celebrating excellence in technology and innovation.',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '0201234567',
            'is_active' => true,
        ]);
        // Create Organization Admins
        $techAdmin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'UENR CSS',
            'password' => Hash::make('J#O3832ho_#'),
            'role' => User::ROLE_ADMIN,
            'organization_id' => $techOrg->id,
            'slug' => 'uenr-css',
            'email_verified_at' => now(),
        ]);

        // Create Award Categories
        $techCategories = [
            [
                'name' => 'Executive of the Year',
                'description' => 'Honoring outstanding leadership and executive excellence in the tech community.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Most Popular Student of the Year',
                'description' => 'Recognizing the most well-known and influential student in the academic community.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Most Influential Student of the Year',
                'description' => 'Celebrating students who have made significant impact on campus and beyond.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Face of CSS (male)',
                'description' => 'Recognizing the male student who best represents the Computer Science Society.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Face of CSS (female)',
                'description' => 'Recognizing the female student who best represents the Computer Science Society.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Programmer of the Year',
                'description' => 'Honoring exceptional programming talent and achievements.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Designer of the Year',
                'description' => 'Celebrating outstanding design talent and creativity.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Gentleman of the Year',
                'description' => 'Recognizing male students exemplifying excellent character and conduct.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Most Fashionable Student of the Year (male)',
                'description' => 'Celebrating male students with outstanding style and fashion sense.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Most Fashionable Student of the Year (female)',
                'description' => 'Celebrating female students with outstanding style and fashion sense.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Course Rep of the Year',
                'description' => 'Honoring exceptional service and leadership as a course representative.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Entrepreneur of the Year',
                'description' => 'Recognizing outstanding student entrepreneurship and business acumen.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Content Creator of the Year',
                'description' => 'Celebrating exceptional digital content creation and social media influence.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Best Pal',
                'description' => 'Recognizing students known for their exceptional friendship and camaraderie.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Fresher of the Year',
                'description' => 'Celebrating outstanding first-year students who have made a significant impact.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Sports Personality of the Year',
                'description' => 'Honoring excellence in sports and athletic achievements.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Class of the Year',
                'description' => 'Recognizing the most outstanding academic class or cohort.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Photogenic',
                'description' => 'Celebrating students with exceptional camera presence and photogenic qualities.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Student Politician of the Year',
                'description' => 'Honoring excellence in student politics and campus leadership.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
            [
                'name' => 'Most Popular Fresher of the Year',
                'description' => 'Recognizing the most well-known and popular first-year student.',
                'price_per_vote' => 1.00,
                'discount_percentage' => 0.00,
                'discount_min_votes' => 0,
            ],
        ];

        // Create categories for the tech organization
        $createdCategories = [];
        foreach ($techCategories as $categoryData) {
            $category = AwardCategory::firstOrCreate([
                'name' => $categoryData['name'],
                'organization_id' => $techOrg->id,
            ], array_merge($categoryData, [
                'organization_id' => $techOrg->id,
                'slug' => Str::slug($categoryData['name']),
                'is_active' => true,
                'voting_start_date' => now()->subDays(30), // Started 30 days ago
                'voting_end_date' => now()->addMonths(2), // Ends in 2 months
            ]));
            $createdCategories[] = $category;
        }

        // Sample nominee names for creating diverse profiles
        $nomineeNames = [
            'Kwame Asante', 'Ama Osei', 'Kofi Mensah', 'Akosua Boateng', 'Yaw Oppong',
            'Efua Adomako', 'Kweku Antwi', 'Abena Sarpong', 'Kojo Owusu', 'Adjoa Agyeman',
            'Nana Okyere', 'Akwasi Boadu', 'Maame Yaa', 'Kwadwo Frimpong', 'Esi Asiedu',
            'Samuel Appiah', 'Princess Amoah', 'Richard Gyimah', 'Grace Addai', 'Michael Nkrumah',
            'Elizabeth Ansah', 'Francis Darkwa', 'Joyce Amankwah', 'Benjamin Osei', 'Margaret Asante',
            'Daniel Obeng', 'Victoria Adu', 'Isaac Gyamfi', 'Rita Akyeampong', 'Emmanuel Darko',
            'Sarah Boateng', 'Collins Mensah', 'Priscilla Ofori', 'Stephen Asamoah', 'Hannah Okyere',
            'Patrick Acheampong', 'Felicia Owusu', 'Joseph Antwi', 'Linda Asiedu', 'David Boakye'
        ];

        // Create sample nominees for each category
        foreach ($createdCategories as $category) {
            // Create 3-6 nominees per category
            $nomineeCount = rand(3, 6);
            $selectedNames = array_slice($nomineeNames, 0, $nomineeCount);
            
            foreach ($selectedNames as $index => $nomineeName) {
                // Create user for nominee
                $email = strtolower(str_replace(' ', '.', $nomineeName)) . '@student.uenr.edu.gh';
                
                $nomineeUser = User::firstOrCreate([
                    'email' => $email
                ], [
                    'name' => $nomineeName,
                    'password' => Hash::make('password'),
                    'role' => User::ROLE_NOMINEE,
                    'organization_id' => $techOrg->id,
                    'slug' => Str::slug($nomineeName) . '-' . Str::random(4),
                    'phone' => '0' . rand(200000000, 599999999),
                    'approval_status' => 'approved',
                    'is_suspended' => false,
                    'email_verified_at' => now(),
                    'approved_at' => now()->subDays(rand(5, 25)),
                    'approved_by' => $techAdmin->id,
                ]);

                // Get or create nominee profile
                $nomineeProfile = NomineeProfile::firstOrCreate([
                    'user_id' => $nomineeUser->id
                ], [
                    'slug' => $nomineeUser->slug,
                    'nominee_code' => strtoupper(Str::random(6)),
                    'display_name' => $nomineeName,
                    'bio' => "Passionate student dedicated to excellence in " . strtolower($category->name) . ". Committed to making a positive impact in the university community.",
                    'why_vote_for_me' => "I believe in leading by example and inspiring others to achieve their best. My dedication to " . strtolower($category->name) . " and commitment to excellence makes me the ideal candidate.",
                    'total_votes' => 0,
                    'is_approved' => true,
                    'is_suspended' => false,
                ]);

                // Assign nominee to category
                $category->nomineeProfiles()->syncWithoutDetaching([$nomineeProfile->id => [
                    'assigned_at' => now()->subDays(rand(5, 20)),
                    'assigned_by' => $techAdmin->id,
                ]]);

                // Create realistic voting data
                $baseVotes = rand(50, 800); // Base number of votes
                
                // Create multiple payment and vote records to simulate realistic voting patterns
                $voteTransactions = rand(15, 50); // Number of separate voting transactions
                $totalVotes = 0;
                
                for ($i = 0; $i < $voteTransactions; $i++) {
                    $votesInTransaction = rand(1, 10); // 1-10 votes per transaction
                    $voterNames = [
                        'Anonymous', 'John Doe', 'Jane Smith', 'Alex Johnson', 'Maria Garcia',
                        'David Brown', 'Sarah Wilson', 'Mike Davis', 'Emily Chen', 'Chris Lee'
                    ];
                    
                    // Create a payment record
                    $payment = Payment::create([
                        'reference' => 'PAY_' . strtoupper(Str::random(10)),
                        'amount' => $votesInTransaction * $category->price_per_vote,
                        'currency' => 'GHS',
                        'status' => 'success',
                        'payment_method' => rand(0, 1) ? 'paystack' : 'momo',
                        'customer_name' => $voterNames[array_rand($voterNames)],
                        'customer_email' => 'voter' . rand(1000, 9999) . '@example.com',
                        'customer_phone' => '0' . rand(200000000, 599999999),
                        'paid_at' => now()->subDays(rand(1, 25))->subHours(rand(1, 23)),
                        'created_at' => now()->subDays(rand(1, 25))->subHours(rand(1, 23)),
                        'updated_at' => now()->subDays(rand(1, 25))->subHours(rand(1, 23)),
                    ]);

                    // Create vote record
                    Vote::create([
                        'nominee_profile_id' => $nomineeProfile->id,
                        'payment_id' => $payment->id,
                        'voter_name' => $payment->customer_name,
                        'voter_email' => $payment->customer_email,
                        'voter_phone' => $payment->customer_phone,
                        'vote_count' => $votesInTransaction,
                        'amount_paid' => $payment->amount,
                        'discount_applied' => 0,
                        'ip_address' => '192.168.' . rand(1, 255) . '.' . rand(1, 255),
                        'user_agent' => 'VoteYourFav Mobile App',
                        'created_at' => $payment->created_at,
                        'updated_at' => $payment->updated_at,
                    ]);

                    $totalVotes += $votesInTransaction;
                }

                // Update nominee profile with total votes
                $nomineeProfile->update(['total_votes' => $totalVotes]);
            }

            // Shuffle the names for next category to avoid same names in same positions
            shuffle($nomineeNames);
        }

        $this->command->info('✅ Created ' . count($createdCategories) . ' categories with nominees and realistic voting data!');
    }
}