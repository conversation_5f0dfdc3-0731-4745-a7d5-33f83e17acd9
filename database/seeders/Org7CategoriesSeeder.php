<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AwardCategory;
use Carbon\Carbon;

class Org7CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            'Most popular student (male)',
            'Most popular student (female)',
            'Content Creator',
            'Best student dancer of the year',
            'Artiste of the year',
            'Photographer of the year',
            'Student MC of the year',
            'Entrepreneur of the year',
            'Outstanding graphic designer',
            'Tiktoker of the year',
            'Most influential student of the year',
            'Face of ITS',
            'Student activists of the year',
            'Student blogger of the year',
            'Best sports personality of the year',
            'Best Coach of the year',
            'Best student in politics',
            'Best course rep of the year (male)',
            'Best course rep of the year (female)',
            'Best ITS Appointees',
            'Best programmer of the year',
            'Class football team of the year'
        ];

        $this->command->info('Seeding categories for Organization ID: 7');
        $bar = $this->command->getOutput()->createProgressBar(count($categories));

        foreach ($categories as $categoryName) {
            AwardCategory::create([
                'name' => $categoryName,
                'description' => "Award category for the {$categoryName}.",
                'organization_id' => 7,
                'price_per_vote' => 1.00,
                'discount_percentage' => 0,
                'discount_min_votes' => 0,
                'voting_start_date' => Carbon::today(),
                'voting_end_date' => Carbon::createFromDate(null, 8, 29), // August 29th of current year
                'is_active' => true,
            ]);
            $bar->advance();
        }

        $bar->finish();
        $this->command->info("\nSeeding completed successfully.");
    }
}
