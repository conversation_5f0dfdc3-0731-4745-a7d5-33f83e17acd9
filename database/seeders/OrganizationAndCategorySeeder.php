<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Organization;
use App\Models\AwardCategory;
use App\Models\User;
use Illuminate\Support\Str;

class OrganizationAndCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if none exists
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
                'role' => User::ROLE_SUPER_ADMIN,
                'slug' => Str::slug('Admin User' . '-' . Str::random(6)),
                'approval_status' => 'approved',
            ]
        );

        // Create sample organizations
        $organizations = [
            [
                'name' => 'Ghana Music Awards',
                'description' => 'Annual music awards celebrating Ghanaian artists',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+233 20 123 4567',
                'website' => 'https://ghanamusicawards.com',
                'is_active' => true,
            ],
            [
                'name' => 'Ghana Movie Awards',
                'description' => 'Celebrating excellence in Ghanaian cinema',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+233 20 987 6543',
                'website' => 'https://ghanamovieawards.com',
                'is_active' => true,
            ],
            [
                'name' => 'Ghana Fashion Awards',
                'description' => 'Recognizing talent in the fashion industry',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+233 20 456 7890',
                'website' => 'https://ghanafashionawards.com',
                'is_active' => true,
            ],
        ];

        foreach ($organizations as $orgData) {
            $organization = Organization::firstOrCreate(
                ['name' => $orgData['name']],
                [
                    'slug' => Str::slug($orgData['name']),
                    'description' => $orgData['description'],
                    'contact_email' => $orgData['contact_email'],
                    'contact_phone' => $orgData['contact_phone'],
                    'website' => $orgData['website'],
                    'is_active' => $orgData['is_active'],
                ]
            );

            // Create categories for each organization
            if ($organization->name === 'Ghana Music Awards') {
                $categories = [
                    'Artist of the Year',
                    'Best Male Vocalist',
                    'Best Female Vocalist',
                    'Best New Artist',
                    'Song of the Year',
                ];
            } elseif ($organization->name === 'Ghana Movie Awards') {
                $categories = [
                    'Best Actor',
                    'Best Actress',
                    'Best Director',
                    'Best Film',
                    'Best Supporting Actor',
                ];
            } else {
                $categories = [
                    'Designer of the Year',
                    'Model of the Year',
                    'Best Fashion Brand',
                    'Best Accessory Designer',
                    'Best Emerging Talent',
                ];
            }

            foreach ($categories as $categoryName) {
                AwardCategory::firstOrCreate(
                    [
                        'name' => $categoryName,
                        'organization_id' => $organization->id,
                    ],
                    [
                        'slug' => Str::slug($categoryName . '-' . $organization->name),
                        'description' => "Nominees for {$categoryName} in {$organization->name}",
                        'voting_start_date' => now(),
                        'voting_end_date' => now()->addMonths(3),
                        'price_per_vote' => 5.00,
                        'is_active' => true,
                    ]
                );
            }
        }
    }
}
