<?php

namespace Database\Factories;

use App\Models\NomineeProfile;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NomineeProfile>
 */
class NomineeProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = NomineeProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $displayName = $this->faker->name();
        
        return [
            'user_id' => User::factory(),
            'display_name' => $displayName,
            'slug' => null, // Will be auto-generated
            'nominee_code' => null, // Will be auto-generated
            'bio' => $this->faker->paragraph(3),
            'profile_image' => null,
            'additional_images' => [],
            'social_links' => [
                'facebook' => $this->faker->optional()->url(),
                'twitter' => $this->faker->optional()->url(),
                'instagram' => $this->faker->optional()->url(),
                'linkedin' => $this->faker->optional()->url(),
            ],
            'achievements' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
            'why_vote_for_me' => $this->faker->paragraph(2),
            'total_votes' => 0,
            'link_clicks' => 0,
            'is_approved' => true,
            'is_suspended' => false,
            'suspension_reason' => null,
            'suspension_notice' => null,
            'admin_notes' => null,
        ];
    }

    /**
     * Indicate that the profile is not approved.
     */
    public function notApproved(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_approved' => false,
        ]);
    }

    /**
     * Indicate that the profile is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspended' => true,
            'suspension_reason' => $this->faker->sentence(),
            'suspension_notice' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Set specific vote count.
     */
    public function withVotes(int $count): static
    {
        return $this->state(fn (array $attributes) => [
            'total_votes' => $count,
        ]);
    }

    /**
     * Set specific nominee code.
     */
    public function withCode(string $code): static
    {
        return $this->state(fn (array $attributes) => [
            'nominee_code' => $code,
        ]);
    }
}
