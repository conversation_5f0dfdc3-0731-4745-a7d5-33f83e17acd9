<?php

namespace Database\Factories;

use App\Models\AwardCategory;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AwardCategory>
 */
class AwardCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AwardCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true) . ' Award';
        
        return [
            'name' => $name,
            'slug' => Str::slug($name) . '-' . $this->faker->unique()->randomNumber(4),
            'description' => $this->faker->paragraph(),
            'organization_id' => Organization::factory(),
            'voting_start_date' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'voting_end_date' => $this->faker->dateTimeBetween('+1 week', '+2 months'),
            'price_per_vote' => $this->faker->randomFloat(2, 1.00, 5.00),
            'discount_percentage' => $this->faker->randomFloat(2, 5.00, 20.00),
            'discount_min_votes' => $this->faker->numberBetween(5, 20),
            'is_active' => true,
            'is_suspended' => false,
            'suspension_reason' => null,
            'suspension_notice' => null,
        ];
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the category is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspended' => true,
            'suspension_reason' => $this->faker->sentence(),
            'suspension_notice' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Set voting as not started yet.
     */
    public function notStarted(): static
    {
        return $this->state(fn (array $attributes) => [
            'voting_start_date' => $this->faker->dateTimeBetween('+1 day', '+1 week'),
            'voting_end_date' => $this->faker->dateTimeBetween('+2 weeks', '+2 months'),
        ]);
    }

    /**
     * Set voting as ended.
     */
    public function ended(): static
    {
        return $this->state(fn (array $attributes) => [
            'voting_start_date' => $this->faker->dateTimeBetween('-2 months', '-1 month'),
            'voting_end_date' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
        ]);
    }
}
